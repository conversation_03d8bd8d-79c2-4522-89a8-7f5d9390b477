﻿using Multipay.Queue.EnvioWebhookCobranca.Infra.Data;

namespace Multipay.Queue.Webhook.Infrastructure.Services
{
    public interface IConnectMongoService
    {
        bool Verify();
    }

    internal class ConnectMongoService : IConnectMongoService
    {
        private readonly AnspaceMongoContext context;

        public ConnectMongoService(AnspaceMongoContext context)
        {
            this.context = context;
        }

        public bool Verify()
        {
            try
            {
                return context.CheckMongoDBConnection();
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}