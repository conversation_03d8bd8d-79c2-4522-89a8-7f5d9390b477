﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    public interface IContaBancariaRepository
    {
        Task<int?> ObterIdClientePorDadosConta(int idBanco, string? agencia, string? conta, string? @digitoVerificadorConta);

        Task<ContaBancariaModel?> ObterIdContaIdClientePorDadosConta(int idBanco, string? agencia, string? conta, string? @digitoVerificadorConta);
    }

    internal class ContaBancariaRepository : IContaBancariaRepository
    {
        private readonly ILogger logger;
        private readonly IDatabase database;

        public ContaBancariaRepository(
            ILogger logger,
            IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public async Task<int?> ObterIdClientePorDadosConta(int idBanco, string? agencia, string? conta, string? digitoVerificadorConta)
        {
            try
            {
                using var reader = await database.QueryProcAsync<dynamic>("Painel.Select_ContaBancariaPorDadosConta", new
                {
                    idBanco,
                    @digitoVerificadorConta,
                    agencia,
                    conta
                });

                if (await reader.ReadAsync())
                {
                    return reader.Poco.IdCliente;
                }
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(ContaBancariaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<ContaBancariaModel?> ObterIdContaIdClientePorDadosConta(int idBanco, string? agencia, string? conta, string? digitoVerificadorConta)
        {
            try
            {
                using var reader = await database.QueryProcAsync<dynamic>("Painel.Select_ContaBancariaPorDadosConta", new
                {
                    idBanco,
                    @digitoVerificadorConta,
                    agencia,
                    conta
                });

                if (await reader.ReadAsync())
                {
                    return new ContaBancariaModel() { Id = reader.Poco.Id, IdCliente = reader.Poco.IdCliente };
                }
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(ContaBancariaRepository), database.LastCommand);
                throw;
            }
        }
    }
}