﻿using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels
{
    [BsonIgnoreExtraElements]
    public class WebhookFornecedorCobrancaModel : BaseModel
    {
        public WebhookFornecedorCobrancaModel(int idCobranca, string idWebhookFornecedor, string acao)
        {
            IdCobranca = idCobranca;
            IdWebhookFornecedor = idWebhookFornecedor;
            Acao = acao;
            DataCriacaoUtc = DateTime.UtcNow;
        }

        public int IdCobranca { get; private set; }
        public string IdWebhookFornecedor { get; set; }
        public string Acao { get; private set; }
        public DateTime DataCriacaoUtc { get; private set; }
        public DateTime? DataAtualizacaoUtc { get; set; }
    }
}