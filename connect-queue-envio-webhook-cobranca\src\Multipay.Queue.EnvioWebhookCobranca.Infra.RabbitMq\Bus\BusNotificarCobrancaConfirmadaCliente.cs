﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Settings;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Bus
{
    public interface IBusNotificarCobrancaConfirmadaCliente
    {
        void PublicarNotificacaoCliente(CobrancaConfirmadaNotificarClienteEvent @event);
    }

    internal class BusNotificarCobrancaConfirmadaCliente : EventBusPublishRabbitMQ, IBusNotificarCobrancaConfirmadaCliente
    {
        public BusNotificarCobrancaConfirmadaCliente(
            IRabbitMQPersistentConnection persistentConnection,
            ILogger<BusNotificarCobrancaConfirmadaCliente> logger, IOptions<RabbitMqSettings> options) : base(
                persistentConnection,
                logger,
                options.Value.Setup.EventBusRetryCount,
                options.Value.NotificarCobrancaCliente)
        {
        }

        public void PublicarNotificacaoCliente(CobrancaConfirmadaNotificarClienteEvent @event)
        {
            Publish(@event);
        }
    }
}