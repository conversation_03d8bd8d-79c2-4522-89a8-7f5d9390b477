﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    public interface ITaxaClienteOperacaoRepository
    {
        Task<TaxaClienteOperacaoModel?> ObterTaxaPorClienteOperacao(int idCliente, int idOperacaoServico);
    }

    internal class TaxaClienteOperacaoRepository : ITaxaClienteOperacaoRepository
    {
        private readonly ILogger logger;
        private readonly IDatabase database;

        public TaxaClienteOperacaoRepository(
            ILogger logger,
            IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public async Task<TaxaClienteOperacaoModel?> ObterTaxaPorClienteOperacao(int idCliente, int idOperacaoServico)
        {
            try
            {
                using var reader = await database.QueryProcAsync<TaxaClienteOperacaoModel>("Arquitetura.Select_TaxaClienteOperacaoServico", new
                {
                    idCliente,
                    idOperacaoServico
                });

                if (await reader.ReadAsync())
                {
                    return reader.Poco;
                }

                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(TaxaClienteOperacaoRepository), database.LastCommand);
                throw;
            }
        }
    }
}