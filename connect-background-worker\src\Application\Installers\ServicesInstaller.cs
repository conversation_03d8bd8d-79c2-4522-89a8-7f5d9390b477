using System.Diagnostics.CodeAnalysis;
using System.Text.Encodings.Web;
using System.Text.Json;
using Application.Configurations;
using Application.Services.Implementation;
using Application.Services.Implementation.Authentication;
using Application.Services.Interfaces;
using Application.Services.Interfaces.Api;
using Connect.Cryptography.Service;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Polly;
using Refit;

namespace Application.Installers;

[ExcludeFromCodeCoverage]
public static class ServicesInstaller
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        services
            .AddScoped<IConnectAuthService, ConnectAuthService>()
            .AddScoped<ICredentialService, CredentialService>()
            .AddScoped<IRebalanceService, RebalanceService>()
            .AddScoped<IWalletService, WalletService>()
            .AddScoped<IChargeService, ChargeService>()
            .AddScoped<IBankBalanceService, BankBalanceService>()
            .AddScoped<IBankStatementService, BankStatementService>();

        services
            .AddServicesApis()
            .AddCryptographyService();

        return services;
    }

    public static IServiceCollection AddServicesApis(this IServiceCollection services)
    {
        services
            .AddRefitClient<IConnectTokenApi>(RefitSettings)
            .ConfigureConnectApiClient();

        services
            .AddRefitClient<IConnectApi>(RefitSettings)
            .ConfigureConnectApiClient()
            .AddHttpMessageHandler<ConnectAuthHandler>();
        services
            .AddScoped<ConnectAuthHandler>();

        return services;
    }

    private static IHttpClientBuilder ConfigureConnectApiClient(this IHttpClientBuilder builder) =>
        builder
            .ConfigureHttpClient((provider, client) =>
            {
                var configuration = provider.GetRequiredService<IOptions<ConnectApiConfiguration>>().Value;
                client.BaseAddress = new Uri(configuration.BaseUrl!);
                client.Timeout = TimeSpan.FromSeconds(configuration.TimeoutInSeconds);
            })
            .AddDefaultHttpErrorPolicy();

    private static RefitSettings RefitSettings => new()
    {
        ContentSerializer = new SystemTextJsonContentSerializer(new()
        {
            PropertyNameCaseInsensitive = true,
            Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        })
    };

    private static IHttpClientBuilder AddDefaultHttpErrorPolicy(this IHttpClientBuilder builder) =>
        builder
            .AddTransientHttpErrorPolicy(builder => builder
                .WaitAndRetryAsync([
                    TimeSpan.FromSeconds(1),
                    TimeSpan.FromSeconds(3),
                    TimeSpan.FromSeconds(6)]));
}
