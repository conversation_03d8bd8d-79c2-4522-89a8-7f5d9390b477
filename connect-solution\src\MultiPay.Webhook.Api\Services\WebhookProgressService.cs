using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using MultiPay.Webhook.Api.Hubs;

namespace MultiPay.Webhook.Api.Services
{
    public interface IWebhookProgressService
    {
        Task InitializeProgress(string jobId, int total, string userGroup);
        Task UpdateProgress(string jobId, string? currentItem = null, bool success = true, string? error = null);
        Task CompleteProgress(string jobId);
        Task<WebhookProgressModel?> GetProgress(string jobId);
    }

    public class WebhookProgressService : IWebhookProgressService
    {
        private readonly IHubContext<WebhookProgressHub> _hubContext;
        private readonly IMemoryCache _cache;
        private readonly ILogger<WebhookProgressService> _logger;

        public WebhookProgressService(
            IHubContext<WebhookProgressHub> hubContext,
            IMemoryCache cache,
            ILogger<WebhookProgressService> logger)
        {
            _hubContext = hubContext;
            _cache = cache;
            _logger = logger;
        }

        public async Task InitializeProgress(string jobId, int total, string userGroup)
        {
            var progress = new WebhookProgressModel
            {
                JobId = jobId,
                Total = total,
                Status = "Running"
            };

            _cache.Set($"webhook_progress_{jobId}", progress, TimeSpan.FromHours(1));

            await _hubContext.Clients.Group(userGroup).SendAsync("WebhookProgressUpdate", progress);
            
            _logger.LogInformation("Progresso inicializado para Job {JobId} com {Total} itens", jobId, total);
        }

        public async Task UpdateProgress(string jobId, string? currentItem = null, bool success = true, string? error = null)
        {
            var cacheKey = $"webhook_progress_{jobId}";
            
            if (!_cache.TryGetValue(cacheKey, out WebhookProgressModel? progress) || progress == null)
            {
                _logger.LogWarning("Progresso não encontrado para Job {JobId}", jobId);
                return;
            }

            progress.Processed++;
            progress.CurrentItem = currentItem;

            if (success)
                progress.Success++;
            else
            {
                progress.Failed++;
                if (!string.IsNullOrEmpty(error))
                    progress.Errors.Add(error);
            }

            _cache.Set(cacheKey, progress, TimeSpan.FromHours(1));

            // Enviar update via SignalR para o grupo do usuário
            var userGroup = $"user_{jobId.Split('_')[0]}"; // Assumindo formato "userId_timestamp"
            await _hubContext.Clients.Group(userGroup).SendAsync("WebhookProgressUpdate", progress);

            _logger.LogDebug("Progresso atualizado para Job {JobId}: {Processed}/{Total}", 
                jobId, progress.Processed, progress.Total);
        }

        public async Task CompleteProgress(string jobId)
        {
            var cacheKey = $"webhook_progress_{jobId}";
            
            if (!_cache.TryGetValue(cacheKey, out WebhookProgressModel? progress) || progress == null)
            {
                _logger.LogWarning("Progresso não encontrado para Job {JobId}", jobId);
                return;
            }

            progress.Status = "Completed";
            _cache.Set(cacheKey, progress, TimeSpan.FromHours(1));

            var userGroup = $"user_{jobId.Split('_')[0]}";
            await _hubContext.Clients.Group(userGroup).SendAsync("WebhookProgressUpdate", progress);

            _logger.LogInformation("Progresso completado para Job {JobId}. Sucesso: {Success}, Falhas: {Failed}", 
                jobId, progress.Success, progress.Failed);
        }

        public Task<WebhookProgressModel?> GetProgress(string jobId)
        {
            _cache.TryGetValue($"webhook_progress_{jobId}", out WebhookProgressModel? progress);
            return Task.FromResult(progress);
        }
    }
}
