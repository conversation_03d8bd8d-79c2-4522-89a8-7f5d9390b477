{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq\\Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Core\\Multipay.Queue.EnvioWebhookCobranca.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Core\\Multipay.Queue.EnvioWebhookCobranca.Core.csproj", "projectName": "Multipay.Queue.EnvioWebhookCobranca.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Core\\Multipay.Queue.EnvioWebhookCobranca.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Datadog.Trace": {"target": "Package", "version": "[2.51.0, )"}, "Datadog.Trace.Bundle": {"target": "Package", "version": "[2.51.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Exceptions": {"target": "Package", "version": "[8.4.0, )"}, "Serilog.Exceptions.SqlServer": {"target": "Package", "version": "[8.4.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.Datadog.Logs": {"target": "Package", "version": "[0.5.2, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.Map": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq\\Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq\\Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq.csproj", "projectName": "Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq\\Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Core\\Multipay.Queue.EnvioWebhookCobranca.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Queue.EnvioWebhookCobranca.Core\\Multipay.Queue.EnvioWebhookCobranca.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Multipay.RabbitMQExtension": {"target": "Package", "version": "[1.0.18, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj", "projectName": "Multipay.Service.Criptography", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"AWSSDK.SecretsManager": {"target": "Package", "version": "[3.7.304.6, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}