﻿using Multipay.Queue.Webhook.Core.Interfaces;
using System.Globalization;

namespace Multipay.Queue.Webhook.Application.Models.DelBank
{
    public class CobrancaRecebidaModel : IPIXData
    {
        public int Nsu { get; set; }
        public string? CorrelationId { get; set; }
        public string ReferenceId { get; set; } = null!;
        public string Type { get; set; } = null!;
        public string Source { get; set; } = null!;
        public float Amount { get; set; }
        public DateTime CreatedAt { get; set; }
        public PayerData Payer { get; set; } = null!;
        public ProofData Proof { get; set; } = null!;

        public string Txid
        {
            get
            {
                try
                {
                    return Guid.Parse(CorrelationId!).ToString("N");
                }
                catch (Exception)
                {
                    return ReferenceId ?? string.Empty;
                }
            }
        }

        public string Valor => Amount.ToString(CultureInfo.GetCultureInfo("pt-BR"));

        public string CpfPagador => Proof?.Payer?.Holder?.Document ?? string.Empty;

        public string NomePagador => Proof?.Payer?.Holder?.Name ?? string.Empty;

        public string? IspbPagador => Proof?.Payer?.Participant?.Ispb;
        public string? NomeBancoPagador => Proof?.Payer?.Participant?.Name;
        public string? ContaBancoPagador => Proof?.Payer?.Number;
        public string? AgenciaBancoPagador => Proof.Payer.Branch;
        public string EndToEndId => Proof?.EndToEndId ?? string.Empty;



        public class Balance
        {
            public double balancePrevious { get; set; }
            public double currentBalance { get; set; }
        }
        public class TransactionType
        {
            public string name { get; set; }
            public string description { get; set; }
            public bool isCredit { get; set; }
        }

        public class PayerData
        {
            public string Name { get; set; } = null!;
            public string Document { get; set; } = null!;
        }

        public class ProofData
        {
            public string id { get; set; }
            public int nsu { get; set; }
            public double amount { get; set; }
            public DateTime createdAt { get; set; }
            public string type { get; set; }
            public TransactionType transactionType { get; set; }
            public string modality { get; set; }
            public Balance balance { get; set; }

            public string EndToEndId { get; set; } = null!;
            public string? Description { get; set; }
            public string TransactionId { get; set; } = null!;

            public string Key { get; set; } = null!;
            public float Amount { get; set; }
            public ParticipanteDate Payer { get; set; } = null!;
            public ParticipanteDate Payee { get; set; } = null!;
            public BeneficiaryData Beneficiary { get; set; } = null!;


            public class ParticipanteDate
            {
                public string Number { get; set; } = null!;
                public string Branch { get; set; } = null!;
                public string Type { get; set; } = null!;
                public HolderData Holder { get; set; } = null!;
                public ParticipantData Participant { get; set; } = null!;
            }

            public class BeneficiaryData
            {
                public string Number { get; set; } = null!;
                public string Branch { get; set; } = null!;
                public string Type { get; set; } = null!;
                public HolderData Holder { get; set; } = null!;
                public ParticipantData Participant { get; set; } = null!;
            }

            public class HolderData
            {
                public string? Name { get; set; }
                public string Document { get; set; } = null!;
            }

            public class ParticipantData
            {
                public string Ispb { get; set; } = null!;
                public string? Name { get; set; }
            }
        }
    }
}