﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.BS2.Services;
using Multipay.Queue.Webhook.Application.Models;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaBS2Service : CobrancaRecebidaService<CobrancaRecebidaBS2Model, CobrancaRecebidaBS2Model.PIXData>
    {
        private readonly ILogger<CobrancaRecebidaBS2Service> logger;
        private readonly IEstornarQrCodeService estornarService;

        public CobrancaRecebidaBS2Service(
            ILogger<CobrancaRecebidaBS2Service> logger,
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IEstornarQrCodeService estornarService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.BS2)
        {
            this.logger = logger;
            this.estornarService = estornarService;
        }

        protected override IEnumerable<CobrancaRecebidaBS2Model.PIXData> ListPix => Parameters!.PIX;

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);

        protected override async ValueTask<ResultEstornoBanco> ProcessarDevolucaoBancoAsync(CobrancaModel cobranca)
        {
            try
            {
                var valorPago = ObterValorPago(Parameters!.PIX[0]);
                var txId = cobranca.TransacaoPix!.TXId;
                var endToEndId = ObterEndToEndId(Parameters!.PIX[0]);

                estornarService.SetIdContaBancariaEmpresa(cobranca.TransacaoPix!.IdContaBancariaEmpresa!.Value);
                var rs = await estornarService.SolicitarDevolucao(endToEndId!, txId, valorPago);

                if (!rs.Item1)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Falha na devolucao -> " + rs.Item2);
                    return ResultEstornoBanco.Falha;
                }
                await CobrancaService.AtualizarEndToEndPorTxId(txId, rs.Item2);
                return ResultEstornoBanco.Sucesso;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Falha ao processar devolucao de cobranca: {id}", cobranca.Id);
                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, ex.ToString());
                return ResultEstornoBanco.Falha;
            }
        }
    }
}