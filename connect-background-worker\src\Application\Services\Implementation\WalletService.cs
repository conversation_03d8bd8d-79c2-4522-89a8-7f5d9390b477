using Application.Services.Interfaces;
using Application.Services.Interfaces.Api;
using Application.Services.Models.Responses;
using Domain.Entities;
using Domain.Models;
using Microsoft.Extensions.Logging;
using Refit;
using ResultT;
using static ResultT.Results;

namespace Application.Services.Implementation;

public class WalletService(ILogger<WalletService> logger) : IWalletService
{
    public async Task<Result<WalletBalanceResponse>> GetWalletBalance(CustomerIntegrationEntity customerIntegration, CancellationToken cancellationToken)
    {         
        if (string.IsNullOrEmpty(customerIntegration.BalanceUrl))
        {
            logger.Error("Balance Url não encontrada na configuração da integração do cliente Id: {CustomerId}", customerIntegration.CustomerId);
            return Error();
        }

        if (string.IsNullOrEmpty(customerIntegration.AuthorizationToken))
        {
            logger.Error("Authorization Token não encontrado na configuração da integração do cliente Id: {CustomerId}", customerIntegration.CustomerId);
            return Error();
        }

        var walletApi = RestService.For<IWalletApi>(customerIntegration.BalanceUrl);
        logger.Trace("Obtendo o saldo do cliente Id: {CustomerId} usando a url: {BalanceUrl}", customerIntegration.CustomerId, customerIntegration.BalanceUrl);

        var response = await walletApi.GetBalanceAsync(customerIntegration.AuthorizationToken, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            logger.Error("Erro ao obter o saldo ({Status}): {Response}", response.StatusCode, $"{response.Error?.Content}");
            return Error();
        }
        var balanceResponse = response.Content;

        logger.Trace("Saldo obtido com sucesso. Response: {Response}", response.Content.ToJsonString());
        
        return Ok(balanceResponse);
    }
}
