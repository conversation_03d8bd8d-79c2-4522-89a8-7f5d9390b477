﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Data;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Repositories.MongoRepositories
{
    public interface IWebhookFornecedorCobrancaRepository : IBaseRepository<WebhookFornecedorCobrancaModel>
    { }

    internal class WebhookFornecedorCobrancaRepository : BaseRepository<WebhookFornecedorCobrancaModel>, IWebhookFornecedorCobrancaRepository
    {
        public WebhookFornecedorCobrancaRepository(
            ILogger<WebhookFornecedorCobrancaRepository> logger,
            AnspaceMongoContext context) : base(logger, context.WebhookFornecedorCobranca) { }
    }
}