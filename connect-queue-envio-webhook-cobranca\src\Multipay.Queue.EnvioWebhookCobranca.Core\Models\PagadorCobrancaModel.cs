﻿namespace Multipay.Queue.EnvioWebhookCobranca.Core.Models
{
    public class PagadorCobrancaModel
    {
        public int Id { get; set; }
        
        public int IdCobranca { get; set; }
        
        public string NomePagador { get; private set; } = null!;
        
        public string CPFCNPJPagador { get; private set; } = null!;
        
        public string EmailPagador { get; set; } = null!;
        
        public string TelefonePagador { get; set; } = null!;
        
        public DateTime DataCadastro { get; private set; }
        
        public string? IspbPagador { get; private set; }
        
        public string? NomeBancoPagador { get; private set; }
    }
}