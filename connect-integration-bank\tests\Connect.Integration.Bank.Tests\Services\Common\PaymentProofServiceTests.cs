using Connect.Integration.Bank.Services.Common.Models.Requests;
using Connect.Integration.Bank.Strategy.Interfaces;
using Connect.Integration.Bank.Tests.Fixtures;
using FluentAssertions;

namespace Connect.Integration.Bank.Tests.Services.Common;

public class PaymentProofServiceTests : IClassFixture<ServicesFixture>
{
    private readonly IIntegrationBankService _sut;
    private readonly ServicesFixture _servicesFixture;

    public PaymentProofServiceTests(ServicesFixture fixture)
    {
        _servicesFixture = fixture;
        _sut = fixture.IntegrationBankService;
    }

    [Fact]
    public async Task ShouldExecuteDelBankSuccess()
    {
        // Arrange
        var credential = _servicesFixture.CredentialDelBankAccount;
        var endToEndId = "E18236120202501231827s08f8859477";

        var request = new PaymentProofRequest(credential, endToEndId);

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();
        response.Result?.FileStream.Should().NotBeNull();
        response.Result?.ContentType.Should().NotBeNullOrEmpty();

        VerifyFileStream(response.Result!.FileStream!, response.Result!.ContentType!);
    }

    private void VerifyFileStream(Stream fileStream, string contentType)
    {
        var fileExtension = contentType.Substring(contentType.IndexOf("/") + 1);
        
        var fileName = $"{Guid.NewGuid()}.{fileExtension}";
        var filePath = Path.Combine(Path.GetTempPath(), fileName);

        using var file = new FileStream(filePath, FileMode.Create, FileAccess.Write);
        
        fileStream.CopyTo(file);
        file.Close();

        var fileToAssert = File.Open(filePath, FileMode.Open);

        fileToAssert.Should().NotBeNull();
        fileToAssert.Close();

        File.Delete(filePath);
    }
}
