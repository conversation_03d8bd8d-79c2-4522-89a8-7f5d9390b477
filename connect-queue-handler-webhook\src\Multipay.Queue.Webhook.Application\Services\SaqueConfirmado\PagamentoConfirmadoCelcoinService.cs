﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.Saques;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Extensions;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado
{
    public interface IPagamentoConfirmadoCelcoinService : IPagamentoConfirmadoService
    {

    }
    internal class PagamentoConfirmadoCelcoinService : PagamentoConfirmadoBaseService<SolicitacaoSaqueRecebidoCelcoinModel>, IPagamentoConfirmadoCelcoinService
    {
        public PagamentoConfirmadoCelcoinService(
            ILogger<PagamentoConfirmadoCelcoinService> logger,
            IEstornarSaqueService estornarSaqueService,
            ICentralizadorLogsService centralizadorLogsService, IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IBrasilApiService brasilApiService, IUnitOfWork unitOfWork) :
            base(logger, centralizadorLogsService, busNotificarSaqueConfirmado, brasilApiService, estornarSaqueService, unitOfWork)
        {
        }


        protected override string ObterEndToEndId(SolicitacaoSaqueRecebidoCelcoinModel parameters)
        {
            return parameters.Body.EndToEndId;
        }

        protected override string NomeBanco => TipoBanco.Celcoin.GetDescription();

        protected override Status? ObterStatus(SolicitacaoSaqueRecebidoCelcoinModel parameters)
        {
            if(parameters.Status == "CONFIRMED")
                return Status.EFETIVADO;

            if(parameters.Status == "ERROR")
                return Status.REJEITADO;

            return null;
        }
        protected override async Task<ViewSolicitacaoSaqueDadosBasicos?> ObterSolicitacaoSaque(SolicitacaoSaqueRecebidoCelcoinModel parameters)
            => await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(parameters.Body.EndToEndId);
    }
}
