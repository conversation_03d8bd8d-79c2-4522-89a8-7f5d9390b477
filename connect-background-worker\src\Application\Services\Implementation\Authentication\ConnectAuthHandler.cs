using System.Net;
using System.Net.Http.Headers;
using System.Text.Json.Nodes;
using Application.Services.Interfaces;
using Domain.Extensions;
using Microsoft.Extensions.Logging;

namespace Application.Services.Implementation;

public class ConnectAuthHandler(ILogger<ConnectAuthHandler> logger, IConnectAuthService connectAuthService) : DelegatingHandler
{
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var headerAuthorization = request.Headers.GetValues(nameof(Authorization)).FirstOrDefault();
        
        if (headerAuthorization is not null && headerAuthorization.ToString().StartsWith("Bearer"))
        {
            logger.Trace("Header já autenticado");
            return await base.SendAsync(request, cancellationToken);
        }

        var credential = request.Headers.GetAuthorizationCredential();
        
        if (credential is null)
        {
            logger.Error("Header Authorization com credencial não encontrado no request");
            return new HttpResponseMessage { StatusCode = HttpStatusCode.Unauthorized };
        }

        logger.Trace("Obtendo token de acesso a API");

        var tokenResult = await connectAuthService.GetAccessTokenAsync(credential, cancellationToken);
        
        if (!tokenResult.Success)
        {
            logger.Error("Não foi possivel obter o token da API");
            return new HttpResponseMessage { StatusCode = HttpStatusCode.Unauthorized };
        }

        logger.Trace("Token de acesso a API obtido com sucesso");
        
        var accessToken = tokenResult.Value!.AccessToken;

        var digitalSignature = credential.GetDigitalSignature(accessToken);
        await SignRequest(request, digitalSignature);

        request.Headers.Clear();
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        request.Headers.Add("ApplicationToken", credential.ApplicationToken);
        request.Headers.Add("DigitalSignature", digitalSignature);
        request.Headers.Add("X-Force-ConnectPSP", "");

        return await base.SendAsync(request, cancellationToken);
    }

    private async Task SignRequest(HttpRequestMessage request, string digitalSignature)
    {
        var requestBody = await request?.Content?.ReadAsStringAsync();
        
        if (requestBody is not null)
        {
            var requestJson = JsonObject.Parse(requestBody)!;
            
            if (requestJson["digitalSignature"] is not null)
            {
                requestJson["digitalSignature"] = digitalSignature;

                var stringJson = requestJson.ToJsonString();
                request.Content = new StringContent(stringJson, new MediaTypeHeaderValue("application/json"));

                logger.Trace("Assinatura digital gerada para o Request.");
            }
        }
    }
}
