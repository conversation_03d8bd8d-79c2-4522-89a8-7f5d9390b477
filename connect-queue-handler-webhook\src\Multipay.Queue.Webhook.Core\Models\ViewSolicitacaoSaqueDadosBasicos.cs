﻿namespace Multipay.Queue.Webhook.Core.Models
{
    public class ViewSolicitacaoSaqueDadosBasicos
    {
        public int Id { get; set; }
        public string Codigo { get; set; } = null!;
        public string? TipoOperacaoBancaria { get; set; }
        public int? IdFavorecidoCliente { get; set; }
        public string? NomeFavorecido { get; set; }
        public string? CPFCNPJFavorecido { get; set; }
        public int? IdContaBancariaFavorecidoCliente { get; set; }
        public string? AgenciaFavorecido { get; set; }
        public string? ContaFavorecido { get; set; }
        public decimal ValorSolicitado { get; set; }
        public DateTime DataSolicitacao { get; set; }
        public DateTime? DataConclusao { get; set; }
        public DateTime? DataEstorno { get; set; }
        public DateTime? DataPagamento { get; set; }
        public string? UrlConfirmacao { get; set; }
        public string? UrlAtualizacao { get; set; }
        public string? TipoChavePIX { get; set; }
        public string? ChavePIX { get; set; }
        public string? CodigoAutenticacao { get; set; }
        public string? CodigoMovimento { get; set; }
        public string? CodigoTransacao { get; set; }
        public string? RequestIdWebhook { get; set; } = null;
        public string? CustomId { get; set; } = null;
        public string? CustomUserId { get; set; } = null;
        public Guid Guid { get; set; }
    }
}