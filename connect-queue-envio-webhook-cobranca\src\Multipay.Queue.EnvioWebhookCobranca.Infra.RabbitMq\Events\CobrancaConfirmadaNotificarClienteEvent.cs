﻿using Multipay.RabbitMQExtension.Events;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Events
{
    public class CobrancaConfirmadaNotificarClienteEvent : IntegrationEvent
    {
        public CobrancaConfirmadaNotificarClienteEvent(string txId, decimal valorPago, string? idWebhook)
        {
            TxId = txId;
            ValorPago = valorPago;
            IdWebhook = idWebhook;
        }

        public string TxId { get; }
        public decimal ValorPago { get; }
        public string? IdWebhook { get; }
    }
}