﻿using System.Collections.Specialized;

namespace Multipay.Infrastructure.Plugin.Sicoob.Utils
{
    internal static class SicoobApiExtensions
    {
        public static string ToStringApi(this DateTime source)
        {
            return $"{source:yyyy-MM-ddTHH:mm:ssK}";
        }

        public static void Add(this NameValueCollection queryString,
            bool condicao, string key, string? valor)
        {
            if (condicao)
                queryString.Add(key, valor);
        }

        public static void Add(this NameValueCollection queryString,
            string key, DateTime valor)
        {
            queryString.Add(true, key, valor.ToStringApi());
        }

        public static void AddIfNotNull(this NameValueCollection queryString, string key, string? valor)
        {
            queryString.Add(!string.IsNullOrWhiteSpace(valor), key, valor);
        }

        public static void AddIfNotNull(this NameValueCollection queryString, string key, bool? valor)
        {
            queryString.Add(valor != null, key, $"{valor}".ToLowerInvariant());
        }

        public static void AddIfNotNull(this NameValueCollection queryString, string key, int? valor)
        {
            queryString.Add(valor != null, key, $"{valor}");
        }
    }
}