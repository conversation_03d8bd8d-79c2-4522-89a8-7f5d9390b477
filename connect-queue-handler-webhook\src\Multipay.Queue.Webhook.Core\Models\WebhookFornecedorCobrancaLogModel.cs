﻿using Multipay.Queue.Webhook.Core.Interfaces;

namespace Multipay.Queue.Webhook.Core.Models
{
    public class WebhookFornecedorCobrancaLogModel : IBaseLog
    {
        public WebhookFornecedorCobrancaLogModel(string idWebhook, int idCobranca, string mensagem)
        {
            IdWebhook = idWebhook;
            IdCobranca = idCobranca;
            Mensagem = mensagem;
        }

        public string IdWebhook { get; private set; }
        public int IdCobranca { get; private set; }
        public string CodigoCobranca { get; private set; } = string.Empty;
        public string Mensagem { get; private set; }
    }
}