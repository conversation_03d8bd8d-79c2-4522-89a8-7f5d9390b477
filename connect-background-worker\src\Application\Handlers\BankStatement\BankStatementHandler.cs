using Application.Configurations;
using Application.Services.Interfaces;
using Domain.Constants;
using MediatR;
using Microsoft.AspNetCore.Http;
using static Microsoft.AspNetCore.Http.Results;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Infra.Repositories.Implementation;
using Infra.Repositories.Interfaces;
using Domain.Entities;
using Domain.Models;

namespace Application.Handlers.BankStatement;

public class BankStatementHandler
(
    ILogger<BankStatementHandler> logger,
    ICredentialService credentialService,
    IIntegrationRepository integrationRepository,
    IBankStatementService bankStatementService,
    IOptions<ConnectPSPConfiguration> configuration) : IRequestHandler<BankStatementCommand, IResult>
{
    public async Task<IResult> Handle(BankStatementCommand request, CancellationToken cancellationToken)
    {
        var clientsResult = await credentialService.AllGetCredential(cancellationToken);

        var dataDe = DateTime.UtcNow.AddHours(-6);
        var dataAte = DateTime.UtcNow;

        foreach (var client in clientsResult.Value)
        {
            var credential = client;
            //Conta Transacional
            var bankStatementResult = await bankStatementService.ExecuteBankStatementAsync(credential, client.CustomerId,1, dataDe, dataAte, cancellationToken);
            _ = await InsertStatement(logger, integrationRepository, client, bankStatementResult,1, cancellationToken);

            //Conta Proprietaria
            var bankStatementResult = await bankStatementService.ExecuteBankStatementAsync(credential, client.CustomerId,2, dataDe, dataAte, cancellationToken);

            if (bankStatementResult is not null)
            {
                _ = await InsertStatement(logger, integrationRepository, client, bankStatementResult, 2, cancellationToken);
            }

        }

        return Ok();
    }

    private static async Task<bool> InsertStatement(ILogger<BankStatementHandler> logger, IIntegrationRepository integrationRepository, CustomerCredential client, List<Services.Models.Responses.BankStatement> bankStatementResult,int IdTipoContaEmpresa, CancellationToken cancellationToken)
    {

        try
        {
            List<CustomerBankStatement> bankStatement = new List<CustomerBankStatement>();



            foreach (var b in bankStatementResult)
            {
                bankStatement.Add(
                new CustomerBankStatement(
                    client.CustomerId,
                    b.Type.Name.Contains("CREDIT") ? 1 : 2,
                    b.Type.Name + " - " + b.Type.Description,
                    b.Balance.BalancePrevious,
                    b.Amount,
                    (decimal)b.Balance.CurrentBalance,
                    b.CreatedAt,
                    232,
                    IdTipoContaEmpresa,
                    b.Proof?.EndToEndId,
                    b.Proof?.ExternalId,
                    b.Proof?.Id
                )
                );
            }

            logger.Information("Extrato dos clientes executado com sucesso");

            return await integrationRepository.InsertCustomerBankStatement(client.CustomerId, bankStatement, cancellationToken);
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }
}
