<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>f3057358-147e-4449-b8bf-07b39798aeec</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AspNetCore.HealthChecks.MongoDb" Version="8.0.1" />
		<PackageReference Include="AspNetCore.HealthChecks.Rabbitmq" Version="8.0.1" />
		<PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="8.0.2" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
		<PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Multipay.Queue.EnvioWebhookSaque.Application\Multipay.Queue.EnvioWebhookSaque.Application.csproj" />
		<ProjectReference Include="..\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj" />
		<ProjectReference Include="..\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj" />
		<ProjectReference Include="..\Multipay.Queue.EnvioWebhookSaque.Core\Multipay.Queue.EnvioWebhookSaque.Core.csproj" />
		<ProjectReference Include="..\Multipay.Service.Criptography\Multipay.Service.Criptography.csproj" />
	</ItemGroup>

</Project>
