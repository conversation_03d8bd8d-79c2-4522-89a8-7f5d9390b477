﻿using Multipay.Queue.Webhook.Core.Interfaces;
using System.Globalization;

namespace Multipay.Queue.Webhook.Application.Models.DelBank.Indirect
{
    public class CobrancaRecebidaDelBankModel : IPIXData
    {
        public string EndToEndId { get; set; } = null!;
        public string? TransactionId { get; set; }
        public double Amount { get; set; }
        public string InitiationType { get; set; } = null!;
        public ParticipantData Payer { get; set; } = null!;
        public ParticipantData Beneficiary { get; set; } = null!;

        public string Txid => TransactionId!;

        public string Valor => Amount.ToString(CultureInfo.GetCultureInfo("pt-BR"));

        public string CpfPagador => Payer.Holder.Document;

        public string NomePagador => Payer.Holder.Name;
        public string? IspbPagador => Payer.Participant?.Ispb;
        public string? NomeBancoPagador => Payer.Participant?.Name;
        public string? ContaBancoPagador => Payer.Branch;
        public string? AgenciaBancoPagador => Payer.Number;


        public class ParticipantData
        {
            public string Number { get; set; } = null!;
            public string Branch { get; set; } = null!;
            public string Type { get; set; } = null!;
            public ParticipantBank Participant { get; set; } = null!;
            public HolderData Holder { get; set; } = null!;

            public class ParticipantBank
            {
                public string? Name { get; set; }
                public string Ispb { get; set; } = null!;
            }

            public class HolderData
            {
                public string Name { get; set; } = null!;
                public string Document { get; set; } = null!;
                public string Type { get; set; } = null!;
            }
        }
    }
}