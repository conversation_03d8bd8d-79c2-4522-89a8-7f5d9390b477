﻿using Newtonsoft.Json;
using System.Globalization;

namespace Multipay.Infrastructure.Plugin.Sicoob.Utils
{
    public class StringDecimalConverter : JsonConverter
    {
        public override bool CanRead
        { get { return false; } }

        public override bool CanConvert(Type objectType)
        { return objectType == typeof(decimal) || objectType == typeof(decimal?); }

        public override object ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }

        public override void Write<PERSON><PERSON>(JsonWriter writer, object? value, JsonSerializer serializer)
        {
            string? s_value = ((decimal?)value)?.ToString(CultureInfo.InvariantCulture);
            writer.WriteValue($@"{s_value}");
        }
    }
}