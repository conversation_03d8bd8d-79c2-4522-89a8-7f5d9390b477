﻿using Microsoft.Extensions.Configuration;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Setup
{
    public class DataBaseConfigurationSource : IConfigurationSource
    {
        private readonly IUnitOfWork database;
        private readonly string tema;

        public DataBaseConfigurationSource(IUnitOfWork database, string tema)
        {
            this.database = database;
            this.tema = tema;
        }

        public IConfigurationProvider Build(IConfigurationBuilder builder)
        {
            return new DataBaseConfigurationProvider(database, tema);
        }
    }

    public class DataBaseConfigurationProvider : ConfigurationProvider
    {
        private readonly IUnitOfWork unitOfWork;
        private readonly string tema;

        public DataBaseConfigurationProvider(IUnitOfWork unitOfWork, string tema)
        {
            this.unitOfWork = unitOfWork;
            this.tema = tema;
        }

        public override void Load()
        {
            using (unitOfWork)
            {
                static string NormalizeKey(ConstanteModel model)
                {
                    if (string.IsNullOrWhiteSpace(model.Classe))
                        return model.Chave;
                    return $"{model.Classe}:{model.Chave}";
                }

                Data = unitOfWork.ConstantesRepository.ListarConstantesPorTema(tema)
                    .Where(item => tema.Equals(item.NomeTema, StringComparison.InvariantCultureIgnoreCase))
                    .DistinctBy(NormalizeKey)
                    .ToDictionary<ConstanteModel, string, string?>(NormalizeKey, item => item.Valor);
            }
        }
    }
}