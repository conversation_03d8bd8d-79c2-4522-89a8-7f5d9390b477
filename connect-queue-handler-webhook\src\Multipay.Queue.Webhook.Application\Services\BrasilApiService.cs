﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services
{
    public interface IBrasilApiService
    {
        /// <summary>
        /// Consultar dados banco participante pix por ispb
        /// </summary>
        /// <param name="ispb"></param>
        /// <returns></returns>
        Task<BancoParticipantesPixModel?> ConsultarPorIspb(string? ispb);
    }

    internal class BrasilApiService : IBrasilApiService
    {
        private readonly ILogger<BrasilApiService> logger;
        private readonly IHttpClientFactory clientFactory;

        public BrasilApiService(
            ILogger<BrasilApiService> logger,
            IHttpClientFactory clientFactory)
        {
            this.logger = logger;
            this.clientFactory = clientFactory;

            _ = ConsultarBancosParticipantesPixApi();
        }

        public async Task<BancoParticipantesPixModel?> ConsultarPorIspb(string? ispb) =>
            ispb == null ? null : (await ObterBancosParticipantesPix()).FirstOrDefault(item => item.Ispb == ispb);

        private async Task ConsultarBancosParticipantesPixApi()
        {
            try
            {
                var httpClient = clientFactory.CreateClient("BrasilApi");
                var response = await httpClient.GetStringAsync("api/pix/v1/participants");
                bancosParticipantesPix =
                    JsonConvert.DeserializeObject<BancoParticipantesPixModel[]>(response);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Falha ao consultar participants pix");
            }
        }

        private BancoParticipantesPixModel[]? bancosParticipantesPix;

        private async Task<BancoParticipantesPixModel[]> ObterBancosParticipantesPix()
        {
            try
            {
                if (bancosParticipantesPix == null)
                {
                    await ConsultarBancosParticipantesPixApi();
                }
                return bancosParticipantesPix ?? Array.Empty<BancoParticipantesPixModel>();
            }
            catch (Exception)
            {
                return Array.Empty<BancoParticipantesPixModel>();
            }
        }
    }
}