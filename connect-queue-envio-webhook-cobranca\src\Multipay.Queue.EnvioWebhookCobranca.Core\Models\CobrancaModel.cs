﻿namespace Multipay.Queue.EnvioWebhookCobranca.Core.Models
{
    public class CobrancaModel
    {
        public int IdCliente { get; private set; }
        
        public int Id { get; private set; }

        public int IdPessoaCobranca { get; private set; }

        public string Codigo { get; private set; } = string.Empty;
        
        public DateTime? DataPagamento { get; private set; }
        
        public string NumeroFatura { get; private set; } = string.Empty;
        
        public string UsuarioCliente { get; private set; } = string.Empty;

        public QRCodePIXModel? TransacaoPix { get; private set; }
        
        public string UrlConfirmacao { get; private set; } = string.Empty;
        
        public decimal ValorLiquido { get; private set; }
        
        public string? RequestIdWebhook { get; private set; }
        
        public Guid Guid { get; set; }
        
        public string? FormaPagamento { get; set; }
        
        public byte Parcelas { get; set; }
        
        public decimal? ValorPago { get; set; }
        
        public string MoedaCobranca { get; set; } = string.Empty;
        
        public string? MoedaPagamento { get; set; }
        
        public PagadorCobrancaModel? Pagador { get; private set; }

        public void SetarPagador(PagadorCobrancaModel? pagador)
        {
            Pagador = pagador;
        }

        public void SetarTransacao(QRCodePIXModel transacaoPix)
        {
            TransacaoPix = transacaoPix;
        }
    }
}