﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Data;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Repositories.MongoRepositories;

public interface IEnvioWebhookCobrancaRepository : IBaseRepository<EnvioWebhookCobrancaModel>
{
}

internal class EnvioWebhookCobrancaRepository : BaseRepository<EnvioWebhookCobrancaModel>, IEnvioWebhookCobrancaRepository
{
    public EnvioWebhookCobrancaRepository(
        ILogger<WebhookFornecedorCobrancaRepository> logger,
        AnspaceMongoContext context) : base(logger, context.EnvioWebhookCobranca) { }
}