{"version": 2, "dgSpecHash": "hXFT+7m3EAk=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\awssdk.core\\3.7.304.21\\awssdk.core.3.7.304.21.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.secretsmanager\\3.7.304.6\\awssdk.secretsmanager.3.7.304.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.0\\microsoft.bcl.asyncinterfaces.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\2.0.3\\netstandard.library.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.5.2\\system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.2\\system.threading.tasks.extensions.4.5.2.nupkg.sha512"], "logs": []}