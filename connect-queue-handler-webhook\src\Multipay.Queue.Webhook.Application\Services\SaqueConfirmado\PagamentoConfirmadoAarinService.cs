﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.Saques;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Extensions;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado
{
    public interface IPagamentoConfirmadoAarinService : IPagamentoConfirmadoService
    {

    }
    internal class PagamentoConfirmadoAarinService : PagamentoConfirmadoBaseService<SolicitacaoSaqueRecebidoAarinModel>, IPagamentoConfirmadoAarinService
    {
        public PagamentoConfirmadoAarinService(
            ILogger<PagamentoConfirmadoAarinService> logger,
            IEstornarSaqueService estornarSaqueService,
            ICentralizadorLogsService centralizadorLogsService, IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IBrasilApiService brasilApiService, IUnitOfWork unitOfWork) :
            base(logger, centralizadorLogsService, busNotificarSaqueConfirmado, brasilApiService, estornarSaqueService, unitOfWork)
        {
        }


        protected override string ObterEndToEndId(SolicitacaoSaqueRecebidoAarinModel parameters)
        {
            return parameters.EndToEndId;
        }

        protected override string NomeBanco => TipoBanco.BancoAarin.GetDescription();

        protected override Status? ObterStatus(SolicitacaoSaqueRecebidoAarinModel parameters)
        {
            if(parameters.Status == "Completed")
                return parameters.Type == "Debit" ? Status.EFETIVADO : Status.DEVOLVIDO;

            if(parameters.Status == "Rejected")
                return Status.REJEITADO;

            return null;
        }
        protected override async Task<ViewSolicitacaoSaqueDadosBasicos?> ObterSolicitacaoSaque(SolicitacaoSaqueRecebidoAarinModel parameters)
            => await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(parameters.TransactionId);
    }
}
