﻿using Multipay.Queue.Webhook.Application.Models.Saques;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Core.Extensions;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services
{
    public interface IValidacaoChaveRecebidoBS2Service
    {
        Task<bool> Processar(WebhookRecebidoEvent @event);
    }

    public class ValidacaoChaveRecebidoBS2Service : IValidacaoChaveRecebidoBS2Service
    {
        private readonly ICentralizadorLogsService centralizadorLogsService;
        private readonly IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado;
        private readonly IBusSolicitacaoSaques busSolicitacaoSaques;
        private readonly IUnitOfWork unitOfWork;

        public ValidacaoChaveRecebidoBS2Service(
            ICentralizadorLogsService centralizadorLogsService,
            IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado,
            IBusSolicitacaoSaques busSolicitacaoSaques,
            IUnitOfWork unitOfWork)
        {
            this.centralizadorLogsService = centralizadorLogsService;
            this.busNotificarSaqueConfirmado = busNotificarSaqueConfirmado;
            this.busSolicitacaoSaques = busSolicitacaoSaques;
            this.unitOfWork = unitOfWork;
        }

        public async Task<bool> Processar(WebhookRecebidoEvent @event)
        {
            var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<ValidacaoChavePIXBS2RequestModel>($"{@event.Data}");

            if (parameters?.Chave == null)
                return false;

            var existeChave = await unitOfWork.ChavePIXContaBancariaFavorecidoClienteRepository.ExistePorIdValidacao($"{parameters.Id}");

            if (!existeChave)
            {
                centralizadorLogsService.AdicionarLogWebhookFornecedor("BANCO BS2", "VALIDACHAVE", parameters.ToJson(), "Consumer-webhook");

                return true;
            }

            await unitOfWork.ChavePIXContaBancariaFavorecidoClienteRepository.MarcarRespostaChave(parameters.Chave, parameters.Valida, $"BANCO {TipoBanco.BS2.GetDescription()}");

            var saques = await unitOfWork.SolicitacaoSaqueRepository.SolicitacaoSaquePorChavePixValidada(parameters.Chave);

            foreach (var saque in saques)
            {
                centralizadorLogsService.AdicionarLogSolicitacaoSaque(saque.Id, "Webhook de validação recebido.");

                if (saque.DataConclusao.HasValue || saque.DataPagamento.HasValue || saque.DataEstorno.HasValue || !string.IsNullOrEmpty(saque.CodigoMovimento) || !string.IsNullOrEmpty(saque.CodigoTransacao))
                {
                    // Aidionanco Log
                    centralizadorLogsService.AdicionarLogSolicitacaoSaque(saque.Id, "Saque ja concluido anteriormente.");
                    continue;
                }

                if (parameters.Valida)
                {
                    // Aidionanco Log
                    centralizadorLogsService.AdicionarLogSolicitacaoSaque(saque.Id, "Chave válida.");

                    if (!saque.IdFavorecidoCliente.HasValue)
                    {
                        await EstornarSaque(@event.IdWebhook, saque, "Favorecido do saque não preenchido");
                        continue;
                    }

                    if (!saque.IdContaBancariaFavorecidoCliente.HasValue)
                    {
                        await EstornarSaque(@event.IdWebhook, saque, "Conta bancaria ou chave pix do favorecido do saque não preenchido");
                        continue;
                    }

                    var conta = await unitOfWork.ContaBancariaFavorecidoClienteRepository.ObterContaBancariaFavorecidoClientePorId(saque.IdFavorecidoCliente.Value, saque.IdContaBancariaFavorecidoCliente.Value);

                    if (conta == null)
                    {
                        await EstornarSaque(@event.IdWebhook, saque, "Conta bancaria ou chave pix do favorecido do saque não preenchido");
                        continue;
                    }

                    busSolicitacaoSaques.PublicarSolicitacaoSaquesValidada(saque.Codigo, saque.Id, saque.ValorSolicitado, conta.IdTipoChavePIX, conta.ChavePIX, conta.CPFCNPJFavorecido, conta.NomeFavorecido);
                }
                else
                {
                    centralizadorLogsService.AdicionarLogSolicitacaoSaque(saque.Id, "Chave INVÁLIDA.");

                    await EstornarSaque(@event.IdWebhook, saque, "CHAVE INVÁLIDA NO BACEN");
                }
            }
            return true;
        }

        private async Task EstornarSaque(string idWebhook, ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string mensagem)
        {
            if (solicitacaoSaque.DataEstorno == null)
                await unitOfWork.SolicitacaoSaqueRepository.EstornarSaque(solicitacaoSaque.Id, mensagem);

            busNotificarSaqueConfirmado.PublicarNotificacaoCliente(solicitacaoSaque.Id, idWebhook, false, "RETURNED TRANSACTION", "02");
        }
    }
}