﻿namespace Multipay.Queue.Webhook.Core.Entities;
public class WebhookRefundPixEntity
{
    public Guid Id { get; set; }
    public long WebhookFornecedorId { get; set; }
    public DateTime ReceiveDate { get; set; }
    public string? Action { get; set; }

    public WebhookRefundPixEntity(Guid id, long webhookFornecedorId, DateTime receiveDate, string? action)
    {
        Id = id;
        WebhookFornecedorId = webhookFornecedorId;
        ReceiveDate = receiveDate;
        Action = action;
    }
}
