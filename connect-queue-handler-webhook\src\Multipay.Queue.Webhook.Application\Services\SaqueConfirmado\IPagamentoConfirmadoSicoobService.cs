﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Sicoob.Services;
using Multipay.Queue.Webhook.Application.Models.Saques;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Core.Extensions;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;

public interface IPagamentoConfirmadoSicoobService : IPagamentoConfirmadoService
{
}

internal class PagamentoConfirmadoSicoobService : PagamentoConfirmadoBaseService<SolicitacaoSaqueRecebidoSicoobModel>,
    IPagamentoConfirmadoSicoobService
{
    private readonly IPagamentoPixService pagamentoPixService;

    public PagamentoConfirmadoSicoobService(
        ILogger<PagamentoConfirmadoSicoobService> logger,
        IPagamentoPixService pagamentoPixService,
        ICentralizadorLogsService centralizadorLogsService,
        IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado,
        IEstornarSaqueService estornarSaqueService,
        IBrasilApiService brasilApiService,
        IUnitOfWork unitOfWork) : base(logger, centralizadorLogsService, busNotificarSaqueConfirmado, brasilApiService, estornarSaqueService, unitOfWork)
    {
        this.pagamentoPixService = pagamentoPixService;
    }

    protected override string NomeBanco => TipoBanco.SICOOB.GetDescription();

    protected override string ObterEndToEndId(SolicitacaoSaqueRecebidoSicoobModel parameters)
    {
        return parameters.Id;
    }

    protected override Status? ObterStatus(SolicitacaoSaqueRecebidoSicoobModel parameters)
    {
        string estado = parameters.Estado;
        if (estado.Contains("FINALIZADO"))
            return Status.EFETIVADO;
        if (estado.Contains("REJEITADO"))
            return Status.REJEITADO;

        return null;
    }

    protected override async Task ProcessarPagamentoEfetivado(SolicitacaoSaqueRecebidoSicoobModel parameters, string idWebhook,
        ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string endToEndId)
    {
        await base.ProcessarPagamentoEfetivado(parameters, idWebhook, solicitacaoSaque, endToEndId);

        var pagamento = await pagamentoPixService.ConsultarPagamentoPix(endToEndId, CancellationToken.None);

        if (pagamento?.Destino is null)
        {
            base.CentralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id,
                $"Não foi possível obter dados do recebedor da solicitação de saques. endToEndId: {endToEndId}. {pagamento?.ToJson()}");
            return;
        }

        await InserirRecebedorSolicitacaoSaque(solicitacaoSaque.Id,
            pagamento.Destino.Ispb,
            pagamento.Destino.CpfCnpj,
            pagamento.Destino.Nome,
            pagamento.Destino.Nome,
            pagamento.Destino.Agencia,
            pagamento.Destino.Conta,
            pagamento.Destino.Tipo);
    }
}