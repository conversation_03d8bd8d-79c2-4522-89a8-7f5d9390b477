﻿using Multipay.Queue.Webhook.Core.Interfaces;

namespace Multipay.Queue.Webhook.Core.Models
{
    public class CobrancaLogModel : IBaseLog
    {
        public CobrancaLogModel(string idWebhook, int idCobranca, string codigoCobranca, string descricao)
        {
            IdWebhook = idWebhook;
            IdCobranca = idCobranca;
            CodigoCobranca = codigoCobranca;
            Mensagem = descricao;
        }

        public string IdWebhook { get; private set; }
        public int IdCobranca { get; private set; }
        public string CodigoCobranca { get; private set; }
        public string Mensagem { get; private set; }
    }
}