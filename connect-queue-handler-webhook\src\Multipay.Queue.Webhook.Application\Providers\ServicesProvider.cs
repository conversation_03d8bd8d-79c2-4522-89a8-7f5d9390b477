﻿using Microsoft.Extensions.DependencyInjection;

namespace Multipay.Queue.Webhook.Application.Providers
{
    public interface IServicesProvider<TInterface, TKey>
    {
        TInterface GetInstance(IServiceScope scope, TKey key);

        IServiceScope CreateScope();
    }

    internal class ServicesProvider<TInterface, TKey> : IServicesProvider<TInterface, TKey>
    {
        private readonly IServiceProvider serviceProvider;

        public ServicesProvider(IServiceProvider serviceProvider)
        {
            this.serviceProvider = serviceProvider;
        }

        public IServiceScope CreateScope() => serviceProvider.CreateScope();

        public TInterface GetInstance(IServiceScope scope, TKey key)
        {
            var func = (Func<TKey, TInterface>)scope.ServiceProvider.GetRequiredService(typeof(Func<TKey, TInterface>));
            return func(key);
        }
    }
}