using Application.Services.Interfaces;
using Application.Services.Interfaces.Api;
using Application.Services.Models.Requests;
using Domain.Extensions;
using Domain.Models;
using Microsoft.Extensions.Logging;
using ResultT;
using static ResultT.Results;

namespace Application.Services.Implementation;

public class BankBalanceService(ILogger<BankBalanceService> logger, IConnectApi connectApi) : IBankBalanceService
{
    public async Task<Result> ExecuteBankBalanceAsync(CustomerCredential credential, CancellationToken cancellationToken)
    {
        var authorization = credential.GetAuthorization();
        var request = new BankBalanceRequest();

        var response = await connectApi.GetBankBalanceAsync(authorization, request, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            logger.Error("Erro ao executar bank balance service: ({Status}): {Response}", response.StatusCode, $"{response.Error?.Content}");
            return Error();
        }

        return Ok();
    }
}
