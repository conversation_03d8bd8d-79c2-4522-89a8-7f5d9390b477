﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Aarin.Abstractions;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Service.Criptography;
using Newtonsoft.Json.Linq;
using System.Net.Http.Headers;
using System.Net.Http.Json;

namespace Multipay.Infrastructure.Plugin.Aarin.Services
{
    internal class EstornarQrCodeService : IEstornarQrCodeService
    {
        private readonly ILogger<EstornarQrCodeService> logger;
        private readonly IHttpClientFactory httpClientFactory;
        private readonly IAuthService authService;

        public EstornarQrCodeService(
            ILogger<EstornarQrCodeService> logger,
            IHttpClientFactory httpClientFactory,
            IAuthService authService)
        {
            this.logger = logger;
            this.httpClientFactory = httpClientFactory;
            this.authService = authService;
        }

        public async Task<(bool, string)> SolicitarDevolucao(ContaBancariaEmpresaModel contaBancariaEmpresa, string endToEndId, decimal valorPago, bool gerarNovoToken, CancellationToken cancellationToken)
        {
            var httpClient = await CreateClient(contaBancariaEmpresa, gerarNovoToken, cancellationToken);

            var response = await httpClient.PutAsJsonAsync($"baas-pix/pix/{endToEndId}/refund/{Guid.NewGuid()}", new
            {
                value = valorPago,
                reason = "MD06"
            }, cancellationToken);

            await LoggerResponse(response);

            try
            {
                var result = await response.Content.ReadAsStringAsync(cancellationToken);
                
                if (result.Contains("e2EId"))
                {
                    JObject jsonData = JObject.Parse(result);
                    string e2EId = jsonData["e2EId"]!.ToString();

                    return (response.IsSuccessStatusCode, e2EId ?? endToEndId);
                }

                return (response.IsSuccessStatusCode, await response.Content.ReadAsStringAsync(cancellationToken));
            }
            catch
            {
                return (false, await response.Content.ReadAsStringAsync(cancellationToken));
            }

        }
        
        private async Task LoggerResponse(HttpResponseMessage response)
        {
            logger.LogInformation("{url}, {request}",
                response.RequestMessage?.RequestUri?.AbsoluteUri ?? "empty",
                response.RequestMessage?.Content != null ? await response.RequestMessage.Content.ReadAsStringAsync() : "empty");

            logger.LogInformation("Status: {status}, {response}",
                response.StatusCode,
                await response.Content.ReadAsStringAsync());
        }
        
        private async ValueTask<HttpClient> CreateClient(ContaBancariaEmpresaModel contaBancariaEmpresa, bool gerarNovoToken, CancellationToken cancellationToken)
        {
            var dc = new CriptoService();
            var client = httpClientFactory.CreateClient(nameof(TipoBanco.BancoAarin));
            var crypto = await dc.Decrypt(contaBancariaEmpresa.CriptoTokenAPI).ConfigureAwait(false);

            client.DefaultRequestHeaders.Add("X-AccountId", contaBancariaEmpresa.IsEncrypted ? await dc.Decrypt(contaBancariaEmpresa.CriptoTokenAPI) : contaBancariaEmpresa.CriptoTokenAPI);
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", await authService.GetAccessToken(contaBancariaEmpresa,true, cancellationToken));

            return client;
        }
    }
}
