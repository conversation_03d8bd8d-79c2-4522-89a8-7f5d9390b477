
﻿using System.Reflection;

namespace Multipay.Queue.Webhook.Application.Models.DelBank
{
    public class PIXDelBankPagamentoRequestModelV1
    {

        public string? EndToEndId { get; set; }
        public string? IdempotencyKey { get; set; }
        public string? ExternalId { get; set; }
        public string? Status { get; set; }
        public ErrorData? Error { get; set; }
        public DateTime? FinishedAt { get; set; }
        public DateTime? TransferAt { get; set; }
        public string? Description { get; set; }
        public Sender? Sender { get; set; }
        public Recipient? Recipient { get; set; }
        public DateTime? TransferredAt { get; set; }
        public class ErrorData
        {
            public string? Code { get; set; }
            public string? Description { get; set; }
        }

        public string? EventType { get; set; }
        public string? OperationType { get; set; }
        public decimal? Amount { get; set; }
        public DateTime? CreatedAt { get; set; }
        public ParticipantData? Payer { get; set; }
        public ParticipantData? Beneficiary { get; set; }
        public ParticipantData? Payee { get; set; }

        public class ParticipantData
        {
            public string? Number { get; set; }
            public string? Branch { get; set; }
            public string? Type { get; set; }
            public ParticipantInfo? Participant { get; set; }
            public HolderData? Holder { get; set; }
        }

        public class ParticipantInfo
        {
            public string? Name { get; set; }
            public string? Ispb { get; set; }
        }

        public class HolderData
        {
            public string? Name { get; set; }
            public string? Document { get; set; }
            public string? Type { get; set; }
        }
    }




    public class PixPagamentoRecebidoModel
    {
        public string EndToEndId { get; set; } = null!;
        public double Amount { get; set; }
        public string Key { get; set; } = null!;
        public string IdempotencyKey { get; set; } = null!;
        public string? InitiationType { get; set; }
        public string? OperationType { get; set; }
        public string? EndToEndIdOriginal { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime FinishedAt { get; set; }
        public string Description { get; set; } = null!;
        //public ParticipantData Payer { get; set; } = null!;
        //public ParticipantData Beneficiary { get; set; } = null!;
        public string TipoBanco { get; set; }
        public string Tipo { get; set; }
        public object ChanelType { get; set; }
        public ProofData Proofs { get; set; }
        public string IdWebhook { get; set; }
        public string Id { get; set; }
        public DateTime CreationDate { get; set; }


        public class Beneficiary
        {
            public string Number { get; set; }
            public string Branch { get; set; }
            public string Type { get; set; }
            public Participant Participant { get; set; }
            public Holder holder { get; set; }
        }

        public class ProofData
        {
            public string EventType { get; set; }
            public string CorrelationId { get; set; }
            public string ReferenceId { get; set; }
            public string EndToEndId { get; set; }
            public double Amount { get; set; }
            public DateTime CreatedAt { get; set; }
            public DateTime PaymentDateBRT { get; set; }
            public Proof Proof { get; set; }
        }

        public class Holder
        {
            public string Name { get; set; }
            public string Document { get; set; }
            public string Type { get; set; }
        }

        public class Participant
        {
            public string Name { get; set; }
            public string Ispb { get; set; }
        }

        public class Payee
        {
            public string Number { get; set; }
            public string Branch { get; set; }
            public string Type { get; set; }
            public Participant Participant { get; set; }
            public Holder Holder { get; set; }
        }

        public class Payer
        {
            public string Number { get; set; }
            public string Branch { get; set; }
            public string Type { get; set; }
            public Participant Participant { get; set; }
            public Holder Holder { get; set; }
        }

        public class Proof
        {
            public string EventType { get; set; }
            public string EndToEndId { get; set; }
            public string IdempotencyKey { get; set; }
            public string CorrelationId { get; set; }
            public string Status { get; set; }
            public double Amount { get; set; }
            public Payer ProofPayer { get; set; }
            public Beneficiary ProofBeneficiary { get; set; }
            public Payee ProofPayee { get; set; }
        }


        public class ParticipantData
        {
            public string Number { get; set; } = null!;
            public string Branch { get; set; } = null!;
            public string Type { get; set; } = null!;
            public ParticipantBank Participant { get; set; } = null!;
            public HolderData Holder { get; set; } = null!;

            public class ParticipantBank
            {
                public string? Name { get; set; }
                public string Ispb { get; set; } = null!;
            }

            public class HolderData
            {
                public string Name { get; set; } = null!;
                public string Document { get; set; } = null!;
                public string Type { get; set; } = null!;
            }
        }
    }
    public class Holder
    {
        public string? Name { get; set; }
        public string? Document { get; set; }
        public string? Email { get; set; }
        public string? Type { get; set; }
    }

    public class Recipient
    {
        public string? Number { get; set; }
        public string? Branch { get; set; }
        public string? Type { get; set; }
        public string? BankCode { get; set; }
        public Bank? Bank { get; set; }
        public Holder? Holder { get; set; }
    }
    public class Sender
    {
        public string? Number { get; set; }
        public string? Branch { get; set; }
        public string? Type { get; set; }
        public string? BankCode { get; set; }
        public Bank? Bank { get; set; }
        public Holder? Holder { get; set; }
    }

    public class Bank
    {
        public string? Code { get; set; }
        public string? Name { get; set; }
        public string? NameFantasy { get; set; }
        public string? IspbCode { get; set; }
    }
}