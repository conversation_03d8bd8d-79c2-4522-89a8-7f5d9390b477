﻿namespace Multipay.Queue.Webhook.Core.Entities;
public class PixDepositRefundEntity
{
    public Guid Id { get; set; }
    public int CompanyBankAccountId { get; set; }
    public int BillingId { get; set; }
    public int CustomerId { get; set; }
    public int QrCodeId { get; set; }
    public byte CoinId { get; set; }
    public decimal OriginalValue { get; set; }
    public decimal ReturnedValue { get; set; }
    public decimal RateValue { get; set; }
    public decimal TariffValue { get; set; }
    public decimal PercentageRate { get; set; }
    public byte Parcels { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public string? Description { get; set; }
    public string? Status { get; set; }
    public string? TXId { get; set; }
    public string? EndToEndPayment { get; set; }
    public string? FinancialInstitutionId { get; set; }
}
