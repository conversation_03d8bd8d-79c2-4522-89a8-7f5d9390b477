﻿namespace Multipay.Queue.Webhook.Application.Models.Genial;

public class ReversalPixGenialModel
{
    public bool CustomerDebitMadeByMip { get; set; }
    public string InstantPaymentId { get; set; } = string.Empty;
    public string EndToEndId { get; set; } = string.Empty;
    public DateTime TimestampUTC { get; set; }
    public string SettlementId { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public ReceiverData Receiver { get; set; } = new();
    public PayerData? Payer { get; set; } = new();
    public DateTime SpiTimestampUTC { get; set; }
    public string SpiSettlementDate { get; set; } = string.Empty;
    public DateTime CustomerInitiationTimestampUTC { get; set; }
    public bool MirrorWasActive { get; set; }
    public string OriginalSystem { get; set; } = string.Empty;
    public dynamic? RemittanceInformation { get; set; }
    public string MipTransactionType { get; set; } = string.Empty;
    public EventType DebitEventType { get; set; } = new();
    public EventType DebitEventStatus { get; set; } = new();
    public dynamic? DebitRejectionReason { get; set; }
    public string InstructionType { get; set; } = string.Empty;
    public string? InitiationForm { get; set; } = string.Empty;
    public string TransactionPurpose { get; set; } = string.Empty;
    public string InstructionPriority { get; set; } = string.Empty;
    public bool IsRigthsAndObligationSettlement { get; set; }
    public string? Status { get; set; } = string.Empty;

    public class ReceiverData
    {
        public DebitPSPData DebitPsp { get; set; } = new();
        public string TaxId { get; set; } = string.Empty;
        public DebitAccountData DebitAccount { get; set; } = new();
    }

    public class DebitPSPData
    {
        public string Ispb { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class DebitAccountData
    {
        public string AccountNumber { get; set; } = string.Empty;
        public string AccountType { get; set; } = string.Empty;
        public string Branch { get; set; } = string.Empty;
    }

    public class PayerData
    {
        public DebitPspData DebitPsp { get; set; } = new();
        public string TaxId { get; set; } = string.Empty;
        public string? Name { get; set; } = string.Empty;
        public DebitAccountData DebitAccount { get; set; } = new();
    }

    public class DebitPspData
    {
        public string Ispb { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class EventType
    {
        public int Code { get; set; }
        public string Description { get; set; } = string.Empty;
    }
}
