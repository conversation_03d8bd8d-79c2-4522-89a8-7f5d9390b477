﻿using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaService : ICobrancaService
    {
        private readonly IUnitOfWork unitOfWork;

        public CobrancaService(IUnitOfWork unitOfWork)
        {
            this.unitOfWork = unitOfWork;
        }

        public Task AdicionarPagadorCobranca(int idCobranca, string cpfPagadorPix, string nomePagadorPix, string endToEndId, string? ispbPagador, string? nomeBancoPagador)
        {
            return unitOfWork.CobrancaRepository.AdicionarPagadorCobranca(idCobranca, cpfPagadorPix, nomePagadorPix, endToEndId, ispbPagador, nomeBancoPagador);
        }

        public Task AtualizarEndToEndPorTxId(string txId, string endToEndId)
        {
            return unitOfWork.QrCodePixRepository.AtualizarEndToEndPorTxId(txId, endToEndId);
        }

        public Task ConfirmarPagamentoBRL(int idCobranca, int idCliente, byte pIX, decimal valorPago, int numeroParcelas, string descricao, decimal valor, decimal porcentagemTaxa, decimal valorTarifa, int horasResgate, string txIdPIX)
        {
            return unitOfWork.CobrancaRepository.ConfirmarPagamentoBRL(idCobranca, idCliente, pIX, valorPago, numeroParcelas, descricao, valor, porcentagemTaxa, valorTarifa, horasResgate, txIdPIX);
        }

        public virtual async ValueTask<CobrancaModel?> ObterCobrancaPorCodigo(string codigo)
        {
            var cobranca = await unitOfWork.CobrancaRepository.ObterPorCodigo(codigo);

            if (cobranca == null)
                return null;

            var transacaoPIX = await unitOfWork.QrCodePixRepository.ObterPorIdCobranca(cobranca.Id);

            if (transacaoPIX != null)
                cobranca.SetarTransacao(transacaoPIX);

            return cobranca;
        }

        public virtual async ValueTask<CobrancaModel?> ObterCobranca(string txId)
        {
            var transacaoPIX = await unitOfWork.QrCodePixRepository.ObterPorTxId(txId);

            if (transacaoPIX == null)
                return null;

            var cobranca = await unitOfWork.CobrancaRepository.ObterPorId(transacaoPIX.IdCobranca);

            if (cobranca != null)
                cobranca.SetarTransacao(transacaoPIX);

            return cobranca;
        }
    }
}