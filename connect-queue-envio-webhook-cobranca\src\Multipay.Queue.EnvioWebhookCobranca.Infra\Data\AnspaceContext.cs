﻿using Microsoft.EntityFrameworkCore;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Data
{
    public class AnspaceContext : DbContext
    {
        //public AnspaceContext() { }
        public AnspaceContext(DbContextOptions<AnspaceContext> options) : base(options) { }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            var entity = modelBuilder.Entity<CobrancaModel>();
            entity.HasNoKey().Ignore(c => c.TransacaoPix);
            entity.Property(c => c.Id).HasColumnName("Id");
            entity.Property(c => c.ValorLiquido).HasPrecision(18, 2);
            entity.Property(c => c.ValorPago).HasPrecision(18, 8);

            modelBuilder.Entity<QRCodePIXModel>()
                .HasNo<PERSON>ey();

            var entityTaxa = modelBuilder.Entity<TaxaClienteOperacaoModel>();
            entityTaxa.HasNoKey();
            entityTaxa.Property(item => item.TarifaAVista).HasPrecision(18, 2);
            entityTaxa.Property(item => item.TaxaAVista).HasPrecision(18, 2);
            entityTaxa.Property(item => item.TarifaParcelado2x6).HasPrecision(18, 2);
            entityTaxa.Property(item => item.TarifaParcelado7x12).HasPrecision(18, 2);
            entityTaxa.Property(item => item.TaxaParcelado2x6).HasPrecision(18, 2);
            entityTaxa.Property(item => item.TaxaParcelado7x12).HasPrecision(18, 2);
        }

        public DbSet<CobrancaModel> Cobrancas { get; set; }
        public DbSet<ConstanteModel> Constantes { get; set; }
        public DbSet<TaxaClienteOperacaoModel> TaxaClienteOperacao { get; set; }
    }
}