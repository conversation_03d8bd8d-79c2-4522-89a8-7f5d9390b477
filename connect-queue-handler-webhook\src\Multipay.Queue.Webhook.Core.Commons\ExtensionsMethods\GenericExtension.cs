﻿namespace Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;

public static class GenericExtension
{
    public static string ToJson<T>(this T source, bool writeIndented = false)
    {
        var options = new System.Text.Json.JsonSerializerOptions { WriteIndented = writeIndented };

        return System.Text.Json.JsonSerializer.Serialize(source, options);
    }

    public static T TryDeserialize<T>(this string serializedObject, Newtonsoft.Json.JsonSerializerSettings? serializerSettings = null)
    {
        try
        {
            return (Newtonsoft.Json.JsonConvert.DeserializeObject<T>(serializedObject, serializerSettings))!;
        }
        catch (Exception)
        {
            throw new Exception($"Não foi possível tratar este objeto: {serializedObject}");
        }
    }
}