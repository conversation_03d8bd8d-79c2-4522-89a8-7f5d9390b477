﻿namespace Multipay.Queue.Webhook.Core.Models
{
    public class SolicitacaoSaque
    {
        public int Id { get; set; }
        public string Codigo { get; set; } = null!;
        public DateTime? DataPagamento { get; set; }
        public DateTime? DataEstorno { get; set; }
        public DateTime? DataConclusao { get; set; }
        public string? CodigoTransacao { get; set; }
        public string? CodigoMovimento { get; set; }
        public string? UrlAtualizacao { get; set; }
        public string? UrlConfirmacao { get; set; }
        public int IdCliente { get; set; }

        internal bool VerificarSeFinalizado(out string? msg)
        {
            if (DataPagamento != null)
            {
                msg = "Saque já pago";
                return true;
            }
            if (DataEstorno != null)
            {
                msg = "Saque já estornado";
                return true;
            }
            if (DataConclusao != null)
            {
                msg = "Saque já concluído";
                return true;
            }
            msg = null;
            return false;
        }
    }
}