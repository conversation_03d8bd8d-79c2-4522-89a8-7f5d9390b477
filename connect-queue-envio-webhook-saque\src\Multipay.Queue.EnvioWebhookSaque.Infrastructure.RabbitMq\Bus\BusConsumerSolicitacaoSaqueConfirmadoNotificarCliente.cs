﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Settings;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.Webhook.Infrastructure.RabbitMq.Bus
{
    internal class BusConsumerSolicitacaoSaqueConfirmadoNotificarCliente : EventBusConsumerRabbitMQ<SaqueConfirmadoNotificarClienteEvent>, IBusConsumerSolicitacaoSaqueConfirmadoNotificarCliente
    {
        public BusConsumerSolicitacaoSaqueConfirmadoNotificarCliente(
            IRabbitMQPersistentConnection persistentConnection,
            IOptions<RabbitMqSettings> options,
            ILogger<BusConsumerSolicitacaoSaqueConfirmadoNotificarCliente> logger)
            : base(persistentConnection, logger, options.Value.NotificarSaqueCliente)
        { }
    }
}