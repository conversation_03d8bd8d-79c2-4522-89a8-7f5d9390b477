namespace Domain.Entities;

public class ApplicationEntity
{    
    public int Id { get; }
    public int TerminalId { get; }
    public int CustomerId { get; }
    public Guid Guid { get; }    
    public TerminalEntity? Terminal { get; private set; }
    public CustomerEntity? Customer { get; private set; }

    public ApplicationEntity(int id, int terminalId, int customerId, Guid guid)
    {
        Id = id;
        TerminalId = terminalId;
        CustomerId = customerId;
        Guid = guid;        
    }

    public void SetTerminal(TerminalEntity value) => Terminal = value;
    public void SetCustomer(CustomerEntity value) => Customer = value;
}
