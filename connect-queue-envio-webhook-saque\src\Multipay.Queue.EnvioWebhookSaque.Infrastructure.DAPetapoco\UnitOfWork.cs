﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.Repositories;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco
{
    public interface IUnitOfWork : IDisposable
    {
        //IRecebimentoOperacaoBancariaRepository RecebimentoOperacaoBancariaRepository { get; }
        //IContaBancariaRepository ContaBancariaRepository { get; }
        //ITaxaClienteOperacaoRepository TaxaClienteOperacaoRepository { get; }
        //IEntradaClienteRepository EntradaClienteRepository { get; }
        //ICobrancaRepository CobrancaRepository { get; }
        //IQrCodePixRepository QrCodePixRepository { get; }
        IConstantesPetapocoRepository ConstantesRepository { get; }

        ISolicitacaoSaqueRepository SolicitacaoSaqueRepository { get; }
        //IChavePIXContaBancariaFavorecidoClienteRepository ChavePIXContaBancariaFavorecidoClienteRepository { get; }
        //IContaBancariaFavorecidoClienteRepository ContaBancariaFavorecidoClienteRepository { get; }
        //IConstantesRepository ConstantesRepository { get; }
    }

    internal class UnitOfWork : IUnitOfWork
    {
        private readonly ILogger<UnitOfWork> logger;
        private readonly IDatabase database;

        //public IRecebimentoOperacaoBancariaRepository RecebimentoOperacaoBancariaRepository => new RecebimentoOperacaoBancariaRepository(logger, database);
        //public IContaBancariaRepository ContaBancariaRepository => new ContaBancariaRepository(logger, database);
        //public ITaxaClienteOperacaoRepository TaxaClienteOperacaoRepository => new TaxaClienteOperacaoRepository(logger, database);
        //public IEntradaClienteRepository EntradaClienteRepository => new EntradaClienteRepository(logger, database);
        //public ICobrancaRepository CobrancaRepository => new CobrancaRepository(logger, database);
        //public IQrCodePixRepository QrCodePixRepository => new QrCodePixRepository(logger, database);
        public IConstantesPetapocoRepository ConstantesRepository => new ConstantesPetapocoRepository(database);

        public ISolicitacaoSaqueRepository SolicitacaoSaqueRepository => new SolicitacaoSaqueRepository(logger, database);
        //public IChavePIXContaBancariaFavorecidoClienteRepository ChavePIXContaBancariaFavorecidoClienteRepository => new ChavePIXContaBancariaFavorecidoClienteRepository(logger, database);
        //public IContaBancariaFavorecidoClienteRepository ContaBancariaFavorecidoClienteRepository => new ContaBancariaFavorecidoClienteRepository(logger, database);
        //public IConstantesRepository ConstantesRepository => new ConstantesRepository(database);

        public UnitOfWork(ILogger<UnitOfWork> logger, IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public Task BeginTransactionAsync()
        {
            return database.BeginTransactionAsync();
        }

        public void CompleteTransaction()
        {
            database.CompleteTransaction();
        }

        public void AbortTransaction()
        {
            database.AbortTransaction();
        }

        public void Dispose()
        {
            database.Dispose();
        }
    }
}