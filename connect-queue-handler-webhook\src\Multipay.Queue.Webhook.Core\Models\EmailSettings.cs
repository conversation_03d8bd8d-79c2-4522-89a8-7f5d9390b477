﻿using Microsoft.Extensions.Configuration;

namespace Multipay.Queue.Webhook.Core.Models
{
    public class EmailSettings
    {
        public string SMTPServer { get; set; } = null!;
        public int? SMTPPort { get; set; }
        public string SMTPUser { get; set; } = null!;
        public string SMTPPassword { get; set; } = null!;
        public bool? SMTPSSL { get; set; }
        public string SMTPFrom { get; set; } = null!;
        public string SMTPDisplayName { get; set; } = null!;

        public string NomeFantasia { get; set; } = null!;
        public string UrlSite { get; set; } = null!;
        public string CorPrincipal { get; set; } = null!;
        public string TextoRodapeEmail { get; set; } = null!;
        public string EmailSuporteOperacional { get; set; } = null!;

        public static EmailSettings Build(EmailSettings settings, IConfiguration configuration)
        {
            if (settings == null)
                settings = new EmailSettings();

            Int32.TryParse(configuration["EmailSMTP:SMTPPort"], out int port);
            Boolean.TryParse(configuration["EmailSMTP:SMTPSSL"], out bool ssl);

            settings.SMTPServer = settings.SMTPServer ?? configuration["EmailSMTP:SMTPServer"] ?? throw new ArgumentException($"SMTPServer not found");
            settings.SMTPPort = settings.SMTPPort ?? (port == 0 ? throw new ArgumentException($"SMTPPort not found") : port);
            settings.SMTPUser = settings.SMTPUser ?? configuration["EmailSMTP:SMTPUser"] ?? throw new ArgumentException($"SMTPUser not found");
            settings.SMTPPassword = settings.SMTPPassword ?? configuration["EmailSMTP:SMTPPassword"] ?? throw new ArgumentException($"SMTPPassword not found");
            settings.SMTPSSL = settings.SMTPSSL ?? ssl;
            settings.SMTPFrom = settings.SMTPFrom ?? configuration["EmailSMTP:SMTPFrom"] ?? throw new ArgumentException($"SMTPFrom not found");
            settings.SMTPDisplayName = settings.SMTPDisplayName ?? configuration["EmailSMTP:SMTPDisplayName"] ?? throw new ArgumentException($"SMTPDisplayName not found");
            settings.NomeFantasia = settings.NomeFantasia ?? configuration["DadosEmpresa:NomeFantasia"] ?? throw new ArgumentException($"NomeFantasia not found");
            settings.UrlSite = settings.UrlSite ?? configuration["EnderecosSistema:UrlSite"] ?? throw new ArgumentException($"UrlSite not found");
            settings.CorPrincipal = settings.CorPrincipal ?? configuration["CoresPigPag:CorPrincipal"] ?? throw new ArgumentException($"CorPrincipal not found");
            settings.TextoRodapeEmail = settings.TextoRodapeEmail ?? configuration["Email:TextoRodapeEmail"] ?? throw new ArgumentException($"TextoRodapeEmail not found");
            settings.EmailSuporteOperacional = settings.EmailSuporteOperacional ?? configuration["DadosEmpresa:EmailSuporteOperacional"] ?? throw new ArgumentException($"EmailSuporteOperacional not found");

            return settings;
        }
    }
}