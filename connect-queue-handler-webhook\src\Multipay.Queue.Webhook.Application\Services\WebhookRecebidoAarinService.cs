﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.Aarin;
using Multipay.Queue.Webhook.Application.Services.Aarin;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco.Repositories;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;
using Serilog;
using Serilog.Context;

namespace Multipay.Queue.Webhook.Application.Services
{
    public class WebhookRecebidoAarinService : IWebhookRecebidoService
    {
        private readonly ILogger<WebhookRecebidoAarinService> logger;

        public WebhookRecebidoAarinService(ILogger<WebhookRecebidoAarinService> logger)
        {
            this.logger = logger;
        }
        public TipoBanco TipoBanco => TipoBanco.BancoAarin;

        public async Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
        {
            if (@event.Tipo == "pix/recebimento")
            {
                this.logger.LogInformation("[{0}] Iniciando PIX Recebimento, Tipo: {1}", nameof(PixReversalService), @event.Tipo);

                var service = serviceProvider.GetRequiredService<CobrancaRecebidaAarinService>();
                return await service.Processar($"{@event.Data}", @event.IdWebhook);
            }

            this.logger.LogInformation("[{0}] Iniciando verificacao de devolucao, Tipo: {1}", nameof(PixReversalService), @event.Tipo);

            try
            {

                var serviceRefund = serviceProvider.GetRequiredService<IPixReversalService>();
                if (await serviceRefund.VerifyPixCorrelationAsync<ReversalPixAarinModel>(@event, new BehaviorReversalPixModel
                {
                    CheckCorrelationId = true,
                    CheckStatus = true,
                    CheckValue = true,
                    StatusAttributeName = "Status",
                    CompletedStatusName = "Completed",
                    CorrelationIdAttributeName = "PixId",
                    FinancialInstitution = "Aarin",
                    OriginIp = ""
                }, default))
                {
                    this.logger.LogInformation("[{0}] Pix devolução encontrado, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                    @event.Tipo = "pix/devolucao";
                }
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "[{0}] Falha na busca de Devolucao, Ex: {ex}", nameof(PixReversalService), ex);
            }

            this.logger.LogInformation("[{0}] Final de verificacao de devolucao, Tipo: {1}", nameof(PixReversalService), @event.Tipo);

            if (@event.Tipo == "pix/pagamento")
            {
                this.logger.LogInformation("[{0}] Iniciando PIX pagamento, Tipo: {1}", nameof(PixReversalService), @event.Tipo);

                var service = serviceProvider.GetRequiredService<IPagamentoConfirmadoAarinService>();
                return await service.ProcessarPagamento(@event);
            }

            if (@event.Tipo == "pix/devolucao")
            {

                this.logger.LogInformation("[{0}] Iniciando PIX devolucao, Tipo: {1}", nameof(PixReversalService), @event.Tipo);

                var service = serviceProvider.GetRequiredService<IPixReversalService>();
                return await service.ProcessReversalPixAsync<ReversalPixAarinModel>(@event, new BehaviorReversalPixModel
                {
                    CheckCorrelationId = true,
                    CheckStatus = true,
                    CheckValue = true,
                    StatusAttributeName = "Status",
                    CompletedStatusName = "Completed",
                    CorrelationIdAttributeName = "PixId",
                    FinancialInstitution = "Aarin",
                    ValueAttributeName = "Value",
                    OriginIp = ""
                }, default);
            }

            logger.LogWarning($"Mensagem não tratada. {@event}");

            return await Task.FromResult(true);
        }

        public string? GetCorrelationId(WebhookRecebidoEvent @event)
        {
            try
            {
                if (@event.Data is null)
                    return string.Empty;

                var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<ReversalPixAarinModel>($"{@event.Data}");
                if (parameters is null)
                    return string.Empty;

                return parameters.PixId;
            }
            catch(Exception ex)
            {
                this.logger.LogError(ex, "[{0}] Não foi possivel obter o pixid, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                return string.Empty;
            }
        }
    }

}
