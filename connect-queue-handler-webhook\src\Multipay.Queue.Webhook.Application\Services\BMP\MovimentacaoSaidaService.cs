﻿using Multipay.Queue.Webhook.Application.Models.BMP;
using Multipay.Queue.Webhook.Application.Services.ContaGrafica;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Emails;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.BMP
{
    public interface IMovimentacaoSaidaService
    {
        Task<bool> ProcessarEnvioPIX(string idWebhook, CallbackRecebidoModel parameters);

        Task<bool> ProcessarEnvioTED(string idWebhook, CallbackRecebidoModel parameters);

        Task<bool> ProcessarTransferenciaEntreContas(string idWebhook, CallbackRecebidoModel parameters);
    }

    internal class MovimentacaoSaidaService : MovimentacaoSaidaBaseService<CallbackRecebidoModel>, IMovimentacaoSaidaService
    {
        public MovimentacaoSaidaService(IUnitOfWork unitOfWork, IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IEmailHandler emailHandler) : base(unitOfWork, busNotificarSaqueConfirmado, emailHandler)
        {
        }

        protected override TipoBanco TipoBanco => TipoBanco.BMP;
    }
}