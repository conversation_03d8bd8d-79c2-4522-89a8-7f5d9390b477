﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Multipay.Queue.EnvioWebhookCobranca.Core.Utils;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Redis;
using Multipay.Service.Criptography;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Setup
{
    public static class DependencyInjectionRepositories
    {
        public static void AddDependencyInjectionApplicationRepositories(this IServiceCollection services, IConfiguration configuration, CriptoService criptoService)
        {
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            services.AddSingleton<IRedisConnectionFactory>(sp => new RedisConnectionFactory(configuration["RedisConnectionString"]!));

            services.AddScoped(servicesProvider =>
            {
                var sp = services.BuildServiceProvider();
                using var scope = sp.CreateScope();
                var ambienteHelp = scope.ServiceProvider.GetRequiredService<AmbienteHelp>();

                var cnn = ambienteHelp.SqlServer_ConnectionString;

                if (string.IsNullOrWhiteSpace(cnn))
                    cnn = configuration.GetConnectionString("MultiPayDatabase")!;

                var start = cnn.IndexOf("password=") + 9;
                var end = cnn.IndexOf(";", start);

                var password = cnn.Substring(start, end - start);
                var encryptedPassword = password;

                password = criptoService.Decrypt(password).Result;

                cnn = cnn.Replace(encryptedPassword, password);

                return DatabaseConfiguration
                    .Build()
                    .UsingProvider<PetaPoco.Providers.SqlServerDatabaseProvider>()
                    .UsingConnectionString(cnn)
                    .Create();
            });
        }

        public static void AddDataBaseConfigurationProvider(this IServiceCollection services, ConfigurationManager configuration)
        {
            var sp = services.BuildServiceProvider();
            using var scope = sp.CreateScope();

            var ambienteHelp = scope.ServiceProvider.GetRequiredService<AmbienteHelp>();

            var database = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            string tema = ambienteHelp.Tema ?? throw new ArgumentNullException("Tema não localizado em AppSettings");

            configuration.Sources.Add(new DataBaseConfigurationSource(database, tema));
        }
    }
}