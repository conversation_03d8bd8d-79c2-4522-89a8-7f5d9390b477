﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Multipay.Queue.EnvioWebhookSaque.Core.Utils;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Data;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Repositories.MongoRepositories;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Services;
using Multipay.Service.Criptography;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Setup
{
    public static class DependencyInjectionRepositories
    {
        public static void AddDependencyInjectionApplicationRepositoriesMongo(this IServiceCollection services, IConfiguration configuration, CriptoService criptoService)
        {
            var sp = services.BuildServiceProvider();
            using var scope = sp.CreateScope();
            var ambienteHelp = scope.ServiceProvider.GetRequiredService<AmbienteHelp>();

            var mongoSettings = new MongoStoreDatabaseSettings();

            configuration.GetSection(nameof(MongoStoreDatabaseSettings)).Bind(mongoSettings);

            var config = configuration.GetSection(key: nameof(MongoStoreDatabaseSettings));

            var connectionString = config.GetRequiredSection("ConnectionString").Value!;

            var start = connectionString.IndexOf("//") + 2;
            var end = connectionString.IndexOf("@", start);

            var password = connectionString.Substring(start, end - start).Split(":")[1];
            var encryptedPassword = password;

            password = criptoService.Decrypt(password).Result;

            connectionString = connectionString.Replace(encryptedPassword, password);

            config.GetSection("ConnectionString").Value = connectionString;

            mongoSettings.ConnectionString = connectionString;

            services.Configure<MongoStoreDatabaseSettings>(config)
                .PostConfigure<MongoStoreDatabaseSettings>(opt =>
                {
                    opt = mongoSettings;
                });

            services.AddSingleton<IConnectMongoService, ConnectMongoService>();
            services.AddSingleton<AnspaceMongoContext>();

            services.AddSingleton<ILogSolicitacaoSaqueRepository, LogSolicitacaoSaqueRepository>();
            services.AddSingleton<IWebhookFornecedorSolicitacaoSaqueRepository, WebhookFornecedorSolicitacaoSaqueRepository>();
            services.AddSingleton<IEnvioWebhookSolicitacaoSaqueSaqueRepository, EnvioWebhookSolicitacaoSaqueSaqueRepository>();
            services.AddScoped<ICentralizadorLogsService, CentralizadorLogsService>();

            var cnn = ambienteHelp.SqlServer_ConnectionString;

            if (string.IsNullOrWhiteSpace(cnn))
                cnn = configuration.GetConnectionString("MultiPayDatabase")!;

            start = cnn.IndexOf("password=") + 9;
            end = cnn.IndexOf(";", start);

            password = cnn.Substring(start, end - start);
            encryptedPassword = password;

            password = criptoService.Decrypt(password).Result;

            cnn = cnn.Replace(encryptedPassword, password);

            services.AddDbContext<DbContext, AnspaceContext>(
                options => options.UseSqlServer(cnn),
                ServiceLifetime.Transient,
                ServiceLifetime.Transient);
        }
    }
}