namespace Application.Services.Models.Requests;

public class BankStatementRequest
{
    public string DigitalSignature { get; }
    public DateTime DataDe { get; }
    public DateTime DataAte { get; }
    public long IdCliente { get; }
    public long IdTipoConta { get; }

    public BankStatementRequest(long idCliente, long idTipoConta, DateTime dataDe,DateTime dataAte)
    {
        DataDe = dataDe;
        DataAte = dataAte;
        IdCliente = idCliente;
        DigitalSignature = string.Empty;
        IdTipoConta = idTipoConta;
    }
}
