﻿using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Data;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Repositories.MongoRepositories
{
    public interface ILogCobrancaRepository : IBaseRepository<LogCobrancaModel>
    { }

    internal class LogCobrancaRepository : BaseRepository<LogCobrancaModel>, ILogCobrancaRepository
    {
        public LogCobrancaRepository(
            ILogger<LogCobrancaRepository> logger,
            AnspaceMongoContext context) : base(logger, context.LogCobranca) { }

        public IEnumerable<LogCobrancaModel> ObterLogsPorCobranca(int id, string codigoCobranca)
        {
            var filter = Builders<LogCobrancaModel>.Filter;
            var find =
                filter.Or(
                    filter.Eq(nameof(LogCobrancaModel.IdCobranca), id),
                    filter.Eq(nameof(LogCobrancaModel.CodigoCobranca), codigoCobranca)
                );

            var list = _collection.Find(find);

            return list.ToList();
        }
    }
}