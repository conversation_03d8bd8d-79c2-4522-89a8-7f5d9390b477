{"Tema": "ConnectPSP", "NameApp": "queue-webhook-api", "Logging": {"LogLevel": {"Default": "Information", "System": "Error", "Microsoft": "Error"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Datadog.Logs"], "MinimumLevel": {"Default": "Verbose"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "DatadogLogs", "Args": {"apiKey": "********************************", "source": "queue-webhook", "host": "hml", "service": "Queue-Webhook-ConnectPSP", "configuration": {"url": "https://http-intake.logs.datadoghq.com", "port": 443}}}], "Enrich": ["FromLogContext", "WithThreadId", "WithMachineName"], "Properties": {"Application": "connectpsp-multipay-queue-webhook"}}, "RabbitMqSettings": {"NotificarCobrancaCliente": {"Publish": {"Exchange": "notificar-cobranca-cliente", "ExchangeType": "fanout"}, "Consumer": {"Exchange": "notificar-cobranca-cliente", "QueueName": "notificar-cobranca-cliente", "DeadLetter": {"Count": 5}}}, "NotificarCobrancaClientePublishNotification": {"Consumer": {"Exchange": "notificar-cobranca-cliente", "QueueName": "notificar-cobranca-cliente-publish-notification", "DeadLetter": {"Count": 5}}}, "SolicitacaoSaques": {"Publish": {"Exchange": "solicitacao-saques", "ExchangeType": "fanout"}}, "RefundPixNotification": {"Publish": {"Exchange": "refund-pix-notification", "ExchangeType": "fanout"}}, "NotificarCliente": {"Publish": {"Exchange": "notificar-cliente", "ExchangeType": "fanout"}, "Consumer": {"Exchange": "notificar-cliente", "QueueName": "notificar-cliente", "DeadLetter": {"Count": 5}}}, "NotificarSaqueCliente": {"Publish": {"Exchange": "notificar-saque-cliente", "ExchangeType": "fanout"}, "Consumer": {"Exchange": "notificar-saque-cliente", "QueueName": "notificar-saque-cliente", "DeadLetter": {"Count": 5}}}, "SolicitacaoSaquesEstornado": {"Publish": {"Exchange": "solicitacao-saques-estornado", "ExchangeType": "fanout"}}, "CentralizadorLogs": {"Publish": {"Exchange": "centralizador-logs", "ExchangeType": "fanout"}, "Consumer": {"Exchange": "centralizador-logs", "QueueName": "centralizador-logs", "DeadLetter": {"Count": 5}}}, "NotificarSaqueClientePublishNotification": {"Consumer": {"Exchange": "notificar-saque-cliente", "QueueName": "notificar-saque-cliente-publish-notification", "DeadLetter": {"Count": 5}}}, "Webhooks": {"Consumer": {"PrefetchCount": 20, "Exchange": "webhooks", "QueueName": "webhooks", "DeadLetter": {"Exchange": "webhooks-error", "QueueName": "webhooks-error", "RoutingKey": "webhooks-error", "Count": 3}}}}, "Bancos": {"Genial": {"AccountName": "Anspace Intermediacao e Agenciamento de Negocios LTDA", "IspGenial": "********", "Url": "https://genial-baas-anspace.parceiros.api.genial.systems", "UrlAuthentication": "https://genial-arquitetura-authentication.production.api.genial.systems", "UrlMovimentacao": "https://genial-central-movimentacoes.production.api.genial.systems", "XOrigem": "ANSPACE-API"}, "BS2": {"Url": "https://api.bs2.com", "Scope": "saldo extrato pagamento transferencia boleto cob.write cob.read pix.write pix.read dict.write dict.read webhook.read webhook.write cobv.write cobv.read comprovante"}, "Delbank": {"BaseUrl": "https://api.delbank.com.br/"}, "AnspacePay": {"BaseUrl": "https://anspace.pi.delbank.com.br/"}, "Aarin": {"Domain": "aarin.com.br"}}}