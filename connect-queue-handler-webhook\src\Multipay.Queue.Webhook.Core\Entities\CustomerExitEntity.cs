﻿namespace Multipay.Queue.Webhook.Core.Entities;
public class CustomerExitEntity
{
    public int Id { get; set; }
    public int CustomerId { get; set; }
    public byte CoinId { get; set; }
    public string? Description { get; set; }
    public decimal Value { get; set; }
    public decimal RateValue { get; set; }
    public decimal TariffValue { get; set; }
    public decimal NetValue { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime UnlockDate { get; set; }
    public DateTime? LockDate { get; set; }
    public DateTime? CancellationDate { get; set; }
    public string? TxId { get; set; }
}
