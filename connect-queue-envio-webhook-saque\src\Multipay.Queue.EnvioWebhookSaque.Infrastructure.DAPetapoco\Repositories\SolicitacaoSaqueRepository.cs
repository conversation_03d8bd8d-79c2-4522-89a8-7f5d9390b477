﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookSaque.Core.Models;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.Repositories
{
    public interface ISolicitacaoSaqueRepository
    {
        Task<List<MovimentacaoWebhooksAdicionaisModel>> ObterWebhooksAdicionaisPorId(int idCobranca);

        Task ConfirmarEnvioWebhookAdicionalPorId(int id);

        Task<ViewSolicitacaoSaqueDadosBasicos?> ObterSolicitacaoSaqueDadosBasicosPorCodigo(string codigo);

        Task<ViewSolicitacaoSaqueDadosBasicos?> ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(string endToEndId);

        Task UpdateMudarStatusConcluido(int id, string? nsu, string idWebhookConfirmacao);

        Task UpdateMudarStatusEmProcessamento(int id);

        Task EstornarSaque(int idSolicitacaoSaque, string mensagemJustificativa);

        Task ConfirmarSolicitacaoSaque(int id, string idWebhook);

        Task<ViewSolicitacaoSaqueDadosBasicos?> ObterSolicitacaoSaqueDadosBasicosPorId(int idSolicitacaoSaque);

        Task<IEnumerable<ViewSolicitacaoSaqueDadosBasicos>> SolicitacaoSaquePorChavePixValidada(string chave);

        Task<string> ObterNomeClientePorId(int id);
    }

    internal class SolicitacaoSaqueRepository : ISolicitacaoSaqueRepository
    {
        private ILogger<UnitOfWork> logger;
        private IDatabase database;

        public SolicitacaoSaqueRepository(ILogger<UnitOfWork> logger, IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public async Task<List<MovimentacaoWebhooksAdicionaisModel>> ObterWebhooksAdicionaisPorId(int idCobranca)
        {
            try
            {
                var sql = Sql.Builder.Select("*")
                    .From("MovimentacaoWebhooksAdicionais")
                    .Where("IdCobranca=@0", idCobranca);

                using var reader = await database.QueryAsync<MovimentacaoWebhooksAdicionaisModel?>(sql);

                var retorno = new List<MovimentacaoWebhooksAdicionaisModel>();

                if (await reader.ReadAsync())
                {
                    retorno.Add(reader.Poco);
                }

                return retorno;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public async Task ConfirmarEnvioWebhookAdicionalPorId(int id)
        {
            try
            {
                var sql = $@"Update MovimentacaoWebhooksAdicionais set DataEnvio = Getdate() where Id = {id}";

                var rtn = await database.ExecuteAsync(sql);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<ViewSolicitacaoSaqueDadosBasicos?> ObterSolicitacaoSaqueDadosBasicosPorCodigo(string codigo)
        {
            try
            {
                using var reader = await database.QueryProcAsync<ViewSolicitacaoSaqueDadosBasicos>("Painel.Select_SolicitacaoSaqueDadosBasicosPorCodigo", new
                {
                    codigo
                });

                if (await reader.ReadAsync())
                    return reader.Poco;

                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public Task UpdateMudarStatusConcluido(int id, string? nsu, string idWebhookConfirmacao)
        {
            try
            {
                return database.UpdateAsync("SolicitacaoSaque", "Id", new
                {
                    IdStatusSolicitacaoSaque = 3,
                    IdWebhookConfirmacaoObjId = idWebhookConfirmacao,
                    DataPagamento = DateTime.UtcNow,
                    DataConclusao = DateTime.UtcNow,
                    NSU = nsu,
                }, id);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public Task UpdateMudarStatusEmProcessamento(int id)
        {
            try
            {
                return database.UpdateAsync("SolicitacaoSaque", "Id", new
                {
                    IdStatusSolicitacaoSaque = 2,
                    DataProcessamento = DateTime.UtcNow
                }, id);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public async Task EstornarSaque(int idSolicitacaoSaque, string mensagemJustificativa)
        {
            try
            {
                await database.ExecuteNonQueryProcAsync("[Arquitetura].[Update_SolicitacaoSaqueEstornoSaque]", new
                {
                    @Id = idSolicitacaoSaque,
                    @JustificativaEstorno = mensagemJustificativa
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<ViewSolicitacaoSaqueDadosBasicos?> ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(string codigoTransacao)
        {
            try
            {
                using var reader = await database.QueryProcAsync<ViewSolicitacaoSaqueDadosBasicos>("Painel.Select_SolicitacaoSaqueDadosBasicosPorCodigoTransacao", new
                {
                    codigoTransacao
                });

                if (await reader.ReadAsync())
                    return reader.Poco;

                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public async Task ConfirmarSolicitacaoSaque(int idSaque, string idWebhook)
        {
            try
            {
                await database.ExecuteNonQueryProcAsync("[Arquitetura].[Update_SolicitacaoSaqueConcluirComoPagoV1]", new
                {
                    idSaque,
                    idWebhook
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<ViewSolicitacaoSaqueDadosBasicos?> ObterSolicitacaoSaqueDadosBasicosPorId(int idSolicitacaoSaque)
        {
            try
            {
                using var reader = await database.QueryProcAsync<ViewSolicitacaoSaqueDadosBasicos>("[Arquitetura].[Select_SolicitacaoSaqueDadosTransacaoPorId]", new
                {
                    idSolicitacaoSaque
                });

                if (await reader.ReadAsync())
                    return reader.Poco;

                return null;
                //var reader = await database.QueryAsync<ViewSolicitacaoSaqueDadosBasicos>(Sql.Builder
                //    .Select(
                //    "s.*",
                //    "TOB.Nome 'TipoOperacaoBancaria'",
                //    "F.Nome 'NomeFavorecido', F.CPFCNPJ 'CPFCNPJFavorecido'",
                //    "TRIM(CF.Agencia) 'AgenciaFavorecido', CONCAT(TRIM(CF.Conta), '-', TRIM(CF.DigitoVerificadorConta)) 'ContaFavorecido', CF.ChavePIX",
                //    "TC.Nome 'TipoChavePIX'")

                //    .From("SolicitacaoSaque s")
                //    .LeftJoin("TipoOperacaoBancaria TOB").On("TOB.Id = s.IdTipoOperacao")
                //    .LeftJoin("FavorecidoCliente F").On("F.Id = s.IdFavorecidoCliente")
                //    .LeftJoin("ContaBancariaFavorecidoCliente CF").On("CF.Id = s.IdContaBancariaFavorecidoCliente")
                //    .LeftJoin("TipoChavePIX TC").On("TC.Id = CF.IdTipoChavePIX")
                //    .Where("s.Id=@0", idSolicitacaoSaque));

                //if (await reader.ReadAsync())
                //{
                //    return reader.Poco;
                //}
                //return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<IEnumerable<ViewSolicitacaoSaqueDadosBasicos>> SolicitacaoSaquePorChavePixValidada(string chavePIX)
        {
            try
            {
                using var reader = await database.QueryProcAsync<ViewSolicitacaoSaqueDadosBasicos>("[Webhook].[Select_SolicitacaoSaquePorChavePixValidada]", new
                {
                    chavePIX
                });

                var list = new List<ViewSolicitacaoSaqueDadosBasicos>();

                while (await reader.ReadAsync())
                {
                    list.Add(reader.Poco);
                }
                return list;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(SolicitacaoSaqueRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<string> ObterNomeClientePorId(int id)
        {
            var nome = await database.ExecuteScalarAsync<string>($"SELECT NomeFantasia FROM PessoaJuridica a WITH (NOLOCK) INNER JOIN Cliente b WITH (NOLOCK) ON b.IdPessoa = a.Id WHERE b.Id = {id}");

            return nome ?? string.Empty;
        }
    }
}