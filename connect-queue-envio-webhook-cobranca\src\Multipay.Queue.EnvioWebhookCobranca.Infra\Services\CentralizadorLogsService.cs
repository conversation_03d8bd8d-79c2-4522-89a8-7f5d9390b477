﻿using Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Repositories.MongoRepositories;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Services
{
    public interface ICentralizadorLogsService
    {
        string AdicionarLogWebhookFornecedorCobranca(string idWebhookFornecedor, int idCobranca, string acao);

        string AdicionarLogCobranca(int idCobranca, string texto, string? idWebhook = null, string? codigoCobranca = null);

        string AdicionarLogRequisicoesFornecedor(string fornecedor, string tipo, string descricao, string bodyRequest, int statusCode, string bodyResponse);

        string AdicionarLogWebhookFornecedor(string fornecedor, string tipo, string body, string ipOprigem);

        string AdicionarLogEnvioWebhookCobranca(int idCobranca, string acao, string urlWebhook, string requestJson, string httpResponseCode, string responseJson);

        void Salvar();
    }

    internal partial class CentralizadorLogsService : ICentralizadorLogsService
    {
        private readonly Dictionary<Type, List<BaseModel>> _dicLogs = new();
        private readonly ILogCobrancaRepository logCobrancaRepository;
        private readonly IRequisicoesFornecedorRepository requisicoesFornecedorRepository;
        private readonly IWebhookFornecedorCobrancaRepository webhookFornecedorCobrancaRepository;
        private readonly IEnvioWebhookCobrancaRepository envioWebhookCobrancaRepository;

        public Help Util { get; }

        public CentralizadorLogsService(
            ILogCobrancaRepository logCobrancaRepository,
            IRequisicoesFornecedorRepository requisicoesFornecedorRepository,
            IWebhookFornecedorCobrancaRepository webhookFornecedorCobrancaRepository,
            IEnvioWebhookCobrancaRepository envioWebhookCobrancaRepository
            )
        {
            this.TrackId = System.Guid.NewGuid().ToString("N");
            this.Util = new Help(this);
            this.logCobrancaRepository = logCobrancaRepository;
            this.requisicoesFornecedorRepository = requisicoesFornecedorRepository;
            this.webhookFornecedorCobrancaRepository = webhookFornecedorCobrancaRepository;
            this.envioWebhookCobrancaRepository = envioWebhookCobrancaRepository;
        }

        public string TrackId { get; private set; }

        public void Add<T>(T document) where T : BaseModel
        {
            if (!_dicLogs.TryGetValue(typeof(T), out List<BaseModel>? logs))
            {
                _dicLogs.Add(typeof(T), logs = new List<BaseModel>());
            }

            document.TrackId = this.TrackId;
            logs.Add(document);
        }

        public void SetTrackId(string trackId)
        {
            this.TrackId = trackId;
            _dicLogs.Values
                .SelectMany(item => item)
                .ToList()
                .ForEach(item => item.TrackId = this.TrackId);
        }

        public void Salvar()
        {
            if (!_dicLogs.Any()) return;

            lock (_dicLogs)
            {
                foreach (var values in _dicLogs.Values.Where(itens => itens.Any()))
                {
                    switch (values.First())
                    {
                        case LogCobrancaModel _:
                            logCobrancaRepository.InsertMany(values.Select(item => (LogCobrancaModel)item));
                            break;

                        case RequisicoesFornecedorModel _:
                            requisicoesFornecedorRepository.InsertMany(values.Select(item => (RequisicoesFornecedorModel)item));
                            break;

                        case WebhookFornecedorCobrancaModel _:
                            webhookFornecedorCobrancaRepository.InsertMany(values.Select(item => (WebhookFornecedorCobrancaModel)item));
                            break;

                        case EnvioWebhookCobrancaModel _:
                            envioWebhookCobrancaRepository.InsertMany(values.Select(item => (EnvioWebhookCobrancaModel)item));
                            break;
                    }
                }
                _dicLogs.Clear();
            }
        }

        public void Update<T>(string id, Action<T?> updateModel)
            where T : BaseModel
        {
            if (_dicLogs.TryGetValue(typeof(T), out List<BaseModel>? logs))
            {
                var model = logs.FirstOrDefault(item => item.Id == id);
                updateModel(model as T);
            }
        }

        public string AdicionarLogWebhookFornecedorCobranca(string idWebhookFornecedor, int idCobranca, string acao)
        {
            var document = new WebhookFornecedorCobrancaModel(idCobranca, idWebhookFornecedor, acao);
            Add(document);

            return document.Id;
        }

        public string AdicionarLogCobranca(int idCobranca, string texto, string? idWebhook = null, string? codigoCobranca = null)
        {
            var document = new LogCobrancaModel(idCobranca, texto, string.Empty, string.Empty, true)
            {
                IdWebhook = idWebhook,
                CodigoCobranca = codigoCobranca
            };
            Add(document);

            return document.Id;
        }

        public string AdicionarLogRequisicoesFornecedor(string fornecedor, string tipo, string descricao, string bodyRequest, int statusCode, string bodyResponse)
        {
            var document = new RequisicoesFornecedorModel(fornecedor, tipo, descricao, bodyRequest, statusCode, bodyResponse);
            Add(document);
            return document.Id;
        }

        public string AdicionarLogWebhookFornecedor(string fornecedor, string tipo, string body, string ipOprigem)
        {
            var document = new WebhookFornecedorModel(fornecedor, tipo, body, ipOprigem);

            Add(document);

            return document.Id;
        }

        public string AdicionarLogEnvioWebhookCobranca(int idCobranca, string acao, string urlWebhook, string requestJson, string httpResponseCode, string responseJson)
        {
            var document = new EnvioWebhookCobrancaModel(idCobranca, acao, urlWebhook, requestJson, httpResponseCode, responseJson);
            Add(document);
            return document.Id;
        }
    }
}