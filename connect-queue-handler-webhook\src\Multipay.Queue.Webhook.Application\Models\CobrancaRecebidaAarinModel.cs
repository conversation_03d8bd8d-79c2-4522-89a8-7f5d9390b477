﻿using Multipay.Queue.Webhook.Core.Interfaces;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Models
{
    public class CobrancaRecebidaAarinModel : IPIXData
    {
        [JsonProperty("TxId")]
        public string Txid { get; set; } = string.Empty;

        [JsonProperty("Value")]
        public string Valor { get; set; } = string.Empty;

        [JsonProperty("PayerDocument")]
        public string CpfPagador { get; set; } = string.Empty;

        [JsonProperty("PayerName")]
        public string NomePagador { get; set; } = string.Empty;

        [JsonProperty("E2EId")]
        public string EndToEndId { get; set; } = string.Empty;
        public string PixId { get; set; } = string.Empty;

        
        [JsonProperty("PayerBankIspb")]
        public string? IspbPagador { get; set; }
        public string? NomeBancoPagador { get; set; } 
        public string? ContaBancoPagador { get; set; } 
        public string? AgenciaBancoPagador { get; set; }
    }
}
