﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.Saques;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Extensions;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado
{
    public interface IPagamentoConfirmadoBS2Service : IPagamentoConfirmadoService
    {
    }

    internal class PagamentoConfirmadoBS2Service : PagamentoConfirmadoBaseService<SolicitacaoSaqueRecebidoBS2Model>, IPagamentoConfirmadoBS2Service
    {
        public PagamentoConfirmadoBS2Service(
            ILogger<PagamentoConfirmadoBS2Service> logger,
            ICentralizadorLogsService centralizadorLogsService,
            IEstornarSaqueService estornarSaqueService,
            IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IBrasilApiService brasilApiService,
            IUnitOfWork unitOfWork) : base(logger, centralizadorLogsService, busNotificarSaqueConfirmado, brasilApiService, estornarSaqueService, unitOfWork)
        {
        }

        protected override string NomeBanco => TipoBanco.BS2.GetDescription();

        protected override string ObterEndToEndId(SolicitacaoSaqueRecebidoBS2Model parameters)
        {
            return parameters.EndToEndId;
        }

        protected override Status? ObterStatus(SolicitacaoSaqueRecebidoBS2Model parameters)
        {
            return parameters.Status switch
            {
                3 => (Status?)Status.EFETIVADO,
                4 => (Status?)Status.REJEITADO,
                5 => (Status?)Status.ERRO,
                6 => (Status?)Status.EXPIRADO,
                _ => null,
            };
        }
    }
}