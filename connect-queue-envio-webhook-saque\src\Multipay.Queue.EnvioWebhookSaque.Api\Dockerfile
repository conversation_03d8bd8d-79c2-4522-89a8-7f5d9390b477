#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/Multipay.Queue.EnvioWebhookSaque.Api/Multipay.Queue.EnvioWebhookSaque.Api.csproj", "src/Multipay.Queue.EnvioWebhookSaque.Api/"]
COPY ["src/Multipay.Queue.EnvioWebhookSaque.Application/Multipay.Queue.EnvioWebhookSaque.Application.csproj", "src/Multipay.Queue.EnvioWebhookSaque.Application/"]
COPY ["src/Multipay.Queue.EnvioWebhookSaque.Core/Multipay.Queue.EnvioWebhookSaque.Core.csproj", "src/Multipay.Queue.EnvioWebhookSaque.Core/"]
COPY ["src/Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco/Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj", "src/Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco/"]
COPY ["src/Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq/Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj", "src/Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq/"]
COPY ["src/Multipay.Queue.EnvioWebhookSaque.Infrastructure/Multipay.Queue.EnvioWebhookSaque.Infrastructure.csproj", "src/Multipay.Queue.EnvioWebhookSaque.Infrastructure/"]
RUN dotnet restore "src/Multipay.Queue.EnvioWebhookSaque.Api/Multipay.Queue.EnvioWebhookSaque.Api.csproj"
COPY . .
WORKDIR "/src/src/Multipay.Queue.EnvioWebhookSaque.Api"
RUN dotnet build "Multipay.Queue.EnvioWebhookSaque.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Multipay.Queue.EnvioWebhookSaque.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Multipay.Queue.EnvioWebhookSaque.Api.dll"]