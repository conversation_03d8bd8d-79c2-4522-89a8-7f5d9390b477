﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using StackExchange.Redis;
using System.Text.Json;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Redis
{
    public static class RedisGenerics
    {
        public static async Task SetKeyValueChargeAsync(this IRedisConnectionFactory redis, CobrancaModel cobranca, bool success, ILogger logger)
        {
            try
            {
                var database = await redis.GetDatabaseAsync();

                var key = RedisPrefix.WebhookKey(cobranca.TransacaoPix.TxId, success);

                if (await database.KeyExistsAsync(RedisPrefix.WebhookKey(cobranca?.TransacaoPix?.TxId, false)) && success)
                {
                    logger.LogWarning($"Webhook removido da lista de falhas. chave: {RedisPrefix.WebhookKey(cobranca?.TransacaoPix?.TxId, false)}");
                    await database.KeyDeleteAsync(RedisPrefix.WebhookKey(cobranca?.TransacaoPix?.TxId, false));
                    var result = await database.StringSetAsync(RedisPrefix.WebhookKey(cobranca?.TransacaoPix?.TxId, success), $"REPROCESSAMENTO DE WEBHOOK CONCLUIDO COM SUCESSO ÀS {DateTime.UtcNow.AddHours(-3):dd-MM-yyyy HH:mm:ss}", TimeSpan.FromDays(90), when: When.Always);
                }
                else if (success)
                {
                    var result = await database.StringSetAsync(RedisPrefix.WebhookKey(cobranca?.TransacaoPix?.TxId, success), $"WEBHOOK ENVIADO COM SUCESSO ÀS {DateTime.UtcNow.AddHours(-3):dd-MM-yyyy HH:mm:ss}", TimeSpan.FromDays(90), when: When.Always);
                    logger.LogWarning($"WEBHOOK ENVIADO COM SUCESSO COBRANÇA CÓDIGO: {cobranca.Codigo}");
                }
                else if (await database.KeyExistsAsync(RedisPrefix.WebhookKey(cobranca?.TransacaoPix?.TxId, false)))
                {
                    logger.LogWarning($"Webhook persiste com falha. chave: {RedisPrefix.WebhookKey(cobranca?.TransacaoPix?.TxId, false)}");
                    return;
                }
                else if (!success)
                {
                    var charge = new Charge(cobranca.IdCliente,
                            cobranca.Codigo,
                            cobranca.Guid,
                            cobranca?.TransacaoPix?.TxId,
                            cobranca.ValorLiquido,
                            cobranca.NumeroFatura,
                            cobranca.Id,
                            cobranca.UrlConfirmacao,
                            cobranca.RequestIdWebhook,
                            cobranca.MoedaPagamento,
                            cobranca.MoedaCobranca,
                            cobranca.Guid,
                            null,
                            cobranca.DataPagamento,
                            string.Empty,
                            string.Empty,
                            string.Empty,
                            string.Empty,
                            false,
                            string.Empty);

                    await database.StringSetAsync(RedisPrefix.WebhookKey(cobranca?.TransacaoPix?.TxId, success), JsonSerializer.Serialize(charge), TimeSpan.FromDays(90), when: When.Always);

                    logger.LogWarning($"Webhook adicionado a lista de falhas. chave: {RedisPrefix.WebhookKey(cobranca?.TransacaoPix?.TxId, false)}");
                }
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}