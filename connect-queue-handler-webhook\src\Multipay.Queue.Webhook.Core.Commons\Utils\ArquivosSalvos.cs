﻿using Multipay.Queue.Webhook.Core.Models;

namespace Multipay.Queue.Webhook.Core.Commons.Utils
{
    public class ArquivosSalvos
    {
        private readonly EmailSettings _emailSettings;
        private string nomeFantasia = "";

        public ArquivosSalvos(EmailSettings emailSettings)
        {
            this._emailSettings = emailSettings;
            nomeFantasia = this._emailSettings.NomeFantasia.ToUpperInvariant();
            CriarDiretoriosAsync();
        }

        public string CaminhoImagensEmail
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\IMAGENS EMAIL\"; } }
        public string CaminhoDocumentos
        { get { return @"C:\ARQUIVOS\DOCUMENTOS\PESSOAS\"; } }
        public string CaminhoOrdemPagamento
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\ORDEM PAGAMENTO\"; } }
        public string CaminhoAnexosEmailConfirmacaoCompra
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\EMAIL\ANEXOS\CONFIRMACAOCOMPRA\"; } }
        public string CaminhoExtratosBancarios
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\BANCOS\EXTRATO\"; } }
        public string CaminhoBoletos
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\BOLETOS\"; } }
        public string CaminhoCodigoBarrasBoletos
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\BOLETOS\CODIGOBARRAS\"; } }
        public string CaminhoQRCodePIX
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\PIX\QRCODE\"; } }
        public string CaminhoPIXWebhook
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\PIX\WEBHOOK\"; } }
        public string CaminhoPIXWebhookProcessados
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\PIX\WEBHOOK\PROCESSADOS\"; } }
        public string CaminhoListaTransacoesBitcoin
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\BITCOIN\TRANSFERENCIAS\"; } }
        public string CaminhoListaRetornosBitcoin
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\BITCOIN\RETORNOS\"; } }
        public string CaminhoChargebackDocumentosCliente
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\CHARGEBACK\DOCUMENTOSCLIENTE"; } }
        public string CaminhoChargebackDocumentosBandeira
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\CHARGEBACK\DOCUMENTOSBANDEIRA"; } }
        public string CaminhoArquivoRetornoEDIADIQ
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\ARQUIVOSRETORNO\CONCILIACAO\FTP\EDI\ADIQ"; } }
        public string CaminhoLogSistema
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\LOG\"; } }
        public string CaminhoLogWebhook
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\LOG\WEBHOOK\"; } }
        public string CaminhoLogHandShake
        { get { return @"C:\ARQUIVOS\" + nomeFantasia + @"\LOG\HANDSHAKE\"; } }

        public virtual void CriarDiretoriosAsync()
        {
            try
            {
                if (!Directory.Exists(CaminhoImagensEmail))
                    Directory.CreateDirectory(CaminhoImagensEmail);

                if (!Directory.Exists(CaminhoDocumentos))
                    Directory.CreateDirectory(CaminhoDocumentos);

                if (!Directory.Exists(CaminhoListaTransacoesBitcoin))
                    Directory.CreateDirectory(CaminhoListaTransacoesBitcoin);

                if (!Directory.Exists(CaminhoListaRetornosBitcoin))
                    Directory.CreateDirectory(CaminhoListaRetornosBitcoin);

                if (!Directory.Exists(CaminhoBoletos))
                    Directory.CreateDirectory(CaminhoBoletos);

                if (!Directory.Exists(CaminhoQRCodePIX))
                    Directory.CreateDirectory(CaminhoQRCodePIX);

                if (!Directory.Exists(CaminhoCodigoBarrasBoletos))
                    Directory.CreateDirectory(CaminhoCodigoBarrasBoletos);

                if (!Directory.Exists(CaminhoAnexosEmailConfirmacaoCompra))
                    Directory.CreateDirectory(CaminhoAnexosEmailConfirmacaoCompra);

                if (!Directory.Exists(CaminhoChargebackDocumentosCliente))
                    Directory.CreateDirectory(CaminhoChargebackDocumentosCliente);

                if (!Directory.Exists(CaminhoChargebackDocumentosBandeira))
                    Directory.CreateDirectory(CaminhoChargebackDocumentosBandeira);

                if (!Directory.Exists(CaminhoExtratosBancarios))
                    Directory.CreateDirectory(CaminhoExtratosBancarios);

                if (!Directory.Exists(CaminhoPIXWebhook))
                    Directory.CreateDirectory(CaminhoPIXWebhook);

                if (!Directory.Exists(CaminhoPIXWebhookProcessados))
                    Directory.CreateDirectory(CaminhoPIXWebhookProcessados);

                if (!Directory.Exists(CaminhoArquivoRetornoEDIADIQ))
                    Directory.CreateDirectory(CaminhoArquivoRetornoEDIADIQ);

                if (!Directory.Exists(CaminhoLogSistema))
                    Directory.CreateDirectory(CaminhoLogSistema);

                if (!Directory.Exists(CaminhoLogWebhook))
                    Directory.CreateDirectory(CaminhoLogWebhook);

                if (!Directory.Exists(CaminhoLogHandShake))
                    Directory.CreateDirectory(CaminhoLogHandShake);

                if (!Directory.Exists(CaminhoOrdemPagamento))
                    Directory.CreateDirectory(CaminhoOrdemPagamento);
            }
            catch
            {
                throw new Exception("Erro ao criar diretório");
            }
        }
    }
}