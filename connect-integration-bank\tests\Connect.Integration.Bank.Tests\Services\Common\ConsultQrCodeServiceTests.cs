using Connect.Integration.Bank.Services.Common.Models.Requests;
using Connect.Integration.Bank.Strategy.Interfaces;
using Connect.Integration.Bank.Tests.Fixtures;
using FluentAssertions;

namespace Connect.Integration.Bank.Tests.Services.Common;

public class ConsultQrCodeServiceTests : IClassFixture<ServicesFixture>
{
    private readonly IIntegrationBankService _sut;
    private readonly ServicesFixture _fixture;

    public ConsultQrCodeServiceTests(ServicesFixture fixture)
    {
        _fixture = fixture;
        _sut = fixture.IntegrationBankService;
    }

    [Fact]
    public async Task ShouldExecuteDelBankSuccess()
    {
        // Arrange
        var txId = "9be5503b5da4466eb3dbd65b3ce81fe5";
        var credential = _fixture.CredentialDelBankAccount;

        var request = new ConsultQrCodeRequest(credential, txId);

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        // response.Success.Should().BeTrue();
        // response.Result?.TxId.Should().Be(request.TxId);
    }

    [Fact]
    public async Task ShouldExecuteCelcoinSuccess()
    {
        // Arrange
        var txId = "10fb30446f5d447d92fe83e8b60eec00";
        var credential = _fixture.CredentialCelcoinAccount;

        var request = new ConsultQrCodeRequest(credential, txId);

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();
        response.Result?.TxId.Should().Be(request.TxId);
    }
}
