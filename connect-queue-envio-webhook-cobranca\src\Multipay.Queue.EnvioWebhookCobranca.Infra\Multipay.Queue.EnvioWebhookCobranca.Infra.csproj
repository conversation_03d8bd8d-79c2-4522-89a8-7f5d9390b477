﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.6" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
		<PackageReference Include="MongoDB.Bson" Version="2.27.0" />
		<PackageReference Include="MongoDB.Driver" Version="2.27.0" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Multipay.Queue.EnvioWebhookCobranca.Core\Multipay.Queue.EnvioWebhookCobranca.Core.csproj" />
		<ProjectReference Include="..\Multipay.Service.Criptography\Multipay.Service.Criptography.csproj" />
	</ItemGroup>

</Project>
