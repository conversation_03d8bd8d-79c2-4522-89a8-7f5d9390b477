﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Bus;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Settings;
using Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq.Events;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq.Bus;
internal class PublishRefundPixNotificationRetry : EventBusPublishRabbitMQ, IPublishRefundPixNotificationRetry
{
    public PublishRefundPixNotificationRetry(
        IRabbitMQPersistentConnection persistentConnection,
        ILogger<PublishNotificarClienteRetry> logger, IOptions<RabbitMqSettings> options) : base(
        persistentConnection,
        logger,
        options.Value.Setup.EventBusRetryCount,
        options.Value.RefundPixNotification)
    {}

    public void Publish(RefundPixNotificationEvent @event) => Publish(@event);
}
