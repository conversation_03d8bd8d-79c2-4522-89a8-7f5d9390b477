﻿using Microsoft.AspNetCore.Mvc;
using Multipay.Infrastructure.Plugin.Genial.Services;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Requests;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using System.Globalization;
using static Multipay.Infrastructure.Plugin.Sicoob.Models.Requests.AddUpdateCobrancaRequestModel;

namespace Multipay.Queue.Webhook.Api
{
    public static class MapEndpoints
    {
        public static void ConfigureEndpoints(this WebApplication app)
        {
            _ = app.MapGet("/ping", () => Results.Ok("pong"))
                .WithName("Ping");

            _ = app.MapGet("/genial/qrcode", async ([FromKeyedServices(TipoBanco.Genial)] IEstornarQrCodeService service)
                => Results.Ok(await service
                    .SetIdContaBancariaEmpresa(9)
                    .GenerateDynamicQRCode()
                    ))
                .WithName("Genial-QrCode");

            _ = app.MapPost("/genial/qrcode/return", async (
                ReturnQrCode parameters,
                [FromServices] IEstornarQrCodeService service)
                =>
                {
                    var rs = await service
                        .SetIdContaBancariaEmpresa(9)
                        .SolicitarDevolucao(parameters.PagamentoId, parameters.Valor);
                    return Results.Ok($"{rs.Item1} - {rs.Item2}");
                })
                .WithName("Genial-Return-QrCode");

            _ = app.MapPost("/sicoob/qrcode/return", async (
                ReturnQrCode parameters,
                [FromServices] Multipay.Infrastructure.Plugin.Sicoob.Services.IPixService service)
                =>
            {
                var rs = await service
                    .SolicitarDevolucao(
                     "8cc9941c-3ba8-455d-bf21-4badfb3c1960",
                     "E31872495202405130323s2cEF9Tn7GH",
                     "C3WUBYCUR5SIU9041206331715570542846", new PixSolicitarDevolucaoRequestModel { Valor = .03M });
                return Results.Ok(rs);
            })
                .WithName("Sicoob-Return-QrCode");

            _ = app.MapGet("/sicoob/qrcode", async ([FromServices] Multipay.Infrastructure.Plugin.Sicoob.Services.ICobrancaService service)
                =>
                {
                    var rs = await service.Add(new AddUpdateCobrancaRequestModel
                    {
                        clientId = "8cc9941c-3ba8-455d-bf21-4badfb3c1960",
                        chave = "ce9e1cfc-0b8a-40dc-8eec-3a67e6466730",
                        calendario = new AddUpdateCobrancaRequestModel.Calendario
                        {
                            expiracao = 5 * 60
                        },
                        devedor = new AddUpdateCobrancaRequestModel.Devedor
                        {
                            cpf = "08216707606",
                            nome = "ERICO LUIS BARRIENTOS LEITE"
                        },
                        solicitacaoPagador = "Sol 1",
                        valor = new AddUpdateCobrancaRequestModel.Valor
                        {
                            original = 0.03M.ToString("N2", CultureInfo.GetCultureInfo("en"))
                        },
                        infoAdicionais = new AddUpdateCobrancaRequestModel.Infoadicionai[]
                        {
                            new Infoadicionai{
                                nome = "Info 1",
                                valor = "Info val 1"
                            }
                        }
                    });

                    return Results.Json(rs);
                })
                .WithName("Sicoob-QrCode");
        }

        public record ReturnQrCode(string PagamentoId, decimal Valor);
    }
}