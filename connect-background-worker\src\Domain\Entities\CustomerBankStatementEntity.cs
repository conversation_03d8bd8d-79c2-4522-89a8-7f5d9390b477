namespace Domain.Entities;

using System;

public class CustomerBankStatement
{
    public int IdCliente { get; set; }
    public int IdTipoMovimentoContabil { get; set; }
    public string Descricao { get; set; }
    public double SaldoAnterior { get; set; }
    public double Valor { get; set; }
    public decimal Saldo { get; set; }
    public DateTime DataTransacao { get; set; }
    public int IdBanco { get; set; }
    public int? IdTipoBancariaEmpresa { get; set; }
    public string EndToEnd { get; set; }
    public string ExternalId { get; set; }
    public string IdTransacao { get; set; }

    public CustomerBankStatement(int IdCliente, int IdTipoMovimentoContabil, string Descricao, double SaldoAnterior, double Valor, decimal Saldo, DateTime DataTransacao, int IdBanco, int? IdTipoBancariaEmpresa, string EndToEnd, string ExternalId, string IdTransacao)
    {
        this.IdCliente = IdCliente;
        this.IdTipoMovimentoContabil = IdTipoMovimentoContabil;
        this.Descricao = Descricao;
        this.SaldoAnterior = SaldoAnterior;
        this.Valor = Valor;
        this.Saldo = Saldo;
        this.DataTransacao = DataTransacao;
        this.IdBanco = IdBanco;
        this.IdTipoBancariaEmpresa = IdTipoBancariaEmpresa;
        this.EndToEnd = EndToEnd;
        this.ExternalId = ExternalId;
        this.IdTransacao = IdTransacao;
    }
}

