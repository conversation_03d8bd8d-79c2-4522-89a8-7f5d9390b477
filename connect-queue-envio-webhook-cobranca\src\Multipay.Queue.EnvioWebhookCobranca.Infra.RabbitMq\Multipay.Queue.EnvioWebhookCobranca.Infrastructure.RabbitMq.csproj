﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
	  <PackageReference Include="Multipay.RabbitMQExtension" Version="1.0.18" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Multipay.Queue.EnvioWebhookCobranca.Core\Multipay.Queue.EnvioWebhookCobranca.Core.csproj" />
    <ProjectReference Include="..\Multipay.Service.Criptography\Multipay.Service.Criptography.csproj" />
  </ItemGroup>

</Project>
