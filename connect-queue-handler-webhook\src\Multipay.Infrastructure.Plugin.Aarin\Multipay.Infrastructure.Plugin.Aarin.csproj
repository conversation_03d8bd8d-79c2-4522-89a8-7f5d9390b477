﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>



	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Multipay.Queue.Webhook.Core.Commons\Multipay.Queue.Webhook.Core.Commons.csproj" />
		<ProjectReference Include="..\Multipay.Queue.Webhook.Infrastructure.DAPetapoco\Multipay.Queue.Webhook.Infrastructure.DAPetapoco.csproj" />
		<ProjectReference Include="..\Multipay.Service.Criptography\Multipay.Service.Criptography.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="Utils\Aarin.pfx">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="Utils\aarin_homologacao.pfx">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="Utils\certificado_22443203000195.pfx">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
