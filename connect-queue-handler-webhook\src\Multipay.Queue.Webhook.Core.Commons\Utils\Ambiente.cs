﻿using Microsoft.Extensions.Configuration;

namespace Multipay.Queue.Webhook.Core.Commons.Utils
{
    public class Ambiente
    {
        private readonly IConfiguration configuration;

        public Ambiente(IConfiguration configuration)
        {
            this.configuration = configuration;
        }

        public string? Tema => Environment.GetEnvironmentVariable("TEMA") ?? configuration["Tema"];
    }
}