﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.6.33829.357
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "1- Presentation", "1- Presentation", "{011F4F6B-23FC-44DE-B437-C780D58D1EE5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookSaque.Api", "src\Multipay.Queue.EnvioWebhookSaque.Api\Multipay.Queue.EnvioWebhookSaque.Api.csproj", "{4FA78086-808F-42B9-ADCE-9469EDCEA936}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "4- Infrastructure", "4- Infrastructure", "{37E11664-94AA-484D-8E9E-E351F297AD6C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq", "src\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj", "{563DC971-40D5-4F62-88FE-5157DBB81B62}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "3- Business", "3- Business", "{2C5B81F2-117E-4C98-9C68-F547626F18D1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookSaque.Application", "src\Multipay.Queue.EnvioWebhookSaque.Application\Multipay.Queue.EnvioWebhookSaque.Application.csproj", "{A76D0A97-85F8-49D9-8B91-7DAF360D08DF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "2- Core", "2- Core", "{30AC3B1D-899A-418F-968C-A88BFF0A4A4E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookSaque.Core", "src\Multipay.Queue.EnvioWebhookSaque.Core\Multipay.Queue.EnvioWebhookSaque.Core.csproj", "{0D713653-386C-4093-846E-3549EA15B9EC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco", "src\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj", "{BB63BB1F-6CDC-48D8-BC86-FA71EC8B34C1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookSaque.Infrastructure", "src\Multipay.Queue.EnvioWebhookSaque.Infrastructure\Multipay.Queue.EnvioWebhookSaque.Infrastructure.csproj", "{67ECE59B-5BC7-49D6-B1BC-CB7A57D0C17F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Service.Criptography", "src\Multipay.Service.Criptography\Multipay.Service.Criptography.csproj", "{012AD7C0-6E12-4F5C-8185-D3FBE97615EB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4FA78086-808F-42B9-ADCE-9469EDCEA936}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4FA78086-808F-42B9-ADCE-9469EDCEA936}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4FA78086-808F-42B9-ADCE-9469EDCEA936}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4FA78086-808F-42B9-ADCE-9469EDCEA936}.Release|Any CPU.Build.0 = Release|Any CPU
		{563DC971-40D5-4F62-88FE-5157DBB81B62}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{563DC971-40D5-4F62-88FE-5157DBB81B62}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{563DC971-40D5-4F62-88FE-5157DBB81B62}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{563DC971-40D5-4F62-88FE-5157DBB81B62}.Release|Any CPU.Build.0 = Release|Any CPU
		{A76D0A97-85F8-49D9-8B91-7DAF360D08DF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A76D0A97-85F8-49D9-8B91-7DAF360D08DF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A76D0A97-85F8-49D9-8B91-7DAF360D08DF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A76D0A97-85F8-49D9-8B91-7DAF360D08DF}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D713653-386C-4093-846E-3549EA15B9EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D713653-386C-4093-846E-3549EA15B9EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D713653-386C-4093-846E-3549EA15B9EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D713653-386C-4093-846E-3549EA15B9EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB63BB1F-6CDC-48D8-BC86-FA71EC8B34C1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB63BB1F-6CDC-48D8-BC86-FA71EC8B34C1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB63BB1F-6CDC-48D8-BC86-FA71EC8B34C1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB63BB1F-6CDC-48D8-BC86-FA71EC8B34C1}.Release|Any CPU.Build.0 = Release|Any CPU
		{67ECE59B-5BC7-49D6-B1BC-CB7A57D0C17F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67ECE59B-5BC7-49D6-B1BC-CB7A57D0C17F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67ECE59B-5BC7-49D6-B1BC-CB7A57D0C17F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67ECE59B-5BC7-49D6-B1BC-CB7A57D0C17F}.Release|Any CPU.Build.0 = Release|Any CPU
		{012AD7C0-6E12-4F5C-8185-D3FBE97615EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{012AD7C0-6E12-4F5C-8185-D3FBE97615EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{012AD7C0-6E12-4F5C-8185-D3FBE97615EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{012AD7C0-6E12-4F5C-8185-D3FBE97615EB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{4FA78086-808F-42B9-ADCE-9469EDCEA936} = {011F4F6B-23FC-44DE-B437-C780D58D1EE5}
		{563DC971-40D5-4F62-88FE-5157DBB81B62} = {37E11664-94AA-484D-8E9E-E351F297AD6C}
		{A76D0A97-85F8-49D9-8B91-7DAF360D08DF} = {2C5B81F2-117E-4C98-9C68-F547626F18D1}
		{0D713653-386C-4093-846E-3549EA15B9EC} = {30AC3B1D-899A-418F-968C-A88BFF0A4A4E}
		{BB63BB1F-6CDC-48D8-BC86-FA71EC8B34C1} = {37E11664-94AA-484D-8E9E-E351F297AD6C}
		{67ECE59B-5BC7-49D6-B1BC-CB7A57D0C17F} = {37E11664-94AA-484D-8E9E-E351F297AD6C}
		{012AD7C0-6E12-4F5C-8185-D3FBE97615EB} = {37E11664-94AA-484D-8E9E-E351F297AD6C}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BF21034F-1E5E-4E3F-B5F4-F3267F7527CD}
	EndGlobalSection
EndGlobal
