﻿using Multipay.Queue.EnvioWebhookSaque.Core.Enums;

namespace Multipay.Queue.EnvioWebhookSaque.Core.Extensions
{
    public static class GenericExtension
    {
        public static (string tipoChave, string chavePIX) NormalizarChavePix(TipoChavePixEnum tipoChaveEnum, string chavePix)
        {
            var tipoChave = string.Empty;
            var chavePixResult = string.Empty;

            if (string.IsNullOrEmpty(chavePix))
                throw new Exception("Chave PIX não pode estar vazia.");

            switch (tipoChaveEnum)
            {
                case TipoChavePixEnum.CPFCNPJ:
                    tipoChave = chavePix.Length == 11 ? "CPF" : "CNPJ";
                    break;

                case TipoChavePixEnum.TELEFONE:
                    tipoChave = "PHONE";
                    break;

                case TipoChavePixEnum.EMAIL:
                    tipoChave = "EMAIL";
                    break;

                case TipoChavePixEnum.EVP:
                    tipoChave = "EVP";
                    break;
            }

            if (tipoChave == "PHONE")
            {
                chavePixResult = chavePix.Replace("+55", "").ToOnlyNumbers();

                if (chavePixResult.Length != 11)
                    throw new Exception("Chave PIX inválida.");

                chavePixResult = "+55" + chavePixResult;

                return (tipoChave, chavePixResult);
            }

            if (tipoChave == "CPF" || tipoChave == "CNPJ")
                chavePixResult = chavePix.ToOnlyNumbers();

            return (tipoChave, chavePixResult);
        }

        public static string ToOnlyNumbers(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return value;
            return string.Join("", System.Text.RegularExpressions.Regex.Split(value, @"[^\d]"));
        }

        public static string ToJson<T>(this T source, bool writeIndented = false)
        {
            var options = new System.Text.Json.JsonSerializerOptions { WriteIndented = writeIndented };

            return System.Text.Json.JsonSerializer.Serialize(source, options);
        }

        public static T TryDeserialize<T>(this string serializedObject)
        {
            try
            {
                return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(serializedObject)!;
            }
            catch (Exception)
            {
                throw new Exception($"Não foi possível tratar este objeto: {serializedObject}");
            }
        }
    }
}