﻿using System.ComponentModel;
using System.Reflection;

namespace Multipay.Queue.EnvioWebhookCobranca.Core.Models;

public enum EventosCriticos
{
    [Description("Envio de Webhook ao Cliente")]
    EV4001,

    [Description("Erros Não Tratados")]
    EV9999
}

public static class EnumExtensions
{
    public static string Description(this Enum value)
    {
        FieldInfo fieldInfo = value.GetType().GetField(value.ToString())!;
        DescriptionAttribute[] attributes = (DescriptionAttribute[])fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false);

        if (attributes != null && attributes.Length > 0)
        {
            return $"{value}: {attributes[0].Description}";
        }
        else
        {
            return value.ToString();
        }
    }
}
