using Application.Configurations;
using Application.Services.Interfaces;
using Domain.Constants;
using MediatR;
using Microsoft.AspNetCore.Http;
using static Microsoft.AspNetCore.Http.Results;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Application.Handlers.Charge;

public class ChargeHandler
(
    ILogger<ChargeHandler> logger,
    ICredentialService credentialService,
    IChargeService chargeService,
    IOptions<ConnectPSPConfiguration> configuration) : IRequestHandler<ChargeCommand, IResult>
{
    public async Task<IResult> Handle(ChargeCommand request, CancellationToken cancellationToken)
    {
        var customerId = configuration.Value.CustomerId;

        var credentialResult = await credentialService.GetCredentialByCustomerId(customerId, cancellationToken);
        if (!credentialResult.Success)
        {
            logger.Error("Dados da credencial não encontrados para o cliente Id: {CustomerId}",  customerId);
            return NotFound(credentialResult.ErrorMessage);
        }
        var credential = credentialResult.Value!;

        var chargeResult = await chargeService.ExecuteChargeAsync(credential, cancellationToken);
        if (!chargeResult.Success)
        {
            logger.Error("Erro ao executar api de charge para o cliente Id: {CustomerId}",  customerId);
            return BadGateway(MessagesConstants.ErrorExcutingCharge);
        }

        logger.Information("Charge executado com sucesso");
        
        return Ok();
    }
}
