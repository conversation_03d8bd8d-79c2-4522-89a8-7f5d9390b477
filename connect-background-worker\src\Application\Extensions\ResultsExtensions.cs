using System.Net;
using Microsoft.AspNetCore.Http;

namespace Application.Extensions;

public static class ResultsExtensions
{
    public static IResult BadGateway( 
        string? detail = null,
        string? instance = null,        
        string? title = null,
        string? type = null,
        IEnumerable<KeyValuePair<string, object?>>? extensions = null) => Results.Problem(detail, instance, (int) HttpStatusCode.BadGateway, title, type, extensions);
}
