﻿using Multipay.Queue.EnvioWebhookCobranca.Core.Models;

namespace Multipay.Queue.EnvioWebhookCobranca.Core.Interfaces
{
    public interface ICobrancaService
    {
        Task AdicionarPagadorCobranca(int idCobranca, string cpfPagadorPix, string nomePagadorPix, string endToEndId);

        Task AtualizarEndToEndPorTxId(string txId, string endToEndId);

        Task ConfirmarPagamentoBRL(int idCobranca, int idCliente, byte pIX, decimal valorPago1, int numeroParcelas, string descricao, decimal valorPago2, decimal porcentagemTaxa, decimal valorTarifa, int horasResgate, string txIdPIX);

        ValueTask<CobrancaModel?> ObterCobranca(string txId);

        ValueTask<CobrancaModel?> ObterCobrancaPorCodigo(string customId);

        Task<PessoaCobrancaModel> ObterPessoaCobranca(int idCobranca);

        Task<bool> HasPrimeiraCobrancaPorParametros(int id, int idCliente, int idPessoaCobranca);

        Task ConfirmarEnvioConfirmacaoCliente(int idCobranca);

        Task ConfirmarEnvioAdicionalConfirmacaoId(int IdMovimentacaoWebhooksAdicionais);

        ValueTask<List<MovimentacaoWebhooksAdicionaisModel>> ObterWebhooksAdicionaisPorId(int IdCobranca);

        Task<string> ObterNomeClientePorId(int idCobranca);
    }
}