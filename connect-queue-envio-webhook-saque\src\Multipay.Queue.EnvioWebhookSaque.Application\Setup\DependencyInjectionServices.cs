﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Multipay.Queue.EnvioWebhookSaque.Application.Services;
using Multipay.Queue.EnvioWebhookSaque.Core.Utils;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Setup;
using Multipay.Service.Criptography;

namespace Multipay.Queue.EnvioWebhookSaque.Application.Setup
{
    public static class DependencyInjectionServices
    {
        public static void AddDependencyInjectionApplicationServices(this IServiceCollection services, IConfiguration configuration, CriptoService criptoService)
        {
            var sp = services.BuildServiceProvider();
            using var scope = sp.CreateScope();
            var ambienteHelp = scope.ServiceProvider.GetRequiredService<AmbienteHelp>();

            services.AddDependencyInjectionApplicationRepositoriesMongo(configuration, criptoService);

            var cnn = ambienteHelp.SqlServer_ConnectionString;

            if (string.IsNullOrWhiteSpace(cnn))
                cnn = configuration.GetConnectionString("MultiPayDatabase")!;

            services.AddDistributedSqlServerCache(options =>
            {
                options.ConnectionString = cnn;
                options.SchemaName = "dbo";
                options.TableName = "DistributedCache";
            });

            services.AddScoped<INotiticarClienteSaqueConcluidoService, NotiticarClienteSaqueConcluidoService>();
            services.AddScoped<INotificarClientePublishNotificationService, NotificarClientePublishNotificationService>();
            services.AddScoped<ISolicitacaoSaquesEstornadoNotificarClienteService, SolicitacaoSaquesEstornadoNotificarClienteService>();
        }
    }
}