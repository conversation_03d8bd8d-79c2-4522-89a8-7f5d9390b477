﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Multipay.Queue.EnvioWebhookSaque.Core.Utils;
using Multipay.Service.Criptography;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.Setup
{
    public static class DependencyInjectionRepositories
    {
        public static void AddDependencyInjectionApplicationRepositoriesPetapoco(this IServiceCollection services, ConfigurationManager configuration, CriptoService criptoService)
        {
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            services.AddScoped<IDatabase>(servicesProvider =>
            {
                var sp = services.BuildServiceProvider();
                using var scope = sp.CreateScope();
                var ambienteHelp = scope.ServiceProvider.GetRequiredService<AmbienteHelp>();

                var cnn = ambienteHelp.SqlServer_ConnectionString;

                if (string.IsNullOrWhiteSpace(cnn))
                    cnn = configuration.GetConnectionString("MultiPayDatabase")!;

                var start = cnn.IndexOf("password=") + 9;
                var end = cnn.IndexOf(";", start);

                var password = cnn.Substring(start, end - start);
                var encryptedPassword = password;

                password = criptoService.Decrypt(password).Result;

                cnn = cnn.Replace(encryptedPassword, password);

                return DatabaseConfiguration
                    .Build()
                    .UsingProvider<PetaPoco.Providers.SqlServerDatabaseProvider>()
                    .UsingConnectionString(cnn)
                    .Create();
            });

            services.AddDataBaseConfigurationProvider(configuration);
        }

        private static void AddDataBaseConfigurationProvider(this IServiceCollection services, ConfigurationManager configuration)
        {
            var sp = services.BuildServiceProvider();
            using var scope = sp.CreateScope();
            var ambienteHelp = scope.ServiceProvider.GetRequiredService<AmbienteHelp>();

            var database = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            string tema = ambienteHelp.Tema ?? throw new ArgumentNullException("Tema não localizado em AppSettings");

            configuration.Sources.Add(new DataBaseConfigurationSource(database, tema));
        }
    }
}