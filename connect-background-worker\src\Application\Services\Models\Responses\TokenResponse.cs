using System.Text.Json.Serialization;

namespace Application.Services.Models.Responses;

public class TokenResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; }

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; }

    [JsonPropertyName("token_type")]
    public string TokenType { get; }

    public TokenResponse(string accessToken, int expiresIn, string tokenType)
    {
        AccessToken = accessToken;
        ExpiresIn = expiresIn;
        TokenType = tokenType;
    }
}
