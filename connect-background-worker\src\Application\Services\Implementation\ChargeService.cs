using Application.Services.Interfaces;
using Application.Services.Interfaces.Api;
using Application.Services.Models.Requests;
using Domain.Extensions;
using Domain.Models;
using Microsoft.Extensions.Logging;
using ResultT;
using static ResultT.Results;

namespace Application.Services.Implementation;

public class ChargeService(ILogger<ChargeService> logger, IConnectApi connectApi) : IChargeService
{
    public async Task<Result> ExecuteChargeAsync(CustomerCredential credential, CancellationToken cancellationToken)
    {
        var authorization = credential.GetAuthorization();
        var request = new ChargeRequest();

        var response = await connectApi.PostChargeAsync(authorization, request, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            logger.Error("Erro ao executar charge: ({Status}): {Response}", response.StatusCode, $"{response.Error?.Content}");
            return Error();
        }

        return Ok();
    }
}
