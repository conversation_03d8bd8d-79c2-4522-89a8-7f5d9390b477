using System.Net;
using System.Runtime.CompilerServices;
using Application.Services.Models.Requests;
using Application.Services.Models.Responses;
using Microsoft.AspNetCore.Mvc;
using Refit;

namespace Application.Services.Interfaces.Api;

public interface IConnectApi
{
    [Post("/financial/rebalance")]
    Task<IApiResponse<RebalanceResponse>> PostRebalanceAsync(
        [Header(nameof(Authorization))] string authorization,
        [Body] RebalanceRequest request,
        CancellationToken cancellationToken);

    [Post("/financial/charge")]
    Task<IApiResponse> PostChargeAsync(
        [Header(nameof(Authorization))] string authorization,
        [Body] ChargeRequest request,
        CancellationToken cancellationToken);

    [Get("/financial/monitorarsaldos")]
    Task<IApiResponse> GetBankBalanceAsync(
        [Header(nameof(Authorization))] string authorization,
        [Body] BankBalanceRequest request, 
        CancellationToken cancellationToken);


    [Get("/financial/ExtratoBanco")]
    Task<IApiResponse<List<BankStatement>>> GetBankStatementAsync(
        [Header(nameof(Authorization))] string authorization,
        [Query] BankStatementRequest request,
        [Body] BankStatementRequest requestBody,
        CancellationToken cancellationToken);

}
