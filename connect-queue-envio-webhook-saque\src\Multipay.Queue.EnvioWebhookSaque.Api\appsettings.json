{"Tema": "ConnectPsp", "NameApp": "queue-envio-webhook-saque", "DataDog": {"ApiKey": "********************************", "Url": "https://http-intake.logs.datadoghq.com"}, "SlidingExpiration": 30, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Datadog.Logs"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Information", "AWSSDK": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "DatadogLogs", "Args": {"apiKey": "********************************", "source": "csharp", "host": "Multipay.Queue.EnvioWebhookSaque", "service": "Notificacao-Cliente-ConnectPSP", "tags": ["multipay-queue-enviowebhook-saque:production"], "logLevel": "Information", "configuration": {"url": "https://http-intake.logs.datadoghq.com", "port": 443}}}], "Enrich": ["FromLogContext", "WithThreadId", "WithMachineName"], "Properties": {"Application": "multipay-queue-enviowebhook-saque"}}, "RabbitMqSettings": {"Setup": {"EventBusPort": 5672, "EventBusRetryCount": "5"}, "NotificarSaqueCliente": {"Publish": {"Exchange": "notificar-saque-cliente", "ExchangeType": "fanout"}, "Consumer": {"Exchange": "notificar-saque-cliente", "QueueName": "notificar-saque-cliente", "DeadLetter": {"Count": 5}, "PrefetchCount": 1}}, "NotificarClienteRetry": {"Publish": {"Exchange": "notificar-saque-cliente-retry", "ExchangeType": "fanout"}}, "NotificarSaqueClientePublishNotification": {"Consumer": {"Exchange": "notificar-saque-cliente", "QueueName": "notificar-saque-cliente-publish-notification", "DeadLetter": {"Count": 5}}}, "SolicitacaoSaquesEstornado": {"Publish": {"Exchange": "solicitacao-saques-estornado", "ExchangeType": "fanout"}, "Consumer": {"Exchange": "solicitacao-saques-estornado", "QueueName": "solicitacao-saques-estornado-notificar-cliente", "DeadLetter": {"Count": 5}}}, "SolicitacaoSaquesEstornadoRetry": {"Publish": {"Exchange": "solicitacao-saques-estornado-retry", "ExchangeType": "fanout"}}, "SolicitacaoSaques": {"Consumer": {"Exchange": "solicitacao-saques", "QueueName": "solicitacao-saques", "DeadLetter": {"Exchange": "solicitacao-saques-error", "QueueName": "solicitacao-saques-error", "RoutingKey": "solicitacao-saques-error", "Count": 3}}, "Publish": {"Exchange": "solicitacao-saques", "ExchangeType": "fanout"}}}, "Bancos": {"BMP": {"Url": "https://api.ext.pix.dbs.moneyp.com.br/", "UrlInterno": "https://api.ext.dbs.moneyp.com.br", "UrlAuthentication": "https://auth.moneyp.com.br/", "GrantType": "client_credentials", "Scopes": {"Transferir": "api.pix api.recurso.transferir", "TransferirEntreContas": "api.ext api.transf.enviar", "ConsultarChave": "api.pix api.chave.consultar"}, "ClientId": "dbs.api.ext.infinity", "ClientAssertionType": "urn:ietf:params:oauth:client-assertion-type:jwt-bearer", "Audience": "https://auth.moneyp.com.br/connect/token"}, "Delbank": {"ApiKey": "PJPyJ2xGmyB9oDHyNIUwNOt1dgpgolBwcE16ybaKD5pqVEGTBg/p+APpf4ALsiVmjdB8Qh1tgmKPCyx1kSOz7Hd9IsepYPj6a0odInh0gT5hycP6CbnVo7+9TxvjnCtS", "ApiKeyIndirectPix": "3c6eb078b6188042$8c9aad1e0c0bd5fc147edbbbe0427376.ce4741fe0659401fb595a1f21eb302cd", "PartnerKey": "c4ca4238a0b923820dcc509a6f75849b", "BaasUrl": "https://apisandbox.delbank.com.br/baas/api/", "BaseUrl": "https://apisandbox.delbank.com.br/", "IndirectPixUrl": "https://apisandbox.delbank.com.br/indirect-pix/api/", "WhiteLabelUrl": "https://whitelabel-sandbox.delbank.dev.br/"}, "BS2": {"ChavePix": "d0e44cce-7a65-4bd1-958e-c9402f6f356b", "CodigoCarteira": "21", "CodigoCedente": "261726", "RefreshToken": "8cffd91f-9141-4a92-aa83-0edb9df78c2a", "Secret": "f4658631b33446589ba9b3e78efa947a", "Scope": "saldo extrato pagamento transferencia boleto cobv.write cobv.read comprovante webhook-mov-conta aprovacoes pagamento-tributo webhook-conclusao-transf webhook-conclusao-pag cob.write cob.read pix.write pix.read dict.write dict.read webhook.read webhook.write", "Token": "l7d6501ffc279845a6af1ebc0dce10b0a4", "Url": "https://apihmz.bancobonsucesso.com.br/", "UrlAuthentication": "https://apihmz.bancobonsucesso.com.br/"}, "Genial": {"AccountName": "Anspace Intermediacao e Agenciamento de Negocios LTDA", "IspGenial": "********", "Url": "https://genial-baas-anspace.parceiros.api.genial.systems", "UrlAuthentication": "https://genial-arquitetura-authentication.production.api.genial.systems", "UrlMovimentacao": "https://genial-central-movimentacoes.production.api.genial.systems", "XOrigem": "ANSPACE-API"}}}