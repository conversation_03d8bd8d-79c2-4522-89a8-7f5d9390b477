﻿namespace Multipay.Queue.Webhook.Core.Entities;
public class CustomerOutputAccountingStatementEntity
{
    public int CustomerExitId { get; set; }
    public int CustomerAccountingStatementId { get; set; }

    public CustomerOutputAccountingStatementEntity()
    {
        
    }

    public CustomerOutputAccountingStatementEntity(int customerExitId, int customerAccountingStatementId)
    {
        CustomerExitId = customerExitId;
        CustomerAccountingStatementId = customerAccountingStatementId;
    }
}
