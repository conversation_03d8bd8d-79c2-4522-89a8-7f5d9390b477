{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Multipay.Queue.EnvioWebhookCobranca.Core/1.0.0": {"dependencies": {"Datadog.Trace": "2.51.0", "Datadog.Trace.Bundle": "2.51.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Serilog": "4.0.0", "Serilog.Enrichers.Environment": "3.0.1", "Serilog.Enrichers.Thread": "4.0.0", "Serilog.Exceptions": "8.4.0", "Serilog.Exceptions.SqlServer": "8.4.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Datadog.Logs": "0.5.2", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.Map": "1.0.2"}, "runtime": {"Multipay.Queue.EnvioWebhookCobranca.Core.dll": {}}}, "Datadog.Trace/2.51.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "4.4.1"}, "runtime": {"lib/net6.0/Datadog.Trace.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Datadog.Trace.Bundle/2.51.0": {"dependencies": {"Datadog.Trace": "2.51.0"}}, "Microsoft.Extensions.Configuration/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Options/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Serilog/4.0.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Environment/3.0.1": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Thread/4.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Exceptions/8.4.0": {"dependencies": {"Serilog": "4.0.0", "System.Reflection.TypeExtensions": "4.7.0"}, "runtime": {"lib/net6.0/Serilog.Exceptions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.0.0"}}}, "Serilog.Exceptions.SqlServer/8.4.0": {"dependencies": {"Serilog.Exceptions": "8.4.0", "System.Data.SqlClient": "4.8.1"}, "runtime": {"lib/net6.0/Serilog.Exceptions.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.0.0"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.Datadog.Logs/0.5.2": {"dependencies": {"Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1", "Serilog": "4.0.0", "Serilog.Sinks.PeriodicBatching": "3.0.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Datadog.Logs.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Map/1.0.2": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Map.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.PeriodicBatching/3.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Data.SqlClient/4.8.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}}, "System.Diagnostics.DiagnosticSource/4.4.1": {}, "System.Reflection.TypeExtensions/4.7.0": {}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}}}, "libraries": {"Multipay.Queue.EnvioWebhookCobranca.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Datadog.Trace/2.51.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/i0vbMWswn/Ru150lj3JkUi7xCUhKNIRJ+LVv+reiP12+P76OqrmMyZLLVCT/t7ZkTfZ5IITR7gMl5hHO+wpg==", "path": "datadog.trace/2.51.0", "hashPath": "datadog.trace.2.51.0.nupkg.sha512"}, "Datadog.Trace.Bundle/2.51.0": {"type": "package", "serviceable": true, "sha512": "sha512-vLdX7rukUbpLErH4C+Usfox0oMhNq0RLSG2l0C3meAJalKkqe/KC3NUD3nCKGEVWp/hyLto897hxgChUAc/bJA==", "path": "datadog.trace.bundle/2.51.0", "hashPath": "datadog.trace.bundle.2.51.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LjVKO6P2y52c5ZhTLX/w8zc5H4Y3J/LJsgqTBj49TtFq/hAtVNue/WA0F6/7GMY90xhD7K0MDZ4qpOeWXbLvzg==", "path": "microsoft.extensions.configuration/2.1.1", "hashPath": "microsoft.extensions.configuration.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-fcLCTS03poWE4v9tSNBr3pWn0QwGgAn1vzqHXlXgvqZeOc7LvQNzaWcKRQZTdEc3+YhQKwMsOtm3VKSA2aWQ8w==", "path": "microsoft.extensions.configuration.binder/2.1.1", "hashPath": "microsoft.extensions.configuration.binder.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MgYpU5cwZohUMKKg3sbPhvGG+eAZ/59E9UwPwlrUkyXU+PGzqwZg9yyQNjhxuAWmoNoFReoemeCku50prYSGzA==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.1.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Options/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-V7lXCU78lAbzaulCGFKojcCyG8RTJicEbiBkPJjFqiqXwndEBBIehdXRMWEVU3UtzQ1yDvphiWUL9th6/4gJ7w==", "path": "microsoft.extensions.options/2.1.1", "hashPath": "microsoft.extensions.options.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-NpGh3Y/VOBs6hvjKHMsdbtrvGvMO+cBqZ7YT/Rc4iFy0C4ogSnl1lBAq69L1LS6gzlwDBZDZ7WcvzSDzk5zfzA==", "path": "microsoft.extensions.options.configurationextensions/2.1.1", "hashPath": "microsoft.extensions.options.configurationextensions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Serilog/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2jDkUrSh5EofOp7Lx5Zgy0EB+7hXjjxE2ktTb1WVQmU00lDACR2TdROGKU0K1pDTBSJBN1PqgYpgOZF8mL7NJw==", "path": "serilog/4.0.0", "hashPath": "serilog.4.0.0.nupkg.sha512"}, "Serilog.Enrichers.Environment/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9BqCE4C9FF+/rJb/CsQwe7oVf44xqkOvMwX//CUxvUR25lFL4tSS6iuxE5eW07quby1BAyAEP+vM6TWsnT3iqw==", "path": "serilog.enrichers.environment/3.0.1", "hashPath": "serilog.enrichers.environment.3.0.1.nupkg.sha512"}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "path": "serilog.enrichers.thread/4.0.0", "hashPath": "serilog.enrichers.thread.4.0.0.nupkg.sha512"}, "Serilog.Exceptions/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-nc/+hUw3lsdo0zCj0KMIybAu7perMx79vu72w0za9Nsi6mWyNkGXxYxakAjWB7nEmYL6zdmhEQRB4oJ2ALUeug==", "path": "serilog.exceptions/8.4.0", "hashPath": "serilog.exceptions.8.4.0.nupkg.sha512"}, "Serilog.Exceptions.SqlServer/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-8VGHQjHabgyEOrY0pP6LzkFY2dvavV44Bu8jZD5XN8mxC/CvoeothQdzmVp9E62D1YOO1oJJVkTbHEjeRvkJXw==", "path": "serilog.exceptions.sqlserver/8.4.0", "hashPath": "serilog.exceptions.sqlserver.8.4.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Datadog.Logs/0.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-VOH2rbU+9Cx9GUUOUv2wUyMZSzB2mC/QxkE08ByfAptYoy2UI4pz2LCq5ynGuJYNsozhB6EejvuJj3lT64iUmw==", "path": "serilog.sinks.datadog.logs/0.5.2", "hashPath": "serilog.sinks.datadog.logs.0.5.2.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Map/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JbPBAeD5hxUQw8TZg3FlOnqVsSu1269nvqFm5DQ7hc+AmsB+hItl+zMSDphMbPJXjL8KdpMRSWNkGi7zTKRmCA==", "path": "serilog.sinks.map/1.0.2", "hashPath": "serilog.sinks.map.1.0.2.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rgXWamyu3AMpswhJs8T/c5OKRhLDWYNt6it/6q328pl5K8rBLv79lZ9qJiYksoClnZNw9/omNfSFZ4RuH6qx9Q==", "path": "serilog.sinks.periodicbatching/3.0.0", "hashPath": "serilog.sinks.periodicbatching.3.0.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "path": "system.data.sqlclient/4.8.1", "hashPath": "system.data.sqlclient.4.8.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-U/KcC19fyLsPN1GLmeU2zQq15MMVcPwMOYPADVo1+WIoJpvMHxrzvl+BLLZwTEZSneGwaPFZ0aWr0nJ7B7LSdA==", "path": "system.diagnostics.diagnosticsource/4.4.1", "hashPath": "system.diagnostics.diagnosticsource.4.4.1.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "path": "system.reflection.typeextensions/4.7.0", "hashPath": "system.reflection.typeextensions.4.7.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}}}