﻿namespace Multipay.Queue.Webhook.Core.Entities;
public class CustomerAccountingStatementEntity
{
    public int Id { get; set; }
    public int CustomerId { get; set; }
    public byte AccountingMovementTypeId { get; set; }
    public byte CoinId { get; set; }
    public string? Description { get; set; }
    public decimal LastBalance { get; set; }
    public decimal Value { get; set; }
    public decimal Balance { get; set; }
    public DateTime TransactionDate { get; set; }

}
