name: Deploy to ECR connect-queue-envio-webhook-saque

on:
  push:
    branches:
      - release
      - main

jobs:

  build-hml:
    name: Build Image Release
    runs-on: ubuntu-latest    
    if: github.ref == 'refs/heads/release'
    steps:

    - name: Declare variables
      id: vars
      shell: bash
      run: |
        echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
        echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Check vars
      run: |
        echo "Branch: ${{ steps.vars.outputs.branch }}"
        echo "Sha: ${{ steps.vars.outputs.sha_short }}"

    - name: Check out code
      uses: actions/checkout@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.ACCESS_KEY_HML }}
        aws-secret-access-key: ${{ secrets.SECRET_KEY_HML }}
        aws-region: us-east-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build docker #, tag, and push image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: connect-queue-envio-saque
        IMAGE_TAG: ${{ steps.vars.outputs.branch }}-${{ github.sha }}

      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:build-$IMAGE_TAG -f src/Multipay.Queue.EnvioWebhookSaque.Api/Dockerfile .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:build-$IMAGE_TAG


  build-prod:
    name: Build Image PRD
    runs-on: ubuntu-latest    
    if: github.ref == 'refs/heads/main'
    steps:

    - name: Declare variables
      id: vars
      shell: bash
      run: |
        echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
        echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Check vars
      run: |
        echo "Branch: ${{ steps.vars.outputs.branch }}"
        echo "Sha: ${{ steps.vars.outputs.sha_short }}"

    - name: Check out code
      uses: actions/checkout@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.SECRET_KEY }}
        aws-region: us-east-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build docker #, tag, and push image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: connect-queue-envio-saque
        IMAGE_TAG: ${{ steps.vars.outputs.branch }}-${{ github.sha }}

      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:build-$IMAGE_TAG -f src/Multipay.Queue.EnvioWebhookSaque.Api/Dockerfile .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:build-$IMAGE_TAG
      

  

  Deploy:

    name: update-manifest
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.ref_name }}
    needs: ['build-hml','build-prod']   
    if: ${{ success() || contains(needs.*.result, 'success') }}   
    steps:

    - name: Declare variables
      id: vars
      shell: bash
      run: |
        echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
        echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Check vars
      run: |
        echo "Branch: ${{ steps.vars.outputs.branch }}"
        echo "Sha: ${{ steps.vars.outputs.sha_short }}"

    - name: Checkout configs
      uses: MrSquaare/ssh-setup-action@v1
      with:
          host: github.com
          private-key: ${{ secrets.PRIVATE_KEY }}
            

    - name: Clone repository
      run: |
        <NAME_EMAIL>:ConnectPSP/connect-gitops.git
        git config --global user.email "<EMAIL>"
        git config --global user.name "Pipeline"

        cd connect-gitops/apps/connect-queue-envio-saque/
        echo ${{ github.sha }}
        sed -i -e 's/build[0-9]*\-.*/'build-${{ steps.vars.outputs.branch }}-${{ github.sha }}'/g' values-${{ steps.vars.outputs.branch }}.yaml
        git add *
        git commit -m "Update image for tag - connect-queue-envio-saque-${{ steps.vars.outputs.branch }}-${{ github.sha }}  values-${{ steps.vars.outputs.branch }}.yaml "
        echo ${{ steps.vars.outputs.branch }}-${{ github.sha }}
        git push
