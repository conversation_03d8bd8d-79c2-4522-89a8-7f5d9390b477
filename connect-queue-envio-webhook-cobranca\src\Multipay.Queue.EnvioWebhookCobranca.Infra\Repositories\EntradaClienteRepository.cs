﻿using Microsoft.EntityFrameworkCore;
using Multipay.Queue.EnvioWebhookCobranca.Core.Constants;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Data;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Repositories
{
    public interface IEntradaClienteRepository
    {
        Task<int> InserirRecebimentoOperacaoBancaria(int idRecebimento, byte idOperacaoServico, int idCliente, string descricao, decimal valor, TaxaClienteOperacaoModel? taxaRecebimento);
    }

    internal class EntradaClienteRepository : IEntradaClienteRepository
    {
        private readonly DbContext dbContext;

        public EntradaClienteRepository(DbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public async Task<int> InserirRecebimentoOperacaoBancaria(
            int idRecebimentoOperacaoBancaria,
            byte idOperacaoServico,
            int idCliente,
            string descricao,
            decimal valor,
            TaxaClienteOperacaoModel? taxaRecebimento)
        {
            var porcentagemTaxa = taxaRecebimento?.TaxaAVista;
            var valortarifa = taxaRecebimento?.TarifaAVista;
            var idMoeda = Moeda.IdMoedaBRL;
            var valorantecipado = 0.00M;
            int horasDesbloqueioValor = 0;

            var rs = await dbContext.CallStoredProcedureAsync<dynamic>("Arquitetura.Insert_EntradaClienteDiretoExtratoContabil",
                idCliente.ToSqlParameter(),
                idMoeda.ToSqlParameter(),
                descricao.ToSqlParameter(),
                valor.ToSqlParameter(),
                porcentagemTaxa.ToSqlParameter(),
                valortarifa.ToSqlParameter(),
                valorantecipado.ToSqlParameter(),
                horasDesbloqueioValor.ToSqlParameter());

            int idExtratoContabilCliente = rs.FirstOrDefault();

            await dbContext.CallStoredProcedureAsync("Painel.Insert_RecebimentoOperacaoBancariaExtratoContabilCliente",
                idRecebimentoOperacaoBancaria.ToSqlParameter(),
                idCliente.ToSqlParameter(),
                idExtratoContabilCliente.ToSqlParameter());

            return idExtratoContabilCliente;
        }
    }
}