﻿using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.EnvioWebhookSaque.Application.Services
{
    public interface INotificarClientePublishNotificationService
    {
        Task<bool> EnviarNotificacaoSaqueConcluido(SaqueConfirmadoNotificarClienteEvent saqueConfirmadoNotificarClienteEvent);
    }

    internal class NotificarClientePublishNotificationService : INotificarClientePublishNotificationService
    {
        public Task<bool> EnviarNotificacaoSaqueConcluido(SaqueConfirmadoNotificarClienteEvent saqueConfirmadoNotificarClienteEvent)
        {
            return Task.FromResult(true);
        }
    }
}