namespace Domain.Entities;

public class TerminalEntity
{    
    public int Id { get; }
    public string? TerminalNumber { get; }
    public DateTime? LockDate { get; }
    public DateTime? DeletionDate { get; }

    public TerminalEntity(int id, string? terminalNumber, DateTime? lockDate, DateTime? deletionDate)
    {
        Id = id;
        TerminalNumber = terminalNumber;
        LockDate = lockDate;
        DeletionDate = deletionDate;
    }
}
