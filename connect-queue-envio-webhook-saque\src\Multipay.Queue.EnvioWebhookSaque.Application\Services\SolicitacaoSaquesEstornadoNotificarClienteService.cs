﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookSaque.Core.Extensions;
using Multipay.Queue.EnvioWebhookSaque.Core.Models;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using System.Text;

namespace Multipay.Queue.EnvioWebhookSaque.Application.Services
{
    public interface ISolicitacaoSaquesEstornadoNotificarClienteService
    {
        Task<bool> EnviarNotificacao(string urlAtualizacao, string codigoSaque, string? customId, string? customUserId, Guid guid, string? codigoTransacao);
    }

    internal class SolicitacaoSaquesEstornadoNotificarClienteService : ISolicitacaoSaquesEstornadoNotificarClienteService
    {
        private readonly ILogger<SolicitacaoSaquesEstornadoNotificarClienteService> logger;
        private readonly IHttpClientFactory httpClientFactory;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICentralizadorLogsService _centralizadorLogsService;
        private readonly IPublishSolicitacaoSaqueEstornadoRetry publishSolicitacaoSaqueEstornadoRetry;

        public SolicitacaoSaquesEstornadoNotificarClienteService(
            ILogger<SolicitacaoSaquesEstornadoNotificarClienteService> logger,
            IHttpClientFactory httpClientFactory,
            IUnitOfWork unitOfWork,
            ICentralizadorLogsService centralizadorLogsService,
            IPublishSolicitacaoSaqueEstornadoRetry publishSolicitacaoSaqueEstornadoRetry)
        {
            this.logger = logger;
            this.httpClientFactory = httpClientFactory;
            this._unitOfWork = unitOfWork;
            this._centralizadorLogsService = centralizadorLogsService;
            this.publishSolicitacaoSaqueEstornadoRetry = publishSolicitacaoSaqueEstornadoRetry;
        }

        public async Task<bool> EnviarNotificacao(string urlAtualizacao, string codigoSaque, string? customId, string? customUserId, Guid guid, string? codigoTransacao)
        {
            var solicitacaoSaque = await this._unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigo(codigoSaque);

            if (solicitacaoSaque == null)
                return false;

            _centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Enviando notificação de estorno para {urlAtualizacao} via mensageria");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, urlAtualizacao)
            {
                Content = ObterBodyContent(solicitacaoSaque, codigoSaque, customId, customUserId, guid, codigoTransacao, out string bodyJson)
            };

            if (!string.IsNullOrWhiteSpace(solicitacaoSaque.RequestIdWebhook))
                httpRequestMessage.Headers.Add("X-Request-Id", solicitacaoSaque.RequestIdWebhook);

            var httpClient = httpClientFactory.CreateClient();

            var httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);

            // Salvando o retorno do webhook ao cliente
            await AdicionarLogEnvioWebhookSolicitacaoSaque(solicitacaoSaque, bodyJson, httpResponseMessage);

            var isSuccessStatusCode = httpResponseMessage.IsSuccessStatusCode;

            if (isSuccessStatusCode)
            {
                _centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, "Enviado webhook via mensageria");
            }
            else
            {
                var response = await httpResponseMessage.Content.ReadAsStringAsync();
                logger.LogWarning("Response: {response}", response);
                publishSolicitacaoSaqueEstornadoRetry.Publish(urlAtualizacao, codigoSaque, customId, customUserId, guid, codigoTransacao);
            }
            return true;
        }

        private static HttpContent ObterBodyContent(ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string codigoSaque, string? customId, string? customUserId, Guid guid, string? codigoTransacao, out string bodyJson)
        {
            bodyJson = new
            {
                withdrawCode = codigoSaque,
                customId = customId,
                customUserId = customUserId,
                transactionId = guid.ToString(),
                transactionCode = codigoTransacao,
                updateCode = "03",
                updateMessage = "REJECTED TRANSACTION",
                amount = Convert.ToInt64(solicitacaoSaque.ValorSolicitado * 100),
                reason = solicitacaoSaque.JustificativaEstorno
            }.ToJson();

            return new StringContent(bodyJson, Encoding.UTF8, System.Net.Mime.MediaTypeNames.Application.Json);
        }

        private async Task AdicionarLogEnvioWebhookSolicitacaoSaque(ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string bodyJson, HttpResponseMessage httpResponseMessage)
        {
            _centralizadorLogsService.AdicionarLogEnvioWebhookSolicitacaoSaque(
                solicitacaoSaque.Id,
                solicitacaoSaque.Codigo,
                "ENVIO DE PIX",
                solicitacaoSaque.UrlConfirmacao!,
                bodyJson,
                $"{httpResponseMessage.StatusCode}",
                await httpResponseMessage.Content.ReadAsStringAsync());
        }
    }
}