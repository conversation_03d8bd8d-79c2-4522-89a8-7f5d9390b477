using Multipay.Queue.Webhook.Application.Providers;
using Multipay.Queue.Webhook.Application.Services;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;
using Newtonsoft.Json;
using Serilog;
using Serilog.Context;

namespace Multipay.Queue.Webhook.Api.Workers;

public class WorkerWebhookRecebido(
    ILogger<WorkerWebhookRecebido> logger,
    IServicesProvider<IWebhookRecebidoService, TipoBanco> servicesProvider,
    IBusConsumerWebhookRecebido busCobranca) : BackgroundConsumerService<WebhookRecebidoEvent>(logger, busCobranca)
{
    private readonly ILogger<WorkerWebhookRecebido> _logger = logger;

    protected override async Task<bool> ProcessEventHandler(string mensagem, WebhookRecebidoEvent @event, bool _)
    {
        using IServiceScope scope = servicesProvider.CreateScope();

        try
        {
            var payloadEvent = JsonConvert.SerializeObject(@event);

            LogContext.PushProperty("Payload", @event, true);

            TryEnrichLog(@event, mensagem);

            _logger.LogInformation("[{Class}] Evento de recebimento de webhook: {Message}, event {Event}", nameof(WorkerWebhookRecebido), mensagem, payloadEvent);

            var webhookRecebidoService = servicesProvider.GetInstance(scope, @event.TipoBanco);

            return await webhookRecebidoService.ProcessarWebhook(scope.ServiceProvider, @event);
        }
        finally
        {
            var logsService = scope.ServiceProvider.GetRequiredService<ICentralizadorLogsService>();
            
            logsService.Salvar();
        }
    }

    private static void TryEnrichLog(WebhookRecebidoEvent @event, string mensagem)
    {
        try
        {
            if (@event.TipoBanco == TipoBanco.DelBank)
            {
                if (mensagem.Contains("PIX_ERROR"))
                {
                    var pixError = JsonConvert.DeserializeObject<PixErrorNotification>(mensagem);

                    LogContext.PushProperty("PixError", pixError, true);
                }
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Erro ao deserializar erro de PIX");
        }
    }
}

internal record PixErrorNotification(string TipoBanco, string Tipo, string? ChanelType, PixErrorDataPayload Data, string IdWebhook, string Id, DateTime CreationDate);

internal record PixErrorDataPayload(string EndToEndId, string IdempotencyKey, string Status, PixErrorErrorDetails Error, decimal Amount, DateTime CreatedAt, DateTime FinishedAt);

internal record PixErrorErrorDetails(string Code, string Description);