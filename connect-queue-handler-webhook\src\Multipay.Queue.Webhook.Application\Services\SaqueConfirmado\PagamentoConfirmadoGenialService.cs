﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.Saques;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Extensions;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado
{
    public interface IPagamentoConfirmadoGenialService : IPagamentoConfirmadoService
    {
    }

    internal class PagamentoConfirmadoGenialService : PagamentoConfirmadoBaseService<SolicitacaoSaqueRecebidoGenialModel>, IPagamentoConfirmadoGenialService
    {
        public PagamentoConfirmadoGenialService(
            ILogger<PagamentoConfirmadoGenialService> logger,
            IEstornarSaqueService estornarSaqueService,
            ICentralizadorLogsService centralizadorLogsService, IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IBrasilApiService brasilApiService, IUnitOfWork unitOfWork) :
            base(logger, centralizadorLogsService, busNotificarSaqueConfirmado, brasilApiService, estornarSaqueService, unitOfWork)
        {
        }

        protected override string NomeBanco => TipoBanco.Genial.GetDescription();

        protected override string ObterEndToEndId(SolicitacaoSaqueRecebidoGenialModel parameters)
        {
            return parameters.EndToEndId;
        }

        protected override Status? ObterStatus(SolicitacaoSaqueRecebidoGenialModel parameters)
        {
            if (parameters.DebitEventStatus?.Code == 3)
                return Status.EFETIVADO;
            if (parameters.DebitEventStatus?.Code == 22)
                return Status.REJEITADO;

            return null;
        }
    }
}