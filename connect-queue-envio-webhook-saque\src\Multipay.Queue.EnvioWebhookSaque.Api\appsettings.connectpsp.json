{"SlidingExpiration": 1, "ConnectionStrings": {"MultiPayDatabase": "Data Source=*************; Initial Catalog=CONNECTPSP_DB; User ID=cpsp_services; password=********************************************; Trusted_Connection=False; Connection Timeout=360; MultipleActiveResultSets=True; TrustServerCertificate=True; Encrypt=False;"}, "MongoStoreDatabaseSettings": {"ConnectionString": "mongodb://admanager:xW1BW6YDSoxQq+0UxGaDvA==@**************:27017,**************:27017,**************:27017,**************:27017,*************:27017,*************:27017/admin?retryWrites=true&loadBalanced=false&replicaSet=rs0&readPreference=secondaryPreferred&serverSelectionTimeoutMS=5000&connectTimeoutMS=10000&authSource=admin&authMechanism=SCRAM-SHA-1", "DatabaseName": "connectpsp_log_db", "CollectionNames": {"LogCobranca": "logcobranca", "RequisicoesAPI": "requisicoesapi", "RequisicoesBMP": "requisicoesbmp", "WebhookFornecedor": "webhookfornecedor", "LogSolicitacaoSaque": "logsolicitacaosaque", "EnvioWebhookCobranca": "enviowebhookcobranca", "RequisicoesFornecedor": "requisicoesfornecedor", "WebhookFornecedorCobranca": "webhookfornecedorcobranca", "EnvioWebhookSolicitacaoSaque": "enviowebhooksolicitacaosaque", "WebhookFornecedorSolicitacaoSaque": "webhookfornecedorsolicitacaosaque", "CobrancasGeradas": "cobrancasger<PERSON>s", "LogAberturaContaDelbank": "logaberturacontadelbank", "EnvioWebhookCliente": "envio_webhook_cliente", "AuditRequisicoesFornecedor": "auditRequisicoesfornecedor"}}, "RabbitMqSettings": {"Setup": {"EventBusConnection": "b-374cbbd9-86de-4386-9805-14f8682ffce9.mq.us-east-1.amazonaws.com", "EventBusPort": 5671, "EventBusVirtualHost": "connectpsp", "EventBusUserName": "admin", "EventBusPassword": "2xFgvt1e1OfXgX31+WNCYQ==", "EventBusRetryCount": "5", "EnableSsl": true}, "SolicitacaoSaquesEstornado": {"Publish": {"Exchange": "solicitacao-saques-estornado", "ExchangeType": "fanout"}, "Consumer": {"Exchange": "solicitacao-saques-estornado", "QueueName": "solicitacao-saques-estornado-notificar-cliente", "DeadLetter": {"Count": 5}}}, "SolicitacaoSaques": {"Consumer": {"Exchange": "solicitacao-saques", "QueueName": "solicitacao-saques", "DeadLetter": {"Exchange": "solicitacao-saques-error", "QueueName": "solicitacao-saques-error", "RoutingKey": "solicitacao-saques-error", "Count": 3}}}, "NotificarCliente": {"Publish": {"Exchange": "notificar-cliente", "ExchangeType": "fanout"}}}}