﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services
{
    public class WebhookRecebidoDIService : IWebhookRecebidoService
    {
        private readonly ILogger<WebhookRecebidoDIService> logger;

        public TipoBanco TipoBanco => TipoBanco.BANCODI;

        public WebhookRecebidoDIService(ILogger<WebhookRecebidoDIService> logger)
        {
            this.logger = logger;
        }

        public Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
        {
            if (@event.Tipo == "pix/recebimento")
            {
                var service = serviceProvider.GetRequiredService<CobrancaRecebidaDIService>();
                return service.Processar($"{@event.Data}", @event.IdWebhook);
            }

            if (@event.Tipo?.StartsWith("pix/pagamento") == true)
            {
                var service = serviceProvider.GetRequiredService<IPagamentoConfirmadoDIService>();
                return service.ProcessarPagamento(@event);
            }

            logger.LogWarning($"Mensagem não tratada. {@event}");

            return Task.FromResult(true);
        }
    }
}