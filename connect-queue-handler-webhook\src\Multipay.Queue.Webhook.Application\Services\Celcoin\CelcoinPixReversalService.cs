﻿using Microsoft.Extensions.Logging;
using MongoDB.Bson.IO;
using Multipay.Queue.Webhook.Application.Models.Celcoin;
using Multipay.Queue.Webhook.Core.Entities;
using Multipay.Queue.Webhook.Core.Enums;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco.Repositories;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;
using Serilog.Context;

namespace Multipay.Queue.Webhook.Application.Services.Celcoin;
public class CelcoinPixReversalService : ICelcoinPixReversalService
{
    private readonly ILogger<CelcoinPixReversalService> _logger;
    private readonly INewUnitOfWork _unitOfWork;
    private readonly IPixRepository _depositPixRepository;
    private readonly ICustomerRepository _customerRepository;
    private readonly IWebhookRepository _webhookRepository;
    private readonly IBusRefundPixNotification _producer;
    private readonly ICobrancaRepository _cobrancaRepository;
    private readonly IQrCodePixRepository _qrCodePixRepository;
    private readonly ISolicitacaoSaqueRepository _solicitacaoSaqueRepository;
    private readonly IBusNotificarSaqueEstornadoCliente _busNotificarSaqueEstornadoCliente;
    public CelcoinPixReversalService(
        ILogger<CelcoinPixReversalService> logger,
        INewUnitOfWork unitOfWork,
        IPixRepository depositPixRepository,
        ICustomerRepository customerRepository,
        IWebhookRepository webhookRepository,
        IBusRefundPixNotification producer,
        ICobrancaRepository cobrancaRepository,
        IQrCodePixRepository qrCodePixRepository,
        ISolicitacaoSaqueRepository solicitacaoSaqueRepository
        ,IBusNotificarSaqueEstornadoCliente busNotificarSaqueEstornadoCliente
        )
    {
        _logger = logger;
        _unitOfWork = unitOfWork;
        _depositPixRepository = depositPixRepository;
        _customerRepository = customerRepository;
        _producer = producer;
        _webhookRepository = webhookRepository;
        _cobrancaRepository = cobrancaRepository;
        _qrCodePixRepository = qrCodePixRepository;
        _solicitacaoSaqueRepository = solicitacaoSaqueRepository;
        _busNotificarSaqueEstornadoCliente = busNotificarSaqueEstornadoCliente;
    }

    public async Task<bool> VerifyPixCorrelationAsync<T>(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, CancellationToken cancellationToken)
    {
        var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<T>($"{@event.Data}");
        if (parameters is null)
        {
            return false;
        }

        var correlationIdInfo = parameters.GetType().GetProperties().FirstOrDefault(p => p.Name == behavior.CorrelationIdAttributeName);
        if (correlationIdInfo is null)
        {
            return false;
        }

        var correlationIdValue = (string?)correlationIdInfo.GetValue(parameters);
        if (string.IsNullOrWhiteSpace(correlationIdValue))
        {
            return false;
        }

        var refundPix = await _depositPixRepository.GetPixDepositRefundByCorrelationIdAsync(correlationIdValue, cancellationToken);
        if (refundPix is null)
        {
            return false;
        }
        return true;
    }
     public async Task<bool> ProcessReversalPixAsync<T>(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, CancellationToken cancellationToken)
    {
        try
        {
            string? statusValue = string.Empty;

            _logger.LogInformation("[{0}] Processo de Devolucao", nameof(CelcoinPixReversalService));

            var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<ReversalPixCelcoinModel>($"{@event.Data}");
            if (parameters is null)
            {
                _logger.LogError("[{0}] Não foi possivel descerilizar objeto obido, Tipo: {1}", nameof(CelcoinPixReversalService), @event.Tipo);
                return false;
            }
            
            
            var model = parameters.Body;

            if(parameters.Entity == "pix-reversal-out")
            {

                if (model == null)
                    return true;

                var idDeposito = await _cobrancaRepository.RegistrarEndToEndDevolucaoCobranca(model.ReturnIdentification, model.OriginalEndToEndId);
                if (idDeposito == null)
                    return true;

                var deposito = await _cobrancaRepository.ObterPorId(idDeposito.Value);
                if (deposito == null)
                    return true;

                var transacaoPIX = await _qrCodePixRepository.ObterPorIdCobranca(deposito.Id);

                if (transacaoPIX != null)
                    deposito.SetarTransacao(transacaoPIX);

                using (LogContext.PushProperty("Payload", model, true))
                {
                    _logger.LogInformation("Evento de estorno de depósito. E2E Transação: {E2ETransacao} / E2E Devolução: {E2EDevolucao}", model.OriginalEndToEndId, model.ReturnIdentification);

                    await _cobrancaRepository.CancelarCobranca(idDeposito.Value);

                    await _cobrancaRepository.InserirSaidaExtratoContabilCliente(deposito.IdCliente, $"ESTORNO DEPÓSITO - {deposito.Codigo}", deposito.ValorBruto, deposito.TransacaoPix!.TXId, deposito.TransacaoPix.IdContaBancariaEmpresa!.Value);
                }

                return true;
            }

            if (parameters.Entity == "pix-reversal-in")
            {

                var solicitacaoSaque = await _solicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(model.OriginalEndToEndId);

                if (solicitacaoSaque == null || solicitacaoSaque.DataEstorno.HasValue)
                    return true;

                using (LogContext.PushProperty("Payload", model, true))
                {
                    _logger.LogInformation("Evento de estorno de saque. E2E Transação: {E2ETransacao} / E2E Devolução: {E2EDevolucao}", model.OriginalEndToEndId, model.ReturnIdentification);

                    await _solicitacaoSaqueRepository.EstornarSaque(solicitacaoSaque.Id, "ESTORNO" ?? string.Empty);

                    await _solicitacaoSaqueRepository.RegistrarEndToEndDevolucao(model.OriginalEndToEndId, model.ReturnIdentification!);

                    _busNotificarSaqueEstornadoCliente.PublicarNotificacaoCliente(solicitacaoSaque.UrlAtualizacao ?? solicitacaoSaque.UrlConfirmacao!, solicitacaoSaque.Codigo, solicitacaoSaque.CustomId, solicitacaoSaque.CustomUserId, solicitacaoSaque.Guid, solicitacaoSaque.CodigoTransacao);
                }

            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{0}] Erro não mapeado durante a execução do consumer, Tipo: {1}, Message:{2}", nameof(CelcoinPixReversalService), @event.Tipo, ex.Message);

            _unitOfWork.Rollback();

            return false;
        }

    }

    private async Task SetSuccefullAsync(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, string? statusValue, PixDepositRefundEntity? refundPix, BillingEntity? billing, BalanceCustomerCurrencyEntity? balance, CancellationToken cancellationToken)
    {
        var newBalanceValue = balance.GetDebitBalance(billing.NetValue);
        var billingLog = GetBillingLog(refundPix);
        var customerExit = GetCustomerExit(refundPix);
        var customerAccountingStatement = GetCustomerAccountingStatement(refundPix, balance, newBalanceValue);
        var webhookProvider = GetWebhookProvider(@event, behavior);

        _unitOfWork.BeginTransaction();

        await _depositPixRepository.UpdatePixDepositRefundAsync(refundPix!.Id.ToString(), statusValue, DateTime.UtcNow, cancellationToken);
        await _depositPixRepository.UpdateCurrentBillingAsync(refundPix!.BillingId, DateTime.UtcNow, cancellationToken);
        await _depositPixRepository.UpdateCurrenBankSlipAsync(refundPix!.BillingId, DateTime.UtcNow, cancellationToken);
        await _depositPixRepository.UpdatBitcoinTransactionAsync(refundPix!.BillingId, DateTime.UtcNow, cancellationToken);
        await _depositPixRepository.AddBillingLogAsync(billingLog, cancellationToken);
        await _customerRepository.UpdateBalanceAsync(refundPix.CustomerId, refundPix.CoinId, newBalanceValue, DateTime.UtcNow, cancellationToken);
        var customerExitId = await _customerRepository.AddCustomerExitAsync(customerExit, cancellationToken);
        var customerAccountingStatementId = await _customerRepository.AddCustomerAccountingStatementAsync(customerAccountingStatement, cancellationToken);
        var webhookProviderId = await _webhookRepository.AddWebhookProviderEntity(webhookProvider, cancellationToken);
        await _customerRepository.AddCustomerOutputAccountingStatementAsync(new CustomerOutputAccountingStatementEntity(customerExitId, customerAccountingStatementId), cancellationToken);
        await _webhookRepository.AddWebhookRefundPixEntity(new WebhookRefundPixEntity(refundPix.Id, webhookProviderId, DateTime.UtcNow, "rabbitmq"), cancellationToken);

        _unitOfWork.Commit();
    }

    private static WebhookProviderEntity GetWebhookProvider(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior)
    {
        return new WebhookProviderEntity(behavior.FinancialInstitution, "REFUNDPIX", Newtonsoft.Json.JsonConvert.SerializeObject(@event.Data), behavior.OriginIp, DateTime.UtcNow);
    }

    private async Task SetPixRefundNotProcessed(PixDepositRefundEntity? refundPix, string? statusValue, CancellationToken cancellationToken)
    {
        _unitOfWork.BeginTransaction();

        await _depositPixRepository.UpdatePixDepositRefundAsync(refundPix!.Id.ToString(), statusValue, DateTime.UtcNow, cancellationToken);

        _unitOfWork.Commit();

        _logger.LogInformation("[{0}] Processo finalizado com status:{1}", nameof(CelcoinPixReversalService), statusValue);
    }

    private static CustomerAccountingStatementEntity GetCustomerAccountingStatement(PixDepositRefundEntity refundPix, BalanceCustomerCurrencyEntity balance, decimal newBalanceValue)
    {
        return new CustomerAccountingStatementEntity
        {
            CustomerId = refundPix.CustomerId,
            AccountingMovementTypeId = (byte)AccountingMovementType.Debit,
            CoinId = refundPix.CoinId,
            Description = refundPix.Description,
            LastBalance = balance.Balance,
            Value = refundPix.OriginalValue,
            Balance = newBalanceValue,
            TransactionDate = DateTime.UtcNow
        };
    }

    private static CustomerExitEntity GetCustomerExit(PixDepositRefundEntity refundPix) =>
        new()
        {
            CustomerId = refundPix.CustomerId,
            CoinId = refundPix.CoinId,
            Description = refundPix.Description,
            Value = refundPix.OriginalValue,
            RateValue = refundPix.RateValue,
            TariffValue = refundPix.TariffValue,
            TransactionDate = DateTime.UtcNow,
            UnlockDate = DateTime.UtcNow,
            TxId = refundPix.TXId,
        };

    private static LogBillingEntity GetBillingLog(PixDepositRefundEntity? refundPix) =>
        new()
        {
            BillingId = refundPix.BillingId,
            Date = DateTime.UtcNow,
            Description = "Deposito estornado",
            Exclusive = true
        };
}
