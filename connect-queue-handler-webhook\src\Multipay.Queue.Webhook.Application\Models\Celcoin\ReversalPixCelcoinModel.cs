﻿using System.Text.Json.Serialization;

namespace Multipay.Queue.Webhook.Application.Models.Celcoin;
public class ReversalPixCelcoinModel
{
    [JsonPropertyName("entity")]
    public string Entity { get; set; }

    [JsonPropertyName("createTimestamp")]
    public DateTime CreateTimestamp { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; }

    [JsonPropertyName("body")]
    public Body Body { get; set; }

    [JsonPropertyName("webhookId")]
    public string WebhookId { get; set; }
}

public class Body
{
    [JsonPropertyName("id")]
    public string Id { get; set; }

    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("clientCode")]
    public string ClientCode { get; set; }

    [JsonPropertyName("originalPaymentId")]
    public string OriginalPaymentId { get; set; }

    [JsonPropertyName("originalEndToEndId")]
    public string OriginalEndToEndId { get; set; }

    [JsonPropertyName("returnIdentification")]
    public string ReturnIdentification { get; set; }

    [JsonPropertyName("reason")]
    public string Reason { get; set; }

    [JsonPropertyName("debitParty")]
    public Party DebitParty { get; set; }

    [JsonPropertyName("creditParty")]
    public Party CreditParty { get; set; }

    [JsonPropertyName("currentBalance")]
    public decimal CurrentBalance { get; set; }

    [JsonPropertyName("oldBalance")]
    public decimal OldBalance { get; set; }
}

public class Party
{
    [JsonPropertyName("taxId")]
    public string TaxId { get; set; }

    [JsonPropertyName("accountType")]
    public string AccountType { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonPropertyName("branch")]
    public string Branch { get; set; }

    [JsonPropertyName("account")]
    public string Account { get; set; }

    [JsonPropertyName("bank")]
    public string Bank { get; set; }
}
