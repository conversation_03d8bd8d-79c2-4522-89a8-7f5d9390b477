﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Settings;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Bus;

public interface IPublishNotificarClienteRetry
{
    void Publicar(CobrancaConfirmadaNotificarClienteEvent @event);
}

internal class PublishNotificarClienteRetry : EventBusPublishRabbitMQ, IPublishNotificarClienteRetry
{
    public PublishNotificarClienteRetry(
        IRabbitMQPersistentConnection persistentConnection,
        ILogger<PublishNotificarClienteRetry> logger, IOptions<RabbitMqSettings> options) : base(
        persistentConnection,
        logger,
        options.Value.Setup.EventBusRetryCount,
        options.Value.NotificarClienteRetry)
    {
    }

    public void Publicar(CobrancaConfirmadaNotificarClienteEvent @event)
    {
        Publish(@event);
    }
}