﻿namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels
{
    internal partial class CentralizadorLogsService
    {
        public class Help
        {
            public Help(CentralizadorLogsService centralizadorLogs)
            {
                this.centralizadorLogs = centralizadorLogs;
            }

            public CentralizadorLogsService centralizadorLogs { get; }

            public string AdicionarLogSolicitacaoSaque(int idSolicitacaoSaque, string texto)
            {
                var document = new LogSolicitacaoSaqueModel(idSolicitacaoSaque, texto);
                centralizadorLogs.Add(document);
                return document.Id;
            }

            public string AdicionarLogWebhookFornecedor(string fornecedor, string tipo, string body, string ipOprigem)
            {
                var document = new WebhookFornecedorModel(fornecedor, tipo, body, ipOprigem);

                centralizadorLogs.Add(document);

                return document.Id;
            }

            public string AdicionarLogWebhookFornecedorSolicitacaoSaque(string idWebhook, int idSolicitacaoSaque, string acao)
            {
                var document = new WebhookFornecedorSolicitacaoSaqueModel(idSolicitacaoSaque, acao, idWebhook);
                centralizadorLogs.Add(document);
                return document.Id;
            }

            public string AdicionarLogEnvioWebhookSolicitacaoSaque(int idSolicitacaoSaque, string codigoSolicitacaoSaque, string acao, string urlWebhook, string requestJson, string httpResponseCode, string responseJson)
            {
                var document = new EnvioWebhookSolicitacaoSaqueModel(idSolicitacaoSaque, codigoSolicitacaoSaque, acao, urlWebhook, requestJson, httpResponseCode, responseJson);
                centralizadorLogs.Add(document);

                return document.Id;
            }
        }
    }
}