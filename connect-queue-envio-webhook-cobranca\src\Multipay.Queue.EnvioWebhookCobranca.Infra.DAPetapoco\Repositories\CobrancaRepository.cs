﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    public interface ICobrancaRepository
    {
        Task<PessoaCobrancaModel> ObterPessoaCobranca(int idCobranca);

        Task<List<MovimentacaoWebhooksAdicionaisModel>> ObterWebhooksAdicionaisPorId(int idCobranca);

        Task ConfirmarEnvioWebhookAdicionalPorId(int id);

        Task AdicionarPagadorCobranca(int idCobranca, string cpfPagadorCobranca, string nomePagadorCobranca, string endToEndId);

        Task ConfirmarPagamentoBRL(int idCobranca, int idCliente, byte pIX, decimal valorPago1, int numeroParcelas, string descricao, decimal valorPago2, decimal porcentagemTaxa, decimal valorTarifa, int horasResgate, string txIdPIX);

        Task<PagadorCobrancaModel?> ObterPagadorPorId(int idCobranca);

        Task<CobrancaModel?> ObterPorCodigo(string codigo);

        Task<CobrancaModel?> ObterPorId(int id);

        Task<long?> CountCobrancasPorIdPessaCliente(int id, int idCliente, int idPessoaCobranca);

        Task ConfirmarEnvioConfirmacaoCliente(int idCobranca);

        Task<string> ObterNomeClientePorId(int id);
    }

    internal class CobrancaRepository : ICobrancaRepository
    {
        private readonly ILogger logger;
        private readonly IDatabase database;

        public CobrancaRepository(
            ILogger logger,
            IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public async Task<List<MovimentacaoWebhooksAdicionaisModel>> ObterWebhooksAdicionaisPorId(int idCobranca)
        {
            try
            {
                var sql = Sql.Builder.Select("*")
                    .From("MovimentacaoWebhooksAdicionais")
                    .Where("IdCobranca=@0", idCobranca);

                using var reader = await database.QueryAsync<MovimentacaoWebhooksAdicionaisModel?>(sql);

                var retorno = new List<MovimentacaoWebhooksAdicionaisModel>();

                if (await reader.ReadAsync())
                {
                    retorno.Add(reader.Poco);
                }

                return retorno;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<PessoaCobrancaModel> ObterPessoaCobranca(int idCobranca)
        {
            try
            {
                var sql = $@"SELECT TOP (1) p.[Id]
                              ,p.[IdCliente]
                              ,p.[Nome]
                              ,p.[CPFCNPJ]
                              ,p.[Email]
                              ,p.[EnderecoCompleto]
                              ,p.[Complemento]
                              ,p.[Bairro]
                              ,p.[Cidade]
                              ,p.[Estado]
                              ,p.[Pais]
                              ,p.[CEP]
                              ,p.[Telefone]
                          FROM [dbo].[PessoaCobranca] (nolock) p
                          inner join cobranca (nolock) c on c.IdPessoaCobranca = p.Id
                          where c.Id = {idCobranca}";

                using var reader = await database.QueryAsync<PessoaCobrancaModel?>(sql);

                if (await reader.ReadAsync())
                {
                    return reader.Poco;
                }

                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task ConfirmarEnvioWebhookAdicionalPorId(int id)
        {
            try
            {
                var sql = $@"Update MovimentacaoWebhooksAdicionais set DataEnvio = Getdate() where Id = {id}";

                var rtn = await database.ExecuteAsync(sql);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task AdicionarPagadorCobranca(int IdCobranca, string NomePagador, string CPFCNPJPagador, string EndToEnd)
        {
            try
            {
                await database.ExecuteNonQueryProcAsync("[Painel].[Insert_PagadorCobranca]", new
                {
                    IdCobranca,
                    CPFCNPJPagador,
                    NomePagador,
                    EndToEnd
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task ConfirmarEnvioConfirmacaoCliente(int idCobranca)
        {
            try
            {
                await database.ExecuteNonQueryProcAsync("Painel.Update_CobrancaConfirmacaoPagamento", new
                {
                    id = idCobranca
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task ConfirmarPagamentoBRL(int idCobranca, int idCliente, byte idFormaPagamento, decimal valorPago, int parcelas, string descricao, decimal valor,
            decimal porcentagemTaxa, decimal valorTarifa, int horasResgate, string txIdPIX)
        {
            try
            {
                var idMoeda = idFormaPagamento == 5 ? (byte)4 : (byte)1;

                await database.ExecuteNonQueryProcAsync("Arquitetura.ConfirmarPagamentoCobranca", new
                {
                    idCobranca,
                    idCliente,
                    idFormaPagamento,
                    idMoeda,
                    valorPago,
                    parcelas,
                    descricao,
                    valor,
                    porcentagemTaxa,
                    valorTarifa,
                    horasResgate,
                    txIdPIX
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<PagadorCobrancaModel?> ObterPagadorPorId(int idCobranca)
        {
            try
            {
                var sql = Sql.Builder.Select("*")
                    .From("PagadorCobranca")
                    .Where("IdCobranca=@0", idCobranca);

                using var reader = await database.QueryAsync<PagadorCobrancaModel?>(sql);

                if (await reader.ReadAsync())
                {
                    return reader.Poco;
                }
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<CobrancaModel?> ObterPorCodigo(string codigo)
        {
            try
            {
                var sql = Sql.Builder.Select(
                    "c.*",
                    "fp.Sigla 'FormaPagamento'",
                    "c.QuantidadeParcelasEscolhida 'Parcelas'")
                    .From("Cobranca c")
                    .LeftJoin("FormaPagamento fp").On("fp.Id = c.IdFormaPagamento")
                    .Where("c.Codigo=@0", codigo);

                using var reader = await database.QueryAsync<CobrancaModel?>(sql);

                if (await reader.ReadAsync())
                {
                    return reader.Poco;
                }
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<CobrancaModel?> ObterPorId(int id)
        {
            try
            {
                using var reader = await database.QueryProcAsync<CobrancaModel>("Painel.Select_CobrancaPorId", new
                {
                    idCobranca = id
                });

                if (await reader.ReadAsync())
                    return reader.Poco;

                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<long?> CountCobrancasPorIdPessaCliente(int id, int idCliente, int idPessoaCobranca)
        {
            try
            {
                var sql = @"SELECT Count(1)
                            FROM cobranca C (nolock)
                            WHERE c.IdCliente = @idCliente
                            AND c.IdPessoaCobranca = @idPessoaCobranca
                            AND c.Id <> @id
                            AND c.DataPagamento is not null
                            AND c.DataCancelamento is null ";

                using var reader = await database.QueryAsync<long?>(sql, new { id, idCliente, idPessoaCobranca });

                if (await reader.ReadAsync())
                {
                    return reader.Poco;
                }

                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(CobrancaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<string> ObterNomeClientePorId(int id)
        {
            var nome = await database.ExecuteScalarAsync<string>($"SELECT NomeFantasia FROM PessoaJuridica a WITH (NOLOCK) INNER JOIN Cliente b WITH (NOLOCK) ON b.IdPessoa = a.Id WHERE b.Id = {id}");

            return nome ?? string.Empty;
        }
    }
}