﻿using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Models
{
    public class BancoParticipantesPixModel
    {
        public string Ispb { get; set; } = string.Empty;
        public string Nome { get; set; } = string.Empty;

        [JsonProperty("nome_reduzido")]
        public string NomeReduzido { get; set; } = string.Empty;

        [JsonProperty("modalidade_participacao")]
        public string ModalidadeParticipacao { get; set; } = string.Empty;

        [JsonProperty("tipo_participacao")]
        public string TipoParticipacao { get; set; } = string.Empty;

        [JsonProperty("inicio_operacao")]
        public DateTime InicioOperacao { get; set; }
    }
}