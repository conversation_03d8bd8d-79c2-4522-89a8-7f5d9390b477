﻿using Multipay.Queue.Webhook.Core.Interfaces;
using Newtonsoft.Json;
using System.Globalization;

namespace Multipay.Queue.Webhook.Application.Models.MkBank
{
    public class CobrancaRecebidaModel : IPIXData
    {
        public string Txid => Owner.TransactionId ?? string.Empty;

        public string Valor => Amount.ToString(CultureInfo.GetCultureInfo("pt-BR"));

        public string CpfPagador => Owner.SenderDocumentNumber;

        public string? IspbPagador => null;
        public string? NomeBancoPagador => null;
        public string? ContaBancoPagador => null;
        public string? AgenciaBancoPagador => null;
        public string NomePagador => Owner.SenderName;

        public string EndToEndId => Owner.EndToEndId;

        public int Id { get; set; }

        [JsonProperty("account_id")]
        public int AccountId { get; set; }

        [JsonProperty("authentication_code")]
        public Guid AuthenticationCode { get; set; }

        public decimal Amount { get; set; }
        public string Status { get; set; } = null!;
        public string Type { get; set; } = null!;

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("performed_at")]
        public DateTime PerformedAt { get; set; }

        [JsonProperty("performed_at_timestamp")]
        public long PerformedAtTimestamp { get; set; }

        public OwnerData Owner { get; set; } = null!;

        public class OwnerData
        {
            public int Id { get; set; }

            [JsonProperty("authentication_code")]
            public Guid AuthenticationCode { get; set; }

            public string Status { get; set; } = null!;
            public decimal Amount { get; set; }

            [JsonProperty("end_to_end_id")]
            public string EndToEndId { get; set; } = null!;

            public string? Notes { get; set; }

            [JsonProperty("sender_account_bank_ispb")]
            public string SenderAccountBankIspb { get; set; } = null!;

            [JsonProperty("sender_account_bank_name")]
            public string SenderAccountBankName { get; set; } = null!;

            [JsonProperty("sender_account_branch")]
            public string SenderAccountBranch { get; set; } = null!;

            [JsonProperty("sender_account_number")]
            public string SenderAccountNumber { get; set; } = null!;

            [JsonProperty("sender_account_type")]
            public string SenderAccountType { get; set; } = null!;

            [JsonProperty("sender_document_number")]
            public string SenderDocumentNumber { get; set; } = null!;

            [JsonProperty("sender_name")]
            public string SenderName { get; set; } = null!;

            [JsonProperty("addressing_key")]
            public string AddressingKey { get; set; } = null!;

            [JsonProperty("addressing_key_type")]
            public object? AddressingKeyType { get; set; }

            [JsonProperty("transaction_id")]
            public string? TransactionId { get; set; }

            [JsonProperty("initialization_type")]
            public string InitializationType { get; set; } = null!;

            [JsonProperty("devolution_code")]
            public string? DevolutionCode { get; set; }
        }
    }
}