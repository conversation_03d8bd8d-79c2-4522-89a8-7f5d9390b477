﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Sicoob.Core;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Responses;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.Repositories;
using Multipay.Service.Criptography;
using Newtonsoft.Json;
using System.Net;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Multipay.Infrastructure.Plugin.Sicoob.Services
{
    public interface IAuthPagamentoService
    {
        Task<ApiResponse> GetToken(string clientId, bool ignoreCache = false);
    }

    internal class AuthPagamentoService : BaseService, IAuthPagamentoService
    {
        private const string GET_TOKEN_KEY = "TokenSicoobEncrypted";

        private readonly ILogger<AuthPagamentoService> logger;
        private readonly IErroRepository erroRepository;
        private readonly ICentralizadorLogsService centralizadorLogsService;
        private readonly HttpClient httpClient;
        private readonly IDistributedCache distributedCache;
        private readonly CriptoService _criptoService;

        public AuthPagamentoService(
            ILogger<AuthPagamentoService> logger,
            IEnvironment environment,
            IErroRepository erroRepository,
            ICentralizadorLogsService centralizadorLogsService,
            HttpClient httpClient,
            IDistributedCache distributedCache,
            CriptoService criptoService) : base(environment, criptoService)
        {
            this.logger = logger;
            this.erroRepository = erroRepository;
            this.centralizadorLogsService = centralizadorLogsService;
            this.httpClient = httpClient;
            this.distributedCache = distributedCache;
            this._criptoService = criptoService;
        }

        public async Task<ApiResponse> GetToken(string clientId, bool ignoreCache)
        {
            try
            {
                if (!ignoreCache)
                {
                    var cache = await GetCached(clientId);

                    if (cache != null)
                        return new ApiResponse(true, HttpStatusCode.OK, cache);
                }

                var url = $"{_environment.AuthURL}/auth/realms/cooperado/protocol/openid-connect/token";

                var body = new Dictionary<string, string>
                {
                    { "client_id", clientId },
                    { "grant_type", _environment.GrantType },
                    { "scope", _environment.Scope }
                };

                using var response = await httpClient.PostAsync(url, new FormUrlEncodedContent(body));

                await response.EnsureSuccessStatusCodeAsync();

                var jsonString = await response.Content.ReadAsStringAsync();

                var jsonObj = JsonConvert.DeserializeAnonymousType(jsonString, new
                {
                    access_token = string.Empty,
                    expires_in = 0,
                    token_type = string.Empty,
                    scope = string.Empty
                });

                var returnObj = new TokenPagamentoResponseModel()
                {
                    Scope = jsonObj!.scope,
                    AccessToken = jsonObj.access_token,
                    ExpiresIn = jsonObj.expires_in,
                    TokenType = jsonObj.token_type
                };

                centralizadorLogsService.AdicionarLogRequisicoesFornecedor("Sicoob", "AUTENTICACAO GET TOKEN", "AuthPagamentoService.GetToken", JsonSerializer.Serialize(body), (int)response.StatusCode, JsonSerializer.Serialize(returnObj));

                await SetCache(clientId, returnObj);

                return new ApiResponse(true, response.StatusCode, returnObj);
            }
            catch (HttpResponseException ex)
            {
                await erroRepository.InsertErro("AuthPagamentoService", "GetToken", "MultiPay.Plugin.BancoSicoob.AuthPagamentoService.GetToken", ex.Message, ex.StackTrace ?? string.Empty, ex.ToString());

                return new ApiResponse(false, ex.StatusCode, ex.Response);
            }
            catch (Exception exc)
            {
                await erroRepository.InsertErro("AuthPagamentoService", "GetToken", "MultiPay.Plugin.BancoSicoob.AuthPagamentoService.GetToken", exc.Message, exc.StackTrace ?? string.Empty, exc.ToString());

                return new ApiResponse(false, HttpStatusCode.InternalServerError, exc.ToString());
            }
        }

        private async Task<TokenPagamentoResponseModel?> GetCached(string clientId)
        {
            var cachedData = await this.distributedCache.GetStringAsync($"{GET_TOKEN_KEY}:{clientId}");

            if (cachedData is null)
                return null;

            if (!string.IsNullOrWhiteSpace(cachedData))
                cachedData = await _criptoService.Decrypt(cachedData);

            return JsonConvert.DeserializeObject<TokenPagamentoResponseModel>(cachedData);
        }

        private async Task SetCache(string clientId, TokenPagamentoResponseModel entity)
        {
            var options = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromSeconds(entity.ExpiresIn - 30));

            var cache = await _criptoService.Encrypt(JsonSerializer.Serialize(entity));

            await this.distributedCache.SetStringAsync($"{GET_TOKEN_KEY}:{clientId}", cache, options);
        }
    }
}