﻿using Microsoft.Extensions.Configuration;
using Serilog;
using Serilog.Configuration;
using Serilog.Events;
using Serilog.Exceptions;
using Serilog.Sinks.Datadog.Logs;

namespace Multipay.Queue.EnvioWebhookCobranca.Core.Extensions
{
    public static class SerilogExtension
    {
        public static ILogger AddSerilog(IConfiguration configuration, string tema)
        {
            LoggerConfiguration configLog = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .Enrich.FromLogContext()
                .Enrich.WithMachineName()
                .Enrich.WithThreadName()
                .Enrich.WithThreadId()
                .Enrich.WithExceptionDetails()
                .Enrich.WithProperty("AppName", configuration["NameApp"]);

            var outputTemplate = "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {Message:lj} ({SourceContext}) {NewLine}{Exception}";
            if (!string.IsNullOrEmpty(configuration["LogsApp"]))
            {
                configLog.AddConfigFile(configuration["LogsApp"]!, configuration["NameApp"]!, tema, 90);//reter 90 dias de arquivo
            }
            else
            {
                outputTemplate = "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {Message:lj} {Properties:j} ({SourceContext}) {NewLine}{Exception}";
            }
            configLog = configLog
                .WriteTo.Console(outputTemplate: outputTemplate);

            var config = new DatadogConfiguration() { Url = configuration["DataDog:Url"] };
            configLog = configLog.WriteTo.DatadogLogs(
                configuration["DataDog:ApiKey"],
                tema,
                configuration["NameApp"],
                Environment.MachineName,
                configuration: config);
            configLog = configLog
                .WriteTo.Console(outputTemplate: outputTemplate);

            return Log.Logger = configLog
                .CreateLogger();
        }

        private static void AddConfigFile(this LoggerConfiguration loggerConfiguration,
            string logsPathBase, string nameApp, string tema, int? retainedFileCountLimit = null)
        {
            Func<LogEvent, string> BuildPath()
            {
                return dt => Path.Combine(logsPathBase, $"{tema}_{nameApp}", $"{dt.Timestamp.Year}", $"{dt.Timestamp.Month}", "log_.log");
            }
            void ConfigureFile(string filePath, LoggerSinkConfiguration wt)
            {
                wt.File(filePath,
                        rollingInterval: RollingInterval.Day,
                        outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} Level:u3}] {Message:lj} {Properties:j} ({SourceContext}) {NewLine}{Exception}",
                        rollOnFileSizeLimit: true,
                        shared: true,
                        retainedFileCountLimit: retainedFileCountLimit,
                        fileSizeLimitBytes: 5_000_000);
            }
            loggerConfiguration.WriteTo.Map(BuildPath(), ConfigureFile);
        }
    }
}