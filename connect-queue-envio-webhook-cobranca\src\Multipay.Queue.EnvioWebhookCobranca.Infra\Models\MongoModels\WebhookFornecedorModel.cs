﻿using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels
{
    [BsonIgnoreExtraElements]
    public class WebhookFornecedorModel : BaseModel
    {
        public WebhookFornecedorModel(string fornecedor, string tipo, string bodyJSON, string iPOrigem)
        {
            Fornecedor = fornecedor;
            Tipo = tipo;
            BodyJSON = bodyJSON;
            IPOrigem = iPOrigem;
            DataCriacaoUtc = DateTime.UtcNow;
        }

        public string Fornecedor { get; private set; }
        public string Tipo { get; private set; }
        public string BodyJSON { get; private set; }
        public string IPOrigem { get; private set; }
        public DateTime DataCriacaoUtc { get; private set; }
        public DateTime? DataAtualizacaoUtc { get; private set; }
        //public DateTime Data { get; private set; }
    }
}