﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services
{
    public class WebhookRecebidoSicoobService : IWebhookRecebidoService
    {
        private readonly ILogger<WebhookRecebidoSicoobService> logger;

        public TipoBanco TipoBanco => TipoBanco.SICOOB;

        public WebhookRecebidoSicoobService(ILogger<WebhookRecebidoSicoobService> logger)
        {
            this.logger = logger;
        }

        public Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
        {
            if (@event.Tipo == "recebimento/pix")
            {
                var service = serviceProvider.GetRequiredService<CobrancaRecebidaSicoobService>();
                return service.Processar($"{@event.Data}", @event.IdWebhook);
            }
            if (@event.Tipo == "pagamento/pix")
            {
                var service = serviceProvider.GetRequiredService<IPagamentoConfirmadoSicoobService>();
                return service.ProcessarPagamento(@event);
            }
            logger.LogWarning($"Mensagem não tratada. {@event}");

            return Task.FromResult(true);
        }
    }
}