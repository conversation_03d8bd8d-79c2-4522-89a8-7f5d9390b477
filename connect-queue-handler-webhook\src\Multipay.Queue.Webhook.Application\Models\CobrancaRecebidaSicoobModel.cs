﻿using Multipay.Queue.Webhook.Core.Interfaces;

namespace Multipay.Queue.Webhook.Application.Models
{
    public class CobrancaRecebidaSicoobModel
    {
        public PIXSicoob[] PIX { get; set; } = Array.Empty<PIXSicoob>();

        public class PIXSicoob : IPIXData
        {
            public string EndToEndId { get; set; } = string.Empty;

            public string Txid { get; set; } = string.Empty;

            public string Valor { get; set; } = string.Empty;

            public DateTime Horario { get; set; }

            public string CpfPagador => string.Empty;

            public string NomePagador => string.Empty;
            public string? IspbPagador => null;
            public string? NomeBancoPagador => null;
            public string? AgenciaBancoPagador => null;
            public string? ContaBancoPagador => null;
        }
    }
}