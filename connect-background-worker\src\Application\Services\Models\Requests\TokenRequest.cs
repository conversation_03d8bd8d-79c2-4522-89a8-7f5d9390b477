using System.Text.Json.Serialization;

namespace Application.Services.Models.Requests;

public class TokenRequest
{   
    [JsonPropertyName("grant_type")]
    public string GrantType { get; } 

    [<PERSON>sonPropertyName("username")]
    public string UserName { get; }

    [JsonPropertyName("password")]
    public string Password { get; }

    private const string GrantTypePassword = "password";

    public TokenRequest(string userName, string password)
    {
        GrantType = GrantTypePassword;
        UserName = userName;
        Password = password;
    }
}
