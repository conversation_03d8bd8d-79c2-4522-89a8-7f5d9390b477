﻿namespace Multipay.Queue.Webhook.Application.Models.AnspacePay
{
    public class PIXAnspacePayPagamentoRequestModelV1
    {
        public string EndToEndId { get; set; } = null!;
        public string IdempotencyKey { get; set; } = null!;
        public string Status { get; set; } = null!;
        public ErrorData? Error { get; set; }
        public float Amount { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime FinishedAt { get; set; }

        public class ErrorData
        {
            public string Code { get; set; } = null!;
            public string Description { get; set; } = null!;
        }
    }

    public class PixPagamentoRecebidoModel
    {
        public string EndToEndId { get; set; } = null!;
        public double Amount { get; set; }
        public string Key { get; set; } = null!;
        public string IdempotencyKey { get; set; } = null!;
        public string? InitiationType { get; set; }
        public string? OperationType { get; set; }
        public string? EndToEndIdOriginal { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime FinishedAt { get; set; }
        public string Description { get; set; } = null!;
        public ParticipantData Payer { get; set; } = null!;
        public ParticipantData Beneficiary { get; set; } = null!;

        public class ParticipantData
        {
            public string Number { get; set; } = null!;
            public string Branch { get; set; } = null!;
            public string Type { get; set; } = null!;
            public ParticipantBank Participant { get; set; } = null!;
            public HolderData Holder { get; set; } = null!;

            public class ParticipantBank
            {
                public string? Name { get; set; }
                public string Ispb { get; set; } = null!;
            }

            public class HolderData
            {
                public string Name { get; set; } = null!;
                public string Document { get; set; } = null!;
                public string Type { get; set; } = null!;
            }
        }
    }
}