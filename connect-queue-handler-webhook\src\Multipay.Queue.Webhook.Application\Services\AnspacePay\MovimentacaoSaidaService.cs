﻿using Multipay.Queue.Webhook.Application.Models.AnspacePay;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Emails;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.AnspacePay
{
    public interface IMovimentacaoSaidaService
    {
        Task<bool> ProcessarPagamento(string sourceParamenters, string idWebhook);
    }

    internal class MovimentacaoSaidaService : IMovimentacaoSaidaService
    {
        private readonly IUnitOfWork unitOfWork;
        private readonly ICentralizadorLogsService centralizadorLogsService;
        private readonly IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado;
        private readonly IEmailHandler emailHandler;

        public MovimentacaoSaidaService(IUnitOfWork unitOfWork,
            ICentralizadorLogsService centralizadorLogsService,
            IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado,
            IEmailHandler emailHandler)
        {
            this.unitOfWork = unitOfWork;
            this.centralizadorLogsService = centralizadorLogsService;
            this.busNotificarSaqueConfirmado = busNotificarSaqueConfirmado;
            this.emailHandler = emailHandler;
        }

        private TipoBanco TipoBanco => TipoBanco.AnspacePay;

        public async Task<bool> ProcessarPagamento(string sourceParamenters, string idWebhook)
        {
            var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<PIXAnspacePayPagamentoRequestModelV1>(sourceParamenters)!;

            var solicitacaoSaque = await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigo(parameters.IdempotencyKey);
            if (solicitacaoSaque == null)
                solicitacaoSaque = await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(parameters.EndToEndId);

            if (solicitacaoSaque == null)
            {
                await EnviarEmailSuporteOperacionalSaqueNaoLocalizado(parameters);
                return true;
            }

            if (parameters.Status == "PIX_EFFECTIVE")
            {
                // Marcando como em processamento
                await unitOfWork.SolicitacaoSaqueRepository.UpdateMudarStatusEmProcessamento(solicitacaoSaque.Id);
                // Concluindo transação
                await unitOfWork.SolicitacaoSaqueRepository.UpdateMudarStatusConcluido(solicitacaoSaque.Id, null, idWebhook);

                busNotificarSaqueConfirmado.PublicarNotificacaoCliente(solicitacaoSaque.Id, idWebhook, true, "Completed");
            }
            else
            {
                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"BANK STATUS: {parameters.Status}");
                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Aguardando webhook de estorno");

                if (parameters.Error != null)
                {
                    try
                    {
                        return await ProcessarPagamentoComFalha(solicitacaoSaque, parameters.Error, idWebhook);
                    }
                    catch (Exception e)
                    {
                        centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Falha ao realizar estorno - " + e.ToString());
                    }
                }
            }

            return true;
        }

        private Task EnviarEmailSuporteOperacionalSaqueNaoLocalizado(PIXAnspacePayPagamentoRequestModelV1 parameters, string operacao = "ENVIOPIX")
        {
            var body = $"Não foi encontrada nenhuma solicitação de saque com o código de autenticação informado!<br/><br />Código de autenticação: {parameters.EndToEndId}<br/><br />JSON:{parameters.ToJson()}";
            return EnviarEmailSuporteOperacional($"CallBack {TipoBanco} - {operacao} - Solicitação saque não encontrada", body);
        }

        private Task EnviarEmailSuporteOperacional(string titulo, string body)
        {
            this.emailHandler.EnviarEmailSuporteOperacional(titulo, body);
            return Task.CompletedTask;
        }

        public async Task<bool> ProcessarPagamentoComFalha(
            ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque,
            PIXAnspacePayPagamentoRequestModelV1.ErrorData error,
            string idWebhook)
        {
            string mensagemJustificativa = $"{error.Code} - {error.Description}";
            centralizadorLogsService.AdicionarLogWebhookFornecedorSolicitacaoSaque(idWebhook, solicitacaoSaque.Id, mensagemJustificativa);
            if (solicitacaoSaque.DataEstorno == null)
                await unitOfWork.SolicitacaoSaqueRepository.EstornarSaque(solicitacaoSaque.Id, mensagemJustificativa);

            busNotificarSaqueConfirmado.PublicarNotificacaoCliente(solicitacaoSaque.Id, idWebhook, false, "REJECTED TRANSACTION");
            return true;
        }
    }
}