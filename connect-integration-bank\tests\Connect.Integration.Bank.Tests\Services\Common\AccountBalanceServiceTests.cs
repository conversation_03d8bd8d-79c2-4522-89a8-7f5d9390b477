using Connect.Integration.Bank.Services.Common.Models.Requests;
using Connect.Integration.Bank.Strategy.Interfaces;
using Connect.Integration.Bank.Tests.Fixtures;
using FluentAssertions;

namespace Connect.Integration.Bank.Tests.Services.Common;

public class AccountBalanceServiceTests : IClassFixture<ServicesFixture>
{
    private readonly IIntegrationBankService _sut;
    private readonly ServicesFixture _fixture;

    public AccountBalanceServiceTests(ServicesFixture fixture)
    {
        _fixture = fixture;
        _sut = fixture.IntegrationBankService;
    }

    [Fact]
    public async Task ShouldExecuteDelBankSuccess()
    {
        // Arrange        
        var credential = _fixture.CredentialDelBankAccount;

        var request = new AccountBalanceRequest(credential);

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();
        response.Result?.Amount.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task ShouldExecuteCelcoinSuccess()
    {
        // Arrange        
        var credential = _fixture.CredentialCelcoinAccount;

        var request = new AccountBalanceRequest(credential);

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();
        response.Result?.Amount.Should().BeGreaterThan(0);
    }
}
