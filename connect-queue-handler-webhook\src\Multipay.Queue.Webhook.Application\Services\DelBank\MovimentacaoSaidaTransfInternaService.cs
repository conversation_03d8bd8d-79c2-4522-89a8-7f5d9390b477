﻿using Multipay.Queue.Webhook.Application.Models.DelBank;
using Multipay.Queue.Webhook.Application.Services.ContaGrafica;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Emails;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.DelBank
{
    public interface IMovimentacaoSaidaTransfInternaService
    {
        Task<bool> ProcessarTransferenciaEntreContas(string idWebhook, TransferenciaInternaRecebidoModel parameters);
    }

    internal class MovimentacaoSaidaTransfInternaService :
        MovimentacaoSaidaBaseService<TransferenciaInternaRecebidoModel>,
        IMovimentacaoSaidaTransfInternaService
    {
        public MovimentacaoSaidaTransfInternaService(IUnitOfWork unitOfWork, IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IEmailHandler emailHandler) : base(unitOfWork, busNotificarSaqueConfirmado, emailHandler)
        {
        }

        protected override TipoBanco TipoBanco => TipoBanco.DelBank;
    }
}