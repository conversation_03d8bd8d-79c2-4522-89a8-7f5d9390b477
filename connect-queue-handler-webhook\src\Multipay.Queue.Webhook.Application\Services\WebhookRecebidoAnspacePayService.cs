﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.AnspacePay;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.AnspacePay;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services
{
    public class WebhookRecebidoAnspacePayService : IWebhookRecebidoService
    {
        public TipoBanco TipoBanco => TipoBanco.AnspacePay;

        private readonly ILogger<WebhookRecebidoAnspacePayService> logger;

        public WebhookRecebidoAnspacePayService(ILogger<WebhookRecebidoAnspacePayService> logger)
        {
            this.logger = logger;
        }

        public Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
        {
            if (@event.Tipo == "pix/recebimento")
            {
                 var service = serviceProvider.GetRequiredService<IMovimentacaoEntradaService>();
                 return service.ProcessarRecebimentoPIX($"{@event.Data}", @event.IdWebhook);
            }
            if (@event.Tipo == "pix-indirect/confirmacao-recebimento")
            {
                var service = serviceProvider.GetRequiredService<CobrancaRecebidaService>();
                return service.Processar($"{@event.Data}", @event.IdWebhook);
            }
            if (@event.Tipo?.StartsWith("pix/pagamento") == true)
            {
                var service = serviceProvider.GetRequiredService<IMovimentacaoSaidaService>();

                return service.ProcessarPagamento($"{@event.Data}", @event.IdWebhook);
            }
            if (@event.Tipo?.StartsWith("pix-indirect/confirmacao-transferencia") == true)
            {
                var service = serviceProvider.GetRequiredService<IPagamentoConfirmadoService>();

                return service.ProcessarPagamento(@event);
            }

            if (@event.Tipo == "transferencia-interna")
            {
                var service = serviceProvider.GetRequiredService<IMovimentacaoSaidaTransfInternaService>();
                return service.ProcessarTransferenciaEntreContas(@event.IdWebhook, $"{@event.Data}".To<TransferenciaInternaRecebidoModel>()!);
            }

            logger.LogWarning($"Mensagem não tratada. {@event}");

            return Task.FromResult(true);
        }
    }
}