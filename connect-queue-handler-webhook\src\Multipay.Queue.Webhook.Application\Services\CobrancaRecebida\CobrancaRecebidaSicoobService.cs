﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Responses;
using Multipay.Queue.Webhook.Application.Models;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Multipay.Service.Criptography;
using Newtonsoft.Json;
using Serilog;
using Serilog.Context;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaSicoobService : CobrancaRecebidaService<CobrancaRecebidaSicoobModel, CobrancaRecebidaSicoobModel.PIXSicoob>
    {
        public CobrancaRecebidaSicoobService(
            ILogger<CobrancaRecebidaSicoobService> logger,
            ICobrancaService cobrancaService,
            Multipay.Infrastructure.Plugin.Sicoob.Services.ICobrancaService pluginsCobrancaService,
            Multipay.Infrastructure.Plugin.Sicoob.Services.IPixService pluginPixService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente,
            CriptoService criptoService) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.SICOOB)
        {
            this.logger = logger;
            this.pluginCobrancaService = pluginsCobrancaService;
            this.pluginPixService = pluginPixService;
            this.criptoService = criptoService;
        }

        protected override IEnumerable<CobrancaRecebidaSicoobModel.PIXSicoob> ListPix => Parameters!.PIX;

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);

        private Lazy<Task<CobrancaResponseModel?>>? lazyCobranca = null;
        private readonly ILogger<CobrancaRecebidaSicoobService> logger;
        private readonly Multipay.Infrastructure.Plugin.Sicoob.Services.ICobrancaService pluginCobrancaService;
        private readonly Multipay.Infrastructure.Plugin.Sicoob.Services.IPixService pluginPixService;
        private readonly CriptoService criptoService;

        private async Task<CobrancaResponseModel?> ObterCobrancaPortxid(CobrancaModel cobranca, string txid)
        {
            if (lazyCobranca == null)
            {
                lazyCobranca = new Lazy<Task<CobrancaResponseModel?>>(async () =>
                {
                    if ((cobranca.TransacaoPix?.IdContaBancariaEmpresa ?? 0) == 0)
                    {
                        CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Não foi possível obter o ClientId da conta Sicoob, talvez não consiga obter os dados do pagador");
                        return null;
                    }

                    var clientId = string.Empty;
                    var contaBancariaEmpresa = await UnitOfWork.ContaBancariaRepository.ObterContaBancariaEmpresa(cobranca.TransacaoPix!.IdContaBancariaEmpresa!.Value);
                    if (contaBancariaEmpresa == null)
                    {
                        CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Não foi possível obter o ClientId da conta Sicoob, talvez não consiga obter os dados do pagador");
                        return null;
                    }
                    clientId = contaBancariaEmpresa.UsernameAPI ?? string.Empty;

                    if (contaBancariaEmpresa.IsEncrypted)
                        clientId = await criptoService.Decrypt(clientId);

                    var response = await pluginCobrancaService.Get(clientId, txid);

                    CobrancaResponseModel? cobrancaResponseModel = (CobrancaResponseModel)response.Result;

                    return cobrancaResponseModel;
                });
            }
            return await lazyCobranca.Value;
        }

        protected override async ValueTask<string> ObterCpfPagador(CobrancaModel cobranca, CobrancaRecebidaSicoobModel.PIXSicoob dadosPIX)
        {
            var cobrancaResponseModel = await ObterCobrancaPortxid(cobranca, dadosPIX.Txid);

            LogContext.PushProperty("CobrancaSicoob", cobrancaResponseModel, true);

            var documentoPagador = cobrancaResponseModel?.PIX?.FirstOrDefault()?.Pagador?.Cpf;

            documentoPagador ??= cobrancaResponseModel?.PIX?.FirstOrDefault()?.Pagador?.Cnpj;

            logger.LogInformation("[SICOOB] Documento do pagador: {DocumentoPagador}", documentoPagador);

            return documentoPagador ?? "99999999999";

            //return cobrancaResponseModel?.Devedor?.Cpf ?? string.Empty;
        }

        protected override async ValueTask<string> ObterNomePagador(CobrancaModel cobranca, CobrancaRecebidaSicoobModel.PIXSicoob dadosPIX)
        {
            var cobrancaResponseModel = await ObterCobrancaPortxid(cobranca, dadosPIX.Txid);

            return cobrancaResponseModel?.PIX?.FirstOrDefault()?.Pagador?.Nome ?? string.Empty;

            //return cobrancaResponseModel?.Devedor?.Nome ?? string.Empty;
        }

        protected override async ValueTask<ResultEstornoBanco> ProcessarDevolucaoBancoAsync(CobrancaModel cobranca)
        {
            try
            {
                if ((cobranca.TransacaoPix?.IdContaBancariaEmpresa ?? 0) == 0)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Não foi possível obter o ClientId da conta Sicoob");
                    return ResultEstornoBanco.Falha;
                }

                var clientId = string.Empty;
                var contaBancariaEmpresa = await UnitOfWork.ContaBancariaRepository.ObterContaBancariaEmpresa(cobranca.TransacaoPix!.IdContaBancariaEmpresa!.Value);
                if (contaBancariaEmpresa == null)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Não foi possível obter o ClientId da conta Sicoob.");
                    return ResultEstornoBanco.Falha;
                }
                clientId = contaBancariaEmpresa.UsernameAPI ?? string.Empty;

                if (contaBancariaEmpresa.IsEncrypted)
                    clientId = await criptoService.Decrypt(clientId);

                var endToEndId = ObterEndToEndId(Parameters!.PIX[0]);
                var valorPago = ObterValorPago(Parameters!.PIX[0]);

                var txId = cobranca.TransacaoPix!.TXId;

                var rs = await pluginPixService.SolicitarDevolucao(clientId, endToEndId!, txId, new Multipay.Infrastructure.Plugin.Sicoob.Models.Requests.PixSolicitarDevolucaoRequestModel
                {
                    Valor = valorPago
                });

                if (rs.Success)
                {
                    return ResultEstornoBanco.Sucesso;
                }
                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, $"Falha na devolucao -> {rs.Result}");
                return ResultEstornoBanco.Falha;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Falha ao processar devolucao de cobranca: {id}", cobranca.Id);
                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, ex.ToString());
                return ResultEstornoBanco.Falha;
            }
        }
    }
}