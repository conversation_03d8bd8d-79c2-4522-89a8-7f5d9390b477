﻿using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    internal abstract class BaseRepository<T>
    {
        protected readonly IDatabase database;

        public BaseRepository(IDatabase database)
        {
            this.database = database;
        }

        /// <summary>
        /// Insere e retorna o id, do registro inserido
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public virtual Task<object> Add(T item)
        {
            return database.InsertAsync(item);
        }

        public virtual Task<int> Remove(int id)
        {
            return database.DeleteAsync(id);
        }

        public virtual Task<int> Update(T item)
        {
            return database.UpdateAsync(item);
        }

        public virtual Task<T> FindByID(int id)
        {
            return database.SingleOrDefaultAsync<T>(id);
        }

        internal virtual Task<IAsyncReader<T>> FindAll(Sql where)
        {
            return database.QueryAsync<T>(where);
        }
    }
}