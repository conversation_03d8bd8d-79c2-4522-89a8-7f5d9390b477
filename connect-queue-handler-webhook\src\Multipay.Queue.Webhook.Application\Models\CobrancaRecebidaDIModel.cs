﻿using Multipay.Queue.Webhook.Core.Interfaces;

namespace Multipay.Queue.Webhook.Application.Models
{
    public class CobrancaRecebidaDIModel : IPIXData
    {
        public string CustomId { get; set; } = string.Empty;
        public string Id { get; set; } = string.Empty;
        public string Paymentmethod { get; set; } = string.Empty;
        public int Installments { get; set; }
        public string PaymentDate { get; set; } = string.Empty;
        public int Total { get; set; }
        public int TotalPaid { get; set; }
        public string Original_currency { get; set; } = string.Empty;
        public string Payment_currency { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public bool Paid_by_cripto_currency { get; set; }
        public string? Hash { get; set; }
        public string? Address { get; set; }

        public string? IspbPagador => null;
        public string? NomeBancoPagador => null;
        public string Txid => Id;

        public string Valor => $"{TotalPaid}";

        public string CpfPagador => string.Empty;

        public string NomePagador => string.Empty;
        public string? AgenciaBancoPagador => null;
        public string? ContaBancoPagador => null;

        public string EndToEndId => string.Empty;
    }
}