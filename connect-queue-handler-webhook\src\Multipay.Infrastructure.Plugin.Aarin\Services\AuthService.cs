﻿using Microsoft.Extensions.Caching.Distributed;
using Multipay.Infrastructure.Plugin.Aarin.Abstractions;
using Multipay.Infrastructure.Plugin.Aarin.Models;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Service.Criptography;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace Multipay.Infrastructure.Plugin.Aarin.Services
{
    internal class AuthService(IHttpClientFactory httpClientFactory, IDistributedCache distributedCache, CriptoService criptoService) : IAuthService
    {
        private readonly Semaphore semaphore = new(1, 1);

        public async ValueTask<string?> GetAccessToken(ContaBancariaEmpresaModel contaBancariaEmpresa, bool gerarNovoToken, CancellationToken cancellationToken)
        {
            var cacheItem = await TryGetValue(contaBancariaEmpresa.Id);

            if (gerarNovoToken || IsTokenExpired(await cacheItem.UseAccessToken(criptoService)))
            {
                semaphore.WaitOne();
                
                try
                {
                    cacheItem = await TryGetValue(contaBancariaEmpresa.Id);

                    if (gerarNovoToken || IsTokenExpired(await cacheItem.UseAccessToken(criptoService)))
                    {
                        cacheItem = await RefreshAccessTokenAsync(contaBancariaEmpresa, gerarNovoToken);
                    }
                }
                finally
                {
                    semaphore.Release();
                }
            }
            
            return await cacheItem.UseAccessToken(criptoService);
        }

        private async Task<BoxToken> RefreshAccessTokenAsync(ContaBancariaEmpresaModel contaBancariaEmpresa, bool gerarNovoToken, int retry = 0)
        {
            var clientid = contaBancariaEmpresa.UsernameAPI;
            var clientsecret = contaBancariaEmpresa.PasswordAPI;

            if (clientid is null || clientsecret is null)
                return new();

            if (contaBancariaEmpresa.IsEncrypted)
            {
                clientid = await criptoService.Decrypt(clientid);
                clientsecret = await criptoService.Decrypt(clientsecret);
            }

            var httpClient = httpClientFactory.CreateClient(nameof(TipoBanco.BancoAarin));

            HttpResponseMessage? response = null;

            var cacheItem = await TryGetValue(contaBancariaEmpresa.Id);

            if (!gerarNovoToken && cacheItem.IsValid)
            {
                if (!IsTokenExpired(await cacheItem.UseRefreshToken(criptoService)))
                {
                    response = await httpClient.PostAsJsonAsync("baas/auth/refresh", new
                    {
                        RefreshToken = await cacheItem.UseRefreshToken(criptoService)
                    });
                }

                await RemoveCache(contaBancariaEmpresa.Id);
            }

            response ??= await httpClient.PostAsync("baas/auth/token/login", new FormUrlEncodedContent(
            [
                new("grant_type", "client_credentials"),
                new("client_id", clientid),
                new("client_secret", clientsecret)
            ]));

            TokenResponseModel? responseModel = null;

            if (response.IsSuccessStatusCode)
            {
                responseModel = await response.Content.ReadFromJsonAsync<TokenResponseModel>();
            }

            if ((int)response.StatusCode > 499)
            {
                if (retry < 5)
                {
                    await Task.Delay(++retry * 250);
                    return await RefreshAccessTokenAsync(contaBancariaEmpresa, gerarNovoToken, retry);
                }

                var msg = "não foi possivel gerar token para integracao com aarin apos 5 tentativas";

                throw new Exception(msg);
            }

            cacheItem = await BoxToken.Build(criptoService, contaBancariaEmpresa.IsEncrypted, responseModel?.AccessToken!, responseModel?.RefreshToken!);

            await AddCache(
                contaBancariaEmpresa.Id,
                cacheItem,
                responseModel!.ExpiresIn);

            return cacheItem;
        }

        private static bool IsTokenExpired(string? accessToken)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(accessToken))
                {
                    return true;
                }

                var jwtToken = new JwtSecurityToken(accessToken);
                var expirationDate = jwtToken.ValidTo.ToLocalTime();

                // Adicionar uma margem de segurança de 120 segundos
                return expirationDate < DateTime.Now.AddSeconds(-120);
            }
            catch
            {
                return true;
            }
        }

        public Func<int, string> KeyTokenCache = id => $"token:aarin:{id}";
        
        private async Task<BoxToken> TryGetValue(int id)
        {
            var encodedCachedTimeUtc = await distributedCache.GetAsync(KeyTokenCache(id));

            if (encodedCachedTimeUtc == null)
                return new();

            return JsonSerializer.Deserialize<BoxToken>(Encoding.UTF8.GetString(encodedCachedTimeUtc)) ?? new();
        }

        private async Task AddCache(int id, BoxToken boxToken, int expiresIn)
        {
            // cache.Add(id, value);

            byte[] encodedCurrentTimeUtc = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(boxToken));
            var options = new DistributedCacheEntryOptions()
            .SetAbsoluteExpiration(TimeSpan.FromSeconds(expiresIn));

            await distributedCache.SetAsync(KeyTokenCache(id), encodedCurrentTimeUtc, options);
        }

        private async Task RemoveCache(int id)
        {
            await distributedCache.RemoveAsync(KeyTokenCache(id));
        }

        private class BoxToken
        {
            public BoxToken()
            {
                IsValid = false;

            }

            public bool IsEncrypted { get; set; }
            public string? AccessToken { get; set; }
            public string? RefreshToken { get; set; }
            public bool IsValid { get; set; }

            public static async Task<BoxToken> Build(CriptoService criptoService, bool isEncrypted, string accessToken, string refreshToken)
            {
                var boxToken = new BoxToken
                {
                    IsValid = true,
                    IsEncrypted = isEncrypted,
                    AccessToken = accessToken,
                    RefreshToken = refreshToken
                };
                
                if (isEncrypted)
                {
                    boxToken.AccessToken = await criptoService.Encrypt(accessToken);
                    boxToken.RefreshToken = await criptoService.Encrypt(refreshToken);
                }
                
                return boxToken;
            }

            public async Task<string?> UseAccessToken(CriptoService criptoService)
            {
                if (string.IsNullOrWhiteSpace(AccessToken))
                    return null;
                
                return IsEncrypted ? await criptoService.Decrypt(AccessToken!) : AccessToken!;
            }

            public async Task<string> UseRefreshToken(CriptoService criptoService)
            {
                return IsEncrypted ? await criptoService.Decrypt(RefreshToken!) : RefreshToken!;
            }
        }
    }
}
