﻿using Microsoft.Extensions.Logging;
using MongoDB.Bson.IO;
using Multipay.Queue.Webhook.Application.Models.Aarin;
using Multipay.Queue.Webhook.Core.Entities;
using Multipay.Queue.Webhook.Core.Enums;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco.Repositories;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services.Aarin;
public class PixReversalService : IPixReversalService
{
    private readonly ILogger<PixReversalService> _logger;
    private readonly INewUnitOfWork _unitOfWork;
    private readonly IPixRepository _depositPixRepository;
    private readonly ICustomerRepository _customerRepository;
    private readonly IWebhookRepository _webhookRepository;
    private readonly IBusRefundPixNotification _producer;

    public PixReversalService(
        ILogger<PixReversalService> logger, 
        INewUnitOfWork unitOfWork, 
        IPixRepository depositPixRepository, 
        ICustomerRepository customerRepository,
        IWebhookRepository webhookRepository,
        IBusRefundPixNotification producer
        )
    {
        _logger = logger;
        _unitOfWork = unitOfWork;
        _depositPixRepository = depositPixRepository;
        _customerRepository = customerRepository;
        _producer = producer;
        _webhookRepository = webhookRepository;
    }

    public async Task<bool> VerifyPixCorrelationAsync<T>(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, CancellationToken cancellationToken)
    {
        var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<T>($"{@event.Data}");
        if (parameters is null)
        {
            return false;
        }

        var correlationIdInfo = parameters.GetType().GetProperties().FirstOrDefault(p => p.Name == behavior.CorrelationIdAttributeName);
        if (correlationIdInfo is null)
        {
            return false;
        }

        var correlationIdValue = (string?)correlationIdInfo.GetValue(parameters);
        if (string.IsNullOrWhiteSpace(correlationIdValue))
        {
            return false;
        }

        var refundPix = await _depositPixRepository.GetPixDepositRefundByCorrelationIdAsync(correlationIdValue, cancellationToken);
        if (refundPix is null)
        {
            return false;
        }
        return true;
    }
     public async Task<bool> ProcessReversalPixAsync<T>(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, CancellationToken cancellationToken)
    {
        try
        {
            string? statusValue = string.Empty;

            _logger.LogInformation("[{0}] Processo de Devolucao", nameof(PixReversalService));


            if (@event.Data is null)
            {
                _logger.LogError("[{0}] Não foi possivel obter os dadoss atraves dos parametros, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                return false;
            }

            var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<T>($"{@event.Data}");
            if (parameters is null)
            {
                _logger.LogError("[{0}] Não foi possivel descerilizar objeto obido, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                return false;
            }

            if (behavior.CheckCorrelationId && string.IsNullOrWhiteSpace(behavior.CorrelationIdAttributeName))
            {
                _logger.LogError("[{0}] EndToEnd setado como true mas não existe configuração para nome do atributo endToend, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                return false;
            }

            if (behavior.CheckStatus && string.IsNullOrWhiteSpace(behavior.StatusAttributeName))
            {
                _logger.LogError("[{0}] Status setado como true mas não existe configuração para nome do atributo status, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                return false;
            }

            var correlationIdInfo = parameters.GetType().GetProperties().FirstOrDefault(p => p.Name == behavior.CorrelationIdAttributeName);
            if (correlationIdInfo is null)
            {
                _logger.LogError("[{0}] Correlation ID obrigatorio para realização do estorno do pix, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                return false;
            }

            var correlationIdValue = (string?)correlationIdInfo.GetValue(parameters);
            if (string.IsNullOrWhiteSpace(correlationIdValue))
            {
                _logger.LogError("[{0}] Correlation ID valor encontrado para o atributo status não pode ser nulo, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                return false;
            }

            if (behavior.CheckCorrelationId)
            {
                var refundPix = await _depositPixRepository.GetPixDepositRefundByCorrelationIdAsync(correlationIdValue, cancellationToken);
                if (refundPix is null)
                {
                    _logger.LogWarning("[{0}] Devolução não foi encontrada como sendo um devolução realizada pelo cliente, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                    return false;
                }

                if (refundPix.Status != "Pending")
                {
                    _logger.LogError("[{0}] Devolução de pix já processada, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                    return false;
                }

                if (behavior.CheckStatus) 
                {
                    var statusInfo = parameters.GetType().GetProperties().FirstOrDefault(p => p.Name == behavior.StatusAttributeName);

                    if (statusInfo is null)
                    {
                        _logger.LogError("[{0}] Status tem validação obrigatoria para a entidade financeira porem não foi encontrado no payload, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                       
                        await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                        _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");
                        
                        return false;
                    }

                    statusValue = (string?)statusInfo.GetValue(parameters).ToString();

                    if (string.IsNullOrWhiteSpace(statusValue))
                    {
                        _logger.LogError("[{0}] Status valor encontrado para o atributo status não pode ser nulo, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                        
                        await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                        _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");
                        
                        return false;
                    }

                    if (statusValue != behavior.CompletedStatusName)
                    {
                        _logger.LogError("[{0}] Devolução não processada pelo banco, Tipo: {1}", nameof(PixReversalService), @event.Tipo);

                        await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                        _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");

                        return false;
                    }

                    var qrCode = await _depositPixRepository.GetQrCodePixByEndToEndAsync(refundPix.QrCodeId, cancellationToken);
                    if (qrCode is null)
                    {
                        _logger.LogError("[{0}] QrCode associado não encontrado, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                        
                        await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                        _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");
                        
                        return false;
                    }

                    var billing = await _depositPixRepository.GetBillgingByIdAsync(refundPix.BillingId, cancellationToken);
                    if (billing is null)
                    {
                        _logger.LogError("[{0}] Cobrança associado não encontrado, Tipo: {1}", nameof(PixReversalService), @event.Tipo);

                        await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                        _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");

                        return false;
                    }

                    var balance = await _customerRepository.GetBalanceByCustomerIdAsync(refundPix.CustomerId, refundPix.CoinId, cancellationToken);
                    if (balance is null)
                    {
                        _logger.LogError("[{0}] Tipo de operação para cliente:{1} não encontrado, Tipo: {2}", nameof(PixReversalService), refundPix.CustomerId, @event.Tipo);
                        
                        await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                        _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");

                        return false;
                    }

                    if (behavior.CheckValue)
                    {
                        var valueInfo = parameters.GetType().GetProperties().FirstOrDefault(p => p.Name == behavior.ValueAttributeName);
                        if (valueInfo is null)
                        {
                            _logger.LogError("[{0}] Valor do transação não encontrada, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                            
                            await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                            _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");

                            return false;
                        }

                        var value = (string?)valueInfo.GetValue(parameters).ToString();
                        if (string.IsNullOrWhiteSpace(value))
                        {
                            _logger.LogError("[{0}] Valor do transação não encontrada no retorno do webhook, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                            
                            await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                            _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");

                            return false;
                        }

                        if (!decimal.TryParse(value, out var resultValue))
                        {
                            _logger.LogError("[{0}] Não foi possivel converter o valor recebido em um numero decimal , Tipo: {1}", nameof(PixReversalService), @event.Tipo);

                            await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                            _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");

                            return false;
                        }

                        if (refundPix.OriginalValue != resultValue)
                        {
                            _logger.LogError("[{0}] Valor da devolução diferente do valor do webhook, Tipo: {1}", nameof(PixReversalService), @event.Tipo);
                            
                            await SetPixRefundNotProcessed(refundPix, "Error", cancellationToken);
                            _producer.PublishRefundPixNotification(refundPix.FinancialInstitutionId?.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Error");

                            return false;
                        }

                        await SetSuccefullAsync(@event, behavior, statusValue, refundPix, billing, balance, cancellationToken);

                        _producer.PublishRefundPixNotification(refundPix.Id.ToString(), refundPix.EndToEndPayment, refundPix.TXId, "Refunded");

                        return true;
                    }
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{0}] Erro não mapeado durante a execução do consumer, Tipo: {1}, Message:{2}", nameof(PixReversalService), @event.Tipo, ex.Message);

            _unitOfWork.Rollback();

            return false;
        }

    }

    private async Task SetSuccefullAsync(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, string? statusValue, PixDepositRefundEntity? refundPix, BillingEntity? billing, BalanceCustomerCurrencyEntity? balance, CancellationToken cancellationToken)
    {
        var newBalanceValue = balance.GetDebitBalance(billing.NetValue);
        var billingLog = GetBillingLog(refundPix);
        var customerExit = GetCustomerExit(refundPix);
        var customerAccountingStatement = GetCustomerAccountingStatement(refundPix, balance, newBalanceValue);
        var webhookProvider = GetWebhookProvider(@event, behavior);

        _unitOfWork.BeginTransaction();

        await _depositPixRepository.UpdatePixDepositRefundAsync(refundPix!.Id.ToString(), statusValue, DateTime.UtcNow, cancellationToken);
        await _depositPixRepository.UpdateCurrentBillingAsync(refundPix!.BillingId, DateTime.UtcNow, cancellationToken);
        await _depositPixRepository.UpdateCurrenBankSlipAsync(refundPix!.BillingId, DateTime.UtcNow, cancellationToken);
        await _depositPixRepository.UpdatBitcoinTransactionAsync(refundPix!.BillingId, DateTime.UtcNow, cancellationToken);
        await _depositPixRepository.AddBillingLogAsync(billingLog, cancellationToken);
        await _customerRepository.UpdateBalanceAsync(refundPix.CustomerId, refundPix.CoinId, newBalanceValue, DateTime.UtcNow, cancellationToken);
        var customerExitId = await _customerRepository.AddCustomerExitAsync(customerExit, cancellationToken);
        var customerAccountingStatementId = await _customerRepository.AddCustomerAccountingStatementAsync(customerAccountingStatement, cancellationToken);
        var webhookProviderId = await _webhookRepository.AddWebhookProviderEntity(webhookProvider, cancellationToken);
        await _customerRepository.AddCustomerOutputAccountingStatementAsync(new CustomerOutputAccountingStatementEntity(customerExitId, customerAccountingStatementId), cancellationToken);
        await _webhookRepository.AddWebhookRefundPixEntity(new WebhookRefundPixEntity(refundPix.Id, webhookProviderId, DateTime.UtcNow, "rabbitmq"), cancellationToken);

        _unitOfWork.Commit();
    }

    private static WebhookProviderEntity GetWebhookProvider(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior)
    {
        return new WebhookProviderEntity(behavior.FinancialInstitution, "REFUNDPIX", Newtonsoft.Json.JsonConvert.SerializeObject(@event.Data), behavior.OriginIp, DateTime.UtcNow);
    }

    private async Task SetPixRefundNotProcessed(PixDepositRefundEntity? refundPix, string? statusValue, CancellationToken cancellationToken)
    {
        _unitOfWork.BeginTransaction();

        await _depositPixRepository.UpdatePixDepositRefundAsync(refundPix!.Id.ToString(), statusValue, DateTime.UtcNow, cancellationToken);

        _unitOfWork.Commit();

        _logger.LogInformation("[{0}] Processo finalizado com status:{1}", nameof(PixReversalService), statusValue);
    }

    private static CustomerAccountingStatementEntity GetCustomerAccountingStatement(PixDepositRefundEntity refundPix, BalanceCustomerCurrencyEntity balance, decimal newBalanceValue)
    {
        return new CustomerAccountingStatementEntity
        {
            CustomerId = refundPix.CustomerId,
            AccountingMovementTypeId = (byte)AccountingMovementType.Debit,
            CoinId = refundPix.CoinId,
            Description = refundPix.Description,
            LastBalance = balance.Balance,
            Value = refundPix.OriginalValue,
            Balance = newBalanceValue,
            TransactionDate = DateTime.UtcNow
        };
    }

    private static CustomerExitEntity GetCustomerExit(PixDepositRefundEntity refundPix) =>
        new()
        {
            CustomerId = refundPix.CustomerId,
            CoinId = refundPix.CoinId,
            Description = refundPix.Description,
            Value = refundPix.OriginalValue,
            RateValue = refundPix.RateValue,
            TariffValue = refundPix.TariffValue,
            TransactionDate = DateTime.UtcNow,
            UnlockDate = DateTime.UtcNow,
            TxId = refundPix.TXId,
        };

    private static LogBillingEntity GetBillingLog(PixDepositRefundEntity? refundPix) =>
        new()
        {
            BillingId = refundPix.BillingId,
            Date = DateTime.UtcNow,
            Description = "Deposito estornado",
            Exclusive = true
        };
}
