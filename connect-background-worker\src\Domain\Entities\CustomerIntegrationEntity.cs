namespace Domain.Entities;

public class CustomerIntegrationEntity
{
    public byte Id { get; }
    public int CustomerId { get; }
    public bool IsActive { get; }
    public string AuthorizationToken { get; }
    public string BalanceUrl { get; }

    public CustomerIntegrationEntity(byte id, int customerId, bool isActive, string authorizationToken, string balanceUrl)
    {
        Id = id;
        CustomerId = customerId;
        IsActive = isActive;
        AuthorizationToken = authorizationToken;
        BalanceUrl = balanceUrl;
    }
}
