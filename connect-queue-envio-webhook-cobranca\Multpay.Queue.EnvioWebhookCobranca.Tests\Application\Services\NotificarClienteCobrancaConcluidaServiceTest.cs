﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Moq.Protected;
using Multipay.Queue.EnvioWebhookCobranca.Application.Services;
using Multipay.Queue.EnvioWebhookCobranca.Application.Services.CobrancaRecebida;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Bus;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Redis;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Services;
using PetaPoco;
using System.Data.SqlClient;
using System.Net;

namespace Multpay.Queue.EnvioWebhookCobranca.Tests.Application.Services;
public class NotificarClienteCobrancaConcluidaServiceTest
{
    [Fact]
    public async Task NotificarClienteCobrancaConcluidaService_EnviarNotificacao_ShouldBeProcess()
    {
        try
        {
            // Arrange
            var httpClient = new Mock<IHttpClientFactory>();
            var logger = NullLoggerFactory.Instance.CreateLogger<NotificarClienteCobrancaConcluidaService>();
            var loggerDataBase = NullLoggerFactory.Instance.CreateLogger<UnitOfWork>();
            var connection = new SqlConnection("data source=*************;TrustServerCertificate=True;persist security info=True;user id=db_hmltest;password=****************;database=BETBANK_DB_HML;MultipleActiveResultSets=True;pooling=false;min pool size=100;max pool size=1000;Integrated Security=false;Application Name=MultiPay Painel Cliente;");
            connection.Open();
            var dataBase = new Database(connection);
            var unitOfWork = new UnitOfWork(loggerDataBase, dataBase);
            var cobrancaService = new CobrancaService(unitOfWork);
            var publishNotificarClienteRetry = new Mock<IPublishNotificarClienteRetry>();
            var centralizadorLogsService = new Mock<ICentralizadorLogsService>();
            var redisConnectionFactory = new Mock<Multipay.Queue.EnvioWebhookCobranca.Infra.Redis.IRedisConnectionFactory>();

            var tempBase = new Mock<StackExchange.Redis.IDatabase>();
            redisConnectionFactory.Setup(x => x.GetDatabaseAsync()).ReturnsAsync(tempBase.Object);

            var response = new HttpResponseMessage(HttpStatusCode.OK);
            var httpMessageHandlerMock = new Mock<HttpMessageHandler>();
                httpMessageHandlerMock
                 .Protected()
                 .Setup<Task<HttpResponseMessage>>(
                 "SendAsync",
                 ItExpr.IsAny<HttpRequestMessage>(),
                 ItExpr.IsAny<CancellationToken>()).ReturnsAsync(response);

            //var dataHttpClient = new Mock<HttpClient>();
            var dataHttpClient = new HttpClient(httpMessageHandlerMock.Object);
            httpClient.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(dataHttpClient);

            publishNotificarClienteRetry.Setup(x => x.Publicar(It.IsAny<CobrancaConfirmadaNotificarClienteEvent>()));

            // Act
            var act = new NotificarClienteCobrancaConcluidaService(logger, httpClient.Object, cobrancaService, publishNotificarClienteRetry.Object, centralizadorLogsService.Object, redisConnectionFactory.Object);
            await act.EnviarNotificacao(new Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Events.CobrancaConfirmadaNotificarClienteEvent("e96225be07b7485695de9e62bb0c0da0", 100, ""), false);

            // Assert
            centralizadorLogsService.Verify(mock => mock.AdicionarLogCobranca(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<string?>()), Times.AtLeastOnce);

            connection.Close();
            connection.Dispose();

        }
        catch (Exception ex)
        {
            throw ex;
        }
    }
}
