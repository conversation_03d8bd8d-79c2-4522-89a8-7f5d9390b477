﻿using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Repositories.MongoRepositories;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels
{
    public interface ICentralizadorLogsService
    {
        string AdicionarLogWebhookFornecedorSolicitacaoSaque(string idWebhook, int idSolicitacaoSaque, string acao);

        string AdicionarLogSolicitacaoSaque(int idSolicitacaoSaque, string texto);

        string AdicionarLogEnvioWebhookSolicitacaoSaque(int idSolicitacaoSaque, string codigoSolicitacaoSaque, string acao, string urlWebhook, string requestJson, string httpResponseCode, string responseJson);

        void Salvar();
    }

    internal partial class CentralizadorLogsService : ICentralizadorLogsService
    {
        private readonly Dictionary<Type, List<BaseModel>> _dicLogs = new();
        private readonly IEnvioWebhookSolicitacaoSaqueSaqueRepository envioWebhookSolicitacaoSaqueSaqueRepository;
        private readonly ILogSolicitacaoSaqueRepository logSolicitacaoSaqueRepository;
        private readonly IWebhookFornecedorSolicitacaoSaqueRepository webhookFornecedorSolicitacaoSaqueRepository;

        public Help Util { get; }

        public CentralizadorLogsService(
            IEnvioWebhookSolicitacaoSaqueSaqueRepository envioWebhookSolicitacaoSaqueSaqueRepository,
            ILogSolicitacaoSaqueRepository logSolicitacaoSaqueRepository,
            IWebhookFornecedorSolicitacaoSaqueRepository webhookFornecedorSolicitacaoSaqueRepository)
        {
            this.TrackId = System.Guid.NewGuid().ToString("N");
            this.Util = new Help(this);

            this.envioWebhookSolicitacaoSaqueSaqueRepository = envioWebhookSolicitacaoSaqueSaqueRepository;
            this.logSolicitacaoSaqueRepository = logSolicitacaoSaqueRepository;
            this.webhookFornecedorSolicitacaoSaqueRepository = webhookFornecedorSolicitacaoSaqueRepository;
        }

        public string TrackId { get; private set; }

        public void Add<T>(T document) where T : BaseModel
        {
            if (!_dicLogs.TryGetValue(typeof(T), out List<BaseModel>? logs))
            {
                _dicLogs.Add(typeof(T), logs = new List<BaseModel>());
            }

            document.TrackId = this.TrackId;
            logs.Add(document);
        }

        public void SetTrackId(string trackId)
        {
            this.TrackId = trackId;

            _dicLogs.Values
                .SelectMany(item => item)
                .ToList()
                .ForEach(item => item.TrackId = this.TrackId);
        }

        public void Salvar()
        {
            if (!_dicLogs.Any()) return;

            lock (_dicLogs)
            {
                foreach (var values in _dicLogs.Values.Where(itens => itens.Any()))
                {
                    switch (values.First())
                    {
                        case LogSolicitacaoSaqueModel _:
                            logSolicitacaoSaqueRepository.InsertMany(values.Select(item => (LogSolicitacaoSaqueModel)item));
                            break;

                        case EnvioWebhookSolicitacaoSaqueModel:
                            envioWebhookSolicitacaoSaqueSaqueRepository.InsertMany(values.Select(item => (EnvioWebhookSolicitacaoSaqueModel)item));
                            break;

                        case WebhookFornecedorSolicitacaoSaqueModel _:
                            webhookFornecedorSolicitacaoSaqueRepository.InsertMany(values.Select(item => (WebhookFornecedorSolicitacaoSaqueModel)item));
                            break;
                    }
                }

                _dicLogs.Clear();
            }
        }

        public void Update<T>(string id, Action<T?> updateModel) where T : BaseModel
        {
            if (_dicLogs.TryGetValue(typeof(T), out List<BaseModel>? logs))
            {
                var model = logs.FirstOrDefault(item => item.Id == id);
                updateModel(model as T);
            }
        }

        public string AdicionarLogEnvioWebhookSolicitacaoSaque(int idSolicitacaoSaque, string codigoSolicitacaoSaque, string acao, string urlWebhook, string requestJson, string httpResponseCode, string responseJson)
        {
            var document = new EnvioWebhookSolicitacaoSaqueModel(idSolicitacaoSaque, codigoSolicitacaoSaque, acao, urlWebhook, requestJson, httpResponseCode, responseJson);

            Add(document);

            return document.Id;
        }

        public string AdicionarLogSolicitacaoSaque(int idSolicitacaoSaque, string texto)
        {
            var document = new LogSolicitacaoSaqueModel(idSolicitacaoSaque, texto);

            Add(document);

            return document.Id;
        }

        public string AdicionarLogWebhookFornecedorSolicitacaoSaque(string idWebhook, int idSolicitacaoSaque, string acao)
        {
            var document = new WebhookFornecedorSolicitacaoSaqueModel(idSolicitacaoSaque, acao, idWebhook);

            Add(document);

            return document.Id;
        }
    }
}