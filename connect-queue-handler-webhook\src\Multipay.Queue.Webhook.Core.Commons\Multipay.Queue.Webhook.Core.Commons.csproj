<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Update="Resources\WebhookResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>WebhookResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\WebhookResource.en-US.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\WebhookResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>WebhookResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Multipay.Queue.Webhook.Core\Multipay.Queue.Webhook.Core.csproj" />
	</ItemGroup>
</Project>
