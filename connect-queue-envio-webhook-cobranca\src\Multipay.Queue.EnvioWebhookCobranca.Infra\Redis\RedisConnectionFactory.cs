﻿using StackExchange.Redis;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Redis
{
    public class RedisConnectionFactory : IRedisConnectionFactory
    {
        private readonly string _connectionString;
        private readonly Lazy<Task<ConnectionMultiplexer>> _lazyConnection;

        public RedisConnectionFactory(string connectionString)
        {
            _connectionString = connectionString;
            _lazyConnection = new Lazy<Task<ConnectionMultiplexer>>(async () =>
            {
                return await ConnectionMultiplexer.ConnectAsync(_connectionString);
            });
        }

        public async Task<ConnectionMultiplexer> GetConnectionAsync()
        {
            return await _lazyConnection.Value;
        }

        public async Task<IDatabase> GetDatabaseAsync()
        {
            var connection = await GetConnectionAsync();
            return connection.GetDatabase();
        }
    }
}