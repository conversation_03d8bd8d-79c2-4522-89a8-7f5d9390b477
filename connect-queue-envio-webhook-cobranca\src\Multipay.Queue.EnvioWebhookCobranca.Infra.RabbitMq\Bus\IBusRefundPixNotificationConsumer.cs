﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Settings;
using Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq.Events;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Bus.Interfaces;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq.Bus;
public interface IBusRefundPixNotificationConsumer : IBusConsumer<RefundPixNotificationEvent>
{
}
internal class BusRefundPixNotificationConsumer : EventBusConsumerRabbitMQ<RefundPixNotificationEvent>, IBusRefundPixNotificationConsumer
{
    public BusRefundPixNotificationConsumer(
        IRabbitMQPersistentConnection persistentConnection,
        IOptions<RabbitMqSettings> options,
        ILogger<BusRefundPixNotificationConsumer> logger)
        : base(persistentConnection, logger, options.Value.RefundPixNotification)
    { }
}
