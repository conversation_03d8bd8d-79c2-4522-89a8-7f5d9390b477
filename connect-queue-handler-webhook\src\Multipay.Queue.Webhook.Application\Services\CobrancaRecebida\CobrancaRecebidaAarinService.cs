﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Aarin.Abstractions;
using Multipay.Queue.Webhook.Application.Models;
using Multipay.Queue.Webhook.Application.Models.BancoDock;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaAarinService : CobrancaRecebidaService<CobrancaRecebidaAarinModel, CobrancaRecebidaAarinModel>
    {
        private readonly ILogger<CobrancaRecebidaAarinService> logger;
        private readonly IBrasilApiService brasilApiService;
        private readonly IEstornarQrCodeService estornarService;

        public CobrancaRecebidaAarinService(
            ILogger<CobrancaRecebidaAarinService> logger,
            IBrasilApiService brasilApiService,
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IEstornarQrCodeService estornarService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.Aarin)
        {
            this.logger = logger;
            this.brasilApiService = brasilApiService;
            this.estornarService = estornarService;
        }

        protected override IEnumerable<CobrancaRecebidaAarinModel> ListPix => new List<CobrancaRecebidaAarinModel> { Parameters! };

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);

        protected override async ValueTask<ResultEstornoBanco> ProcessarDevolucaoBancoAsync(CobrancaModel cobranca)
        {
            try
            {
                var valorPago = ObterValorPago(Parameters!);

                var contaBancariaEmpresa = await UnitOfWork.ContaBancariaRepository.ObterContaBancariaEmpresa(cobranca.TransacaoPix!.IdContaBancariaEmpresa!.Value);

                if (contaBancariaEmpresa is null)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Não foi possível obter ContaBancariaEmpresa");
                    return ResultEstornoBanco.Falha;
                }

                var endToEndId = ObterEndToEndId(Parameters!)!;

                var rs = await estornarService.SolicitarDevolucao(contaBancariaEmpresa, endToEndId, valorPago, false, CancellationToken.None);

                if (!rs.Item1)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Falha na devolucao -> " + rs.Item2);

                    if (rs.Item2.Contains("not authorized"))
                    {
                        rs = await estornarService.SolicitarDevolucao(contaBancariaEmpresa, endToEndId, valorPago, true, CancellationToken.None);

                        if (!rs.Item1)
                        {
                            CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Falha na devolucao -> " + rs.Item2);

                            return ResultEstornoBanco.Falha;
                        }
                    }

                    if (!rs.Item1)
                        return ResultEstornoBanco.Falha;
                }
                
                var txId = cobranca.TransacaoPix!.TXId;

                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Registrando novo endtoend da cobrança estornada");

                await CobrancaService.AtualizarEndToEndPorTxId(txId, rs.Item2); //novo endtoend

                return ResultEstornoBanco.Sucesso;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Falha ao processar devolucao de cobranca: {id}", cobranca.Id);

                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, ex.ToString());

                return ResultEstornoBanco.Falha;
            }
        }

        protected override async ValueTask<(string?, string?)> ObterDadosBancoPagador(CobrancaRecebidaAarinModel dadosPIX)
        {
            var dados = await base.ObterDadosBancoPagador(dadosPIX);
            var nomeBanco = dados.Item2;

            var bancoParticipantesPix = await brasilApiService.ConsultarPorIspb(dados.Item1);

            if (bancoParticipantesPix != null)
            {
                nomeBanco = bancoParticipantesPix.Nome;
            }

            return (dados.Item1, nomeBanco);
        }
    }
}