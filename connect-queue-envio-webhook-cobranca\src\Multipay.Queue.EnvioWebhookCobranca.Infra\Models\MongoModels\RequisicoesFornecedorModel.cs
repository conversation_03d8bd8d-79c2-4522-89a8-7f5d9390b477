﻿using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels
{
    [BsonIgnoreExtraElements]
    public class RequisicoesFornecedorModel : BaseModel
    {
        public const string CollectionName = nameof(RequisicoesFornecedorModel);

        public RequisicoesFornecedorModel(string fornecedor, string tipo, string descricao, string bodyRequest, int? httpResponseCode = null, string? bodyResponse = null)
        {
            Fornecedor = fornecedor;
            Tipo = tipo;
            Descricao = descricao;
            BodyRequest = bodyRequest;
            HttpResponseCode = httpResponseCode;
            BodyResponse = bodyResponse;
        }

        public string Fornecedor { get; private set; }
        public string Tipo { get; private set; }
        public string Descricao { get; private set; }
        public string BodyRequest { get; private set; }
        public int? HttpResponseCode { get; private set; }
        public string? BodyResponse { get; private set; }
        public DateTime DataCriacaoUtc { get; private set; }
        public DateTime? DataAtualizacaoUtc { get; private set; }
        //public DateTime DataRequisicao { get; set; }

        public override Dictionary<string, object?> GetUpdateDictionary()
        {
            return new()
            {
                { nameof(HttpResponseCode), HttpResponseCode },
                { nameof(BodyResponse), BodyResponse }
            };
        }

        public void SetResponseData(int? httpResponseCode, string bodyResponse)
        {
            HttpResponseCode = httpResponseCode;
            BodyResponse = bodyResponse;
        }
    }
}