{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"Multipay.Service.Criptography/1.0.0": {"dependencies": {"AWSSDK.SecretsManager": "3.7.304.6", "NETStandard.Library": "2.0.3", "System.Configuration": "*******"}, "runtime": {"Multipay.Service.Criptography.dll": {}}}, "AWSSDK.Core/3.7.304.21": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0"}, "runtime": {"lib/netstandard2.0/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.304.21"}}}, "AWSSDK.SecretsManager/3.7.304.6": {"dependencies": {"AWSSDK.Core": "3.7.304.21"}, "runtime": {"lib/netstandard2.0/AWSSDK.SecretsManager.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.304.6"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26919.2"}}}, "System.Threading.Tasks.Extensions/4.5.2": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "runtime": {"lib/netstandard2.0/System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.27129.4"}}}, "System.Configuration/*******": {"runtime": {"System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.3761.0"}}}, "System.Security/*******": {"runtime": {"System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.3761.0"}}}}}, "libraries": {"Multipay.Service.Criptography/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AWSSDK.Core/3.7.304.21": {"type": "package", "serviceable": true, "sha512": "sha512-MD1UcVQPbJmE/s3JJ2/TFc3tuhHqxn5XOMOCKS3WloXFmJM/oKW/e7FEWSmce65WghVKGkscIdCR5Ckh552o7A==", "path": "awssdk.core/3.7.304.21", "hashPath": "awssdk.core.3.7.304.21.nupkg.sha512"}, "AWSSDK.SecretsManager/3.7.304.6": {"type": "package", "serviceable": true, "sha512": "sha512-N890kwJN5hfwr9cCkRyHBnPTZ3FZ4uuySsj8fE2tKt1FALCgWBc/qUX5gZjcDhSxqQtwFFOF8wcP8toEXDFU4A==", "path": "awssdk.secretsmanager/3.7.304.6", "hashPath": "awssdk.secretsmanager.3.7.304.6.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Am6l4Vpn3/K32daEqZI+FFr96OlZkgwK2LcT3pZ2zWubR5zTPW3/FkO1Rat9kb7oQOa4rxgl9LJHc5tspCWfg==", "path": "microsoft.bcl.asyncinterfaces/1.1.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "path": "system.threading.tasks.extensions/4.5.2", "hashPath": "system.threading.tasks.extensions.4.5.2.nupkg.sha512"}, "System.Configuration/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}