using Application.Services.Models.Requests;
using Application.Services.Models.Responses;
using Refit;
using static Refit.BodySerializationMethod;

namespace Application.Services.Interfaces.Api;

public interface IConnectTokenApi
{
    [Post("/token")]
    Task<IApiResponse<TokenResponse>> GetAccessTokenAsync(
        [Body(UrlEncoded)] TokenRequest request, 
        CancellationToken cancellationToken);    
}
