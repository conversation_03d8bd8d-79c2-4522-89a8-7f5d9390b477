using System;

namespace PigPag.Manager.Models
{
    [Serializable]
    public class WebhookProgressInfo
    {
        public string JobId { get; set; }
        public int Total { get; set; }
        public int Processed { get; set; }
        public int Success { get; set; }
        public int Failed { get; set; }
        public string Status { get; set; } // Running, Completed, Failed
        public string CurrentItem { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public DateTime LastUpdate { get; set; }
        public string UserLogin { get; set; }
        
        public decimal ProgressPercentage => Total > 0 ? (decimal)Processed / Total * 100 : 0;
        
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.Now.Subtract(StartTime);
        
        public string DurationFormatted => Duration.ToString(@"mm\:ss");
        
        public bool IsCompleted => Status == "Completed";
        
        public bool IsRunning => Status == "Running";
    }
}
