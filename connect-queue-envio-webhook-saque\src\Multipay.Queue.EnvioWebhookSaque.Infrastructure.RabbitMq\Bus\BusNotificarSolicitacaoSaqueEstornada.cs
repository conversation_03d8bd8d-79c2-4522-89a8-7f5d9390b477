﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Settings;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Bus
{
    internal class BusSolicitacaoSaqueEstornadaNotificarClienteRetry : EventBusPublishRabbitMQ, IPublishSolicitacaoSaqueEstornadoRetry
    {
        public BusSolicitacaoSaqueEstornadaNotificarClienteRetry(
            IRabbitMQPersistentConnection persistentConnection,
            ILogger<BusSolicitacaoSaqueEstornadaNotificarClienteRetry> logger,
            IOptions<RabbitMqSettings> options)
            : base(persistentConnection, logger, options.Value.Setup.EventBusRetryCount, options.Value.SolicitacaoSaquesEstornadoRetry) { }

        public void Publish(string urlAtualizacao, string codigoSaque, string? customId, string? customUserId, Guid guid, string? codigoTransacao) => base.Publish(new SolicitacaoSaquesEstornadoEvent(urlAtualizacao, codigoSaque, customId, customUserId, guid, codigoTransacao));
    }
}