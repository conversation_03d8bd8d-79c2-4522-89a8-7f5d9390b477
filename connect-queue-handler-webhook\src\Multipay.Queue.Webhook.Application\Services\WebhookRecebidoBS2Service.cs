﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services
{
    public class WebhookRecebidoBS2Service : IWebhookRecebidoService
    {
        private readonly ILogger<WebhookRecebidoBS2Service> logger;

        public TipoBanco TipoBanco => TipoBanco.BS2;

        public WebhookRecebidoBS2Service(ILogger<WebhookRecebidoBS2Service> logger)
        {
            this.logger = logger;
        }

        public Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
        {
            if (@event.Tipo == "pix/recebimento")
            {
                var service = serviceProvider.GetRequiredService<CobrancaRecebidaBS2Service>();
                return service.Processar($"{@event.Data}", @event.IdWebhook);
            }

            if (@event.Tipo == "pix/pagamento")
            {
                var service = serviceProvider.GetRequiredService<IPagamentoConfirmadoBS2Service>();
                return service.ProcessarPagamento(@event);
            }
            if (@event.Tipo == "pix/validacaochave")
            {
                var service = serviceProvider.GetRequiredService<IValidacaoChaveRecebidoBS2Service>();
                return service.Processar(@event);
            }

            logger.LogWarning($"Mensagem não tratada. {@event}");

            return Task.FromResult(true);
        }
    }
}