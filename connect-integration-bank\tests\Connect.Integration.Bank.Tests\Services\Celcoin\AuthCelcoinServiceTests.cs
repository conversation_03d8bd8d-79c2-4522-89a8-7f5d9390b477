using Connect.Integration.Bank.Services.Celcoin.Interfaces;
using Connect.Integration.Bank.Tests.Fixtures;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;

namespace Connect.Integration.Bank.Tests.Services.Celcoin;

public class AuthCelcoinServiceTests : IClassFixture<ServicesFixture>
{
    private readonly IAuthCelcoinService _sut;
    private readonly ServicesFixture _servicesFixture;

    public AuthCelcoinServiceTests(ServicesFixture fixture)
    {
        _servicesFixture = fixture;
        _sut = fixture.ServiceProvider.GetRequiredService<IAuthCelcoinService>();
    }

    [Fact]
    public async Task ShouldGetAccessTokenSuccess()
    {
        // Arrange
        var credential = _servicesFixture.CredentialCelcoinAccount;

        // Act
        var response = await _sut.GetAccessTokenAsync(credential, default);
        var responseFromCache = await _sut.GetAccessTokenAsync(credential, default);

        // Assert
        response.Success.Should().BeTrue();
        response.Result?.AccessToken.Should().NotBeNullOrEmpty();
        responseFromCache.Result?.AccessToken.Should().Be(response.Result?.AccessToken);
    }
}
