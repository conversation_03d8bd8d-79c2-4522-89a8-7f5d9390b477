﻿using Multipay.Queue.Webhook.Application.Models.AnspacePay;
using Multipay.Queue.Webhook.Application.Services.ContaGrafica;
using Multipay.Queue.Webhook.Core.Interfaces;

namespace Multipay.Queue.Webhook.Application.Models.BMP
{
    public class CallbackRecebidoModel : IPIXData, IMovimentacaoSaidaModel
    {
        public string? Prefixo { get; set; } = null;
        public DadosQueuecallback? QueueCallback { get; set; } = null;

        public class Lancamento
        {
            public string? CodigoMovimento { get; set; } = null;
            public string? DescricaoOperacao { get; set; } = null;
            public string? TipoLancto { get; set; } = null;
            public string? DescCliente { get; set; } = null;
            public string? CodOperacaoCli { get; set; } = null;
            public DateTime? DtMovimento { get; set; }
            public float VlrMovimento { get; set; }
        }

        public string? Ambiente { get; set; } = null;
        public string? Registro { get; set; } = null;
        public DadosDetalhes? Detalhes { get; set; } = null;

        public string Txid => Detalhes?.IdentificadorTransacao ?? string.Empty;

        public string Valor => $"{Detalhes?.VlrTransacao ?? 0}";

        public string CpfPagador => Detalhes?.PagadorDocumentoFederal ?? string.Empty;

        public string NomePagador => Detalhes?.PagadorNome ?? string.Empty;
        public string? AgenciaBancoPagador => null;
        public string? ContaBancoPagador => null;
        public string? IspbPagador => null;
        public string? NomeBancoPagador => null;
        public string EndToEndId => string.Empty;

        public string? CodigoSolicitacaoSaque => Detalhes?.CodOperacaoCli;
        public string CodigoAutenticacao => Detalhes?.CodigoAutenticacao ?? string.Empty;
        public string NSU => Detalhes?.NSU ?? string.Empty;

        public class DadosContaRecebedor
        {
            public string? Agencia { get; set; } = null;
            public string? AgenciaDigito { get; set; } = null;
            public string? Conta { get; set; } = null;
            public int? TipoConta { get; set; }
            public string? TipoContaDesc { get; set; } = null;
            public string? ContaDigito { get; set; } = null;
            public string? ContaPgto { get; set; } = null;
            public string? CodigoBanco { get; set; } = null;
            public int? TpPessoa { get; set; }
            public string? TpPessoaDesc { get; set; } = null;
            public string? DocumentoFederal { get; set; } = null;
            public string? Nome { get; set; } = null;
        }

        public class DadosDestinatario
        {
            public string? Agencia { get; set; } = null;
            public string? AgenciaDigito { get; set; } = null;
            public string? Conta { get; set; } = null;
            public int? TipoConta { get; set; }
            public string? TipoContaDesc { get; set; } = null;
            public string? ContaDigito { get; set; } = null;
            public string? ContaPgto { get; set; } = null;
            public string? CodigoBanco { get; set; } = null;
            public int? TpPessoa { get; set; }
            public string? TpPessoaDesc { get; set; } = null;
            public string? DocumentoFederal { get; set; } = null;
            public string? Nome { get; set; } = null;
        }

        public class DadosDetalhes
        {
            public string? Identificador { get; set; } = null;
            public string? CodigoMovimento { get; set; } = null;
            public int? FinlddCli { get; set; } = null;
            public string? FinlddCliDesc { get; set; } = null;
            public string? CodIdentdTransf { get; set; } = null;
            public int? TipoProcessamento { get; set; } = null;
            public string? TipoProcessamentoDesc { get; set; } = null;
            public string? CodigoAutenticacao { get; set; } = null;
            public int? NumControle { get; set; } = null;
            public string? DescCliente { get; set; } = null;
            public string? CodOperacaoCli { get; set; } = null;
            public DadosDestinatario? Destinatario { get; set; } = null;
            public double? VlrTransacao { get; set; } = null;
            public DateTime? DtTransacao { get; set; } = null;
            public string? TipoLancto { get; set; } = null;
            public List<DadosLancamento>? Lancamentos { get; set; } = null;
            public int? FinlddIF { get; set; } = null;
            public string? FinlddIFDesc { get; set; } = null;
            public string? LinhaDigitavel { get; set; } = null;
            public string? CodigoBarras { get; set; } = null;
            public double? ValorTitulo { get; set; } = null;
            public string? CodigoBoleto { get; set; } = null;
            public string? BancoOrigem { get; set; } = null;
            public string? AgenciaOrigem { get; set; } = null;
            public DateTime? DtRegistroDDA { get; set; } = null;
            public bool Sucesso { get; set; }
            public string? ErroRegistro { get; set; } = null;
            public string? Mensagem { get; set; } = null;
            public string? CodigoCancelamento { get; set; } = null;
            public string? Mensagens { get; set; } = null;
            public string? DescricaoOperacao { get; set; } = null;
            public DadosContaDestino? ContaDestino { get; set; } = null;
            public DadosContaRecebedor? ContaRecebedor { get; set; } = null;
            public string? CodigoMovimentoOrigem { get; set; } = null;
            public string? CodigoOperacaoCliente { get; set; } = null;
            public string? DescricaoCliente { get; set; } = null;
            public string? InformacoesAdicionais { get; set; } = null;
            public string? IdentificadorTransacao { get; set; } = null;
            public string? CodigoTransacao { get; set; } = null;
            public string? MotivoEstorno { get; set; } = null;
            public string? NSU { get; set; } = null;
            public string? PagadorNome { get; set; } = null;
            public string? PagadorDocumentoFederal { get; set; } = null;
            public string? PagadorParticipante { get; set; } = null;
            public string? PagadorAgencia { get; set; } = null;
            public string? RecebedorChave { get; set; } = null;
            public DadosDestinatario? RecebedorConta { get; set; } = null;
            public string? ChaveDestino { get; set; } = null;
            public string? NomeDestino { get; set; } = null;
            public string? DocumentoFederalDestino { get; set; } = null;
            public string? IdentificOperador { get; set; } = null;
        }

        public class DadosContaDestino
        {
            public string? Agencia { get; set; } = null;
            public string? AgenciaDigito { get; set; } = null;
            public string? Conta { get; set; } = null;
            public int? TipoConta { get; set; }
            public string? TipoContaDesc { get; set; } = null;
            public string? ContaDigito { get; set; } = null;
            public string? ContaPgto { get; set; } = null;
            public string? CodigoBanco { get; set; } = null;
            public int? TpPessoa { get; set; }
            public string? TpPessoaDesc { get; set; } = null;
            public string? DocumentoFederal { get; set; } = null;
            public string? Nome { get; set; } = null;
        }

        public class DadosLancamento
        {
            public string? CodigoMovimento { get; set; } = null;
            public string? DescricaoOperacao { get; set; } = null;
            public string? TipoLancto { get; set; } = null;
            public string? DescCliente { get; set; } = null;
            public string? CodOperacaoCli { get; set; } = null;
            public DateTime? DtMovimento { get; set; }
            public int? VlrMovimento { get; set; }
        }

        public class DadosMensagem
        {
            public string? Ambiente { get; set; } = null;
            public string? Registro { get; set; } = null;
            public DadosDetalhes? Detalhes { get; set; } = null;
        }

        public class DadosQueuecallback
        {
            public string? Codigo { get; set; } = null;
            public string? CodigoParceiro { get; set; } = null;
            public DadosMensagem? Mensagem { get; set; } = null;
        }

        internal bool VerificarEstornoPix()
        {
            return Registro?.ToUpperInvariant() == "ESTORNOPIX";
        }

        public string ObterOperacao()
        {
            string? operacao = null;
            if (string.IsNullOrEmpty(QueueCallback?.Mensagem?.Registro))
                operacao = Registro;
            else
                operacao = QueueCallback.Mensagem.Registro;

            return operacao?.ToUpperInvariant() ?? string.Empty;
        }

        internal string? ObterAgenciaRecebedor()
        {
            try
            {
                return Detalhes?.Identificador?.Substring(3, 4);
            }
            catch
            {
                return null;
            }
        }

        internal string? ObterContaRecebedor()
        {
            try
            {
                return Convert.ToInt64(Detalhes?.Identificador?.Substring(7, 7)).ToString();
            }
            catch
            {
                return null;
            }
        }

        internal string? ObterDigitoContaRecebedor()
        {
            try
            {
                return Detalhes?.Identificador?.Substring(14, 1);
            }
            catch
            {
                return null;
            }
        }
    }
}