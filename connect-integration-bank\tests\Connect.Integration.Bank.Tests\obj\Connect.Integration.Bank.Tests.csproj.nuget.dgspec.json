{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\tests\\Connect.Integration.Bank.Tests\\Connect.Integration.Bank.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\src\\Connect.Integration.Bank\\Connect.Integration.Bank.csproj": {"version": "1.3.6", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\src\\Connect.Integration.Bank\\Connect.Integration.Bank.csproj", "projectName": "Connect.Integration.Bank", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\src\\Connect.Integration.Bank\\Connect.Integration.Bank.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\src\\Connect.Integration.Bank\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1903", "NU1904"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Connect.Cryptography.Service": {"target": "Package", "version": "[1.0.7, )"}, "Connect.Log.Extensions": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[9.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Polly": {"target": "Package", "version": "[8.5.0, )"}, "Refit": {"target": "Package", "version": "[8.0.0, )"}, "Refit.HttpClientFactory": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.3, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.16, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\tests\\Connect.Integration.Bank.Tests\\Connect.Integration.Bank.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\tests\\Connect.Integration.Bank.Tests\\Connect.Integration.Bank.Tests.csproj", "projectName": "Connect.Integration.Bank.Tests", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\tests\\Connect.Integration.Bank.Tests\\Connect.Integration.Bank.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\tests\\Connect.Integration.Bank.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\src\\Connect.Integration.Bank\\Connect.Integration.Bank.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-integration-bank\\src\\Connect.Integration.Bank\\Connect.Integration.Bank.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1903", "NU1904"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoFixture": {"target": "Package", "version": "[4.18.1, )"}, "Bogus": {"target": "Package", "version": "[35.6.1, )"}, "FluentAssertions": {"target": "Package", "version": "[6.12.2, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "Moq.AutoMock": {"target": "Package", "version": "[3.5.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.2, )"}, "coverlet.msbuild": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}