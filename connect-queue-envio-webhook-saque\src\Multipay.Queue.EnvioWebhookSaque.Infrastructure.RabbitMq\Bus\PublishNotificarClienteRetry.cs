﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Settings;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.Webhook.Infrastructure.RabbitMq.Bus;

internal class PublishNotificarClienteRetry : EventBusPublishRabbitMQ, IPublishNotificarClienteRetry
{
    public PublishNotificarClienteRetry(
        IRabbitMQPersistentConnection persistentConnection,
        ILogger<PublishNotificarClienteRetry> logger, IOptions<RabbitMqSettings> options) : base(
        persistentConnection,
        logger,
        options.Value.Setup.EventBusRetryCount,
        options.Value.NotificarClienteRetry)
    {
    }

    public void Publicar(SaqueConfirmadoNotificarClienteEvent @event)
    {
        Publish(@event);
    }
}