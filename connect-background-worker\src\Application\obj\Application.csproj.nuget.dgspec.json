{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Application\\Application.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Application\\Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Application\\Application.csproj", "projectName": "Application", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Application\\Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\Domain.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Infra\\Infra.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Infra\\Infra.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Connect.Log.Extensions": {"target": "Package", "version": "[1.2.0, )"}, "Datadog.Trace": {"target": "Package", "version": "[3.7.0, )"}, "Datadog.Trace.Bundle": {"target": "Package", "version": "[3.7.0, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Featuremanagement.AspNetCore": {"target": "Package", "version": "[4.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "Polly": {"target": "Package", "version": "[8.5.0, )"}, "Refit": {"target": "Package", "version": "[8.0.0, )"}, "Refit.HttpClientFactory": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Expressions": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Extensions.Logging.File": {"target": "Package", "version": "[3.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.Datadog.Logs": {"target": "Package", "version": "[0.5.4, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\Domain.csproj", "projectName": "Domain", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"ResultT": {"target": "Package", "version": "[1.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Infra\\Infra.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Infra\\Infra.csproj", "projectName": "Infra", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Infra\\Infra.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Infra\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Connect.Cryptography.Service": {"target": "Package", "version": "[1.0.7, )"}, "Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}