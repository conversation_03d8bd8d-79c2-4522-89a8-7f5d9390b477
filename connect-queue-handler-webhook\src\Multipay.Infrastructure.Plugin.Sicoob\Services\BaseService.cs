﻿using Multipay.Infrastructure.Plugin.Sicoob.Core;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Responses;
using Multipay.Service.Criptography;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Security.Cryptography.X509Certificates;
using System.Text;

namespace Multipay.Infrastructure.Plugin.Sicoob.Services
{
    internal class BaseService
    {
        protected readonly IEnvironment _environment;
        private readonly CriptoService criptoService;

        public BaseService(IEnvironment environment, Service.Criptography.CriptoService criptoService)
        {
            _environment = environment;
            this.criptoService = criptoService;
        }

        protected virtual HttpContent BuildHttpContent<T>(T model)
        {
            var json = JsonConvert.SerializeObject(model);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            return content;
        }

        protected virtual HttpClientHandler CreateClientHandler()
        {
            var certificate = new X509CertificateCollection() { _environment.GetCertificado(criptoService) };
            var handler = new HttpClientHandler();

            handler.ClientCertificates.Add(certificate[0]);
            handler.UseProxy = false;

            return handler;
        }

        protected async Task<HttpClient> CreateHttpClientConsumo(string clientId, IAuthService authService)
        {
            var client = new HttpClient(CreateClientHandler());

            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Add("x-sicoob-clientid", clientId);

            var token = await authService.GetToken(clientId);

            if (!token.Success)
                throw new Exception("Serviço SICOOB indisponível. Falha ao obter token de autenticação.");

            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {((TokenResponseModel?)token.Result)?.Access_token}");

            return client;
        }

        protected virtual T? GetObject<T>(HttpResponseMessage response)
        {
            string jsonString = response.Content.ReadAsStringAsync().Result;

            return JsonConvert.DeserializeObject<T>(jsonString);
        }
    }
}