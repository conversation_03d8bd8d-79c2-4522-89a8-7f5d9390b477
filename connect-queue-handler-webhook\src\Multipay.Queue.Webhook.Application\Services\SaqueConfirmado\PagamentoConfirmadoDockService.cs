﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.BancoDock;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado
{
    internal interface IPagamentoConfirmadoDockService : IPagamentoConfirmadoService
    {
    }

    internal class PagamentoConfirmadoDockService : PagamentoConfirmadoBaseService<WebhookRecebidoModel>, IPagamentoConfirmadoDockService
    {
        public PagamentoConfirmadoDockService(
            ILogger<PagamentoConfirmadoDockService> logger,
            IEstornarSaqueService estornarSaqueService,
            ICentralizadorLogsService centralizadorLogsService,
            IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IBrasilApiService brasilApiService,
            IUnitOfWork unitOfWork) : base(logger, centralizadorLogsService, busNotificarSaqueConfirmado, brasilApiService, estornarSaqueService, unitOfWork)
        {
        }

        protected override string NomeBanco => Bancos.Dock;

        protected override string ObterEndToEndId(WebhookRecebidoModel parameters) => parameters.Payload.TransactionCode;

        protected override Status? ObterStatus(WebhookRecebidoModel parameters)
        {
            return parameters.Origin switch
            {
                WebhookRecebidoModel.OriginEnum.PIX_TRANSFERS_DEBIT => (Status?)Status.EFETIVADO,
                WebhookRecebidoModel.OriginEnum.PIX_REVERSAL_DEBIT => (Status?)Status.REJEITADO,
                //(Status?)Status.ERRO, (Status?)Status.EXPIRADO,
                _ => null,
            };
        }
    }
}