﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels
{
    public abstract class BaseModel
    {
        public BaseModel()
        {
            Id = ObjectId.GenerateNewId().ToString();
        }

        public virtual Dictionary<string, object?> GetUpdateDictionary() => new();

        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; private set; } = string.Empty;

        public string? TrackId { get; set; }
    }
}