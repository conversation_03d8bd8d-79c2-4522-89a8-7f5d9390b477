﻿namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Setup
{
    internal class MongoStoreDatabaseSettings
    {
        public string ConnectionString { get; set; } = null!;
        public string DatabaseName { get; set; } = null!;
        public CollectionNames CollectionNames { get; set; } = null!;
    }

    internal class CollectionNames
    {
        public string LogCobranca { get; set; } = null!;
        public string CobrancasGeradas { get; set; } = null!;
        public string RequisicoesAPI { get; set; } = null!;
        public string RequisicoesBMP { get; set; } = null!;
        public string WebhookFornecedor { get; set; } = null!;
        public string LogSolicitacaoSaque { get; set; } = null!;
        public string EnvioWebhookCobranca { get; set; } = null!;
        public string RequisicoesFornecedor { get; set; } = null!;
        public string WebhookFornecedorCobranca { get; set; } = null!;
        public string EnvioWebhookSolicitacaoSaque { get; set; } = null!;
        public string WebhookFornecedorSolicitacaoSaque { get; set; } = null!;
    }
}