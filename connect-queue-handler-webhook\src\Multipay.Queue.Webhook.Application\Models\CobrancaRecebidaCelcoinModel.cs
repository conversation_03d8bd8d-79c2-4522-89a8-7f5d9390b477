﻿using System.Text.Json.Serialization;
using Multipay.Queue.Webhook.Core.Interfaces;
using Newtonsoft.Json;
using static Multipay.Queue.Webhook.Application.Models.DelBank.PixPagamentoRecebidoModel;

namespace Multipay.Queue.Webhook.Application.Models
{
    public class CobrancaRecebidaCelcoinModel : IPIXData
    {
        [JsonProperty("TxId")]
        public string Txid => Data?.TransactionIdentification.ToString() ?? string.Empty;

        [JsonProperty("Value")]
        public string Valor => Data?.Amount.ToString() ?? string.Empty;

        [JsonProperty("PayerDocument")]
        public string CpfPagador => Data?.DebitParty?.TaxId.ToString() ?? string.Empty;

        [JsonProperty("PayerName")]
        public string NomePagador => Data?.DebitParty?.Name.ToString() ?? string.Empty;

        [JsonProperty("E2EId")]
        public string EndToEndId => Data?.EndToEndId.ToString() ?? string.Empty;
        public string PixId { get; set; } = string.Empty;


        [JsonProperty("PayerBankIspb")]
        public string? IspbPagador => Data?.DebitParty?.Bank.ToString() ?? string.Empty;
        public string? NomeBancoPagador => null; // Será preenchido pelo BrasilApiService
        public string? ContaBancoPagador => Data?.DebitParty?.Account.ToString() ?? string.Empty;
        public string? AgenciaBancoPagador => Data?.DebitParty?.Branch.ToString() ?? string.Empty;

        [JsonProperty("body")]
        public Body Data { get; set; }
    }


    public class Body
    {

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("amount")]
        public double Amount { get; set; }

        [JsonPropertyName("clientCode")]
        public string ClientCode { get; set; }

        [JsonPropertyName("transactionIdentification")]
        public object TransactionIdentification { get; set; }

        [JsonPropertyName("endToEndId")]
        public string EndToEndId { get; set; }

        [JsonPropertyName("initiationType")]
        public string InitiationType { get; set; }

        [JsonPropertyName("paymentType")]
        public string PaymentType { get; set; }

        [JsonPropertyName("urgency")]
        public string Urgency { get; set; }

        [JsonPropertyName("transactionType")]
        public string TransactionType { get; set; }

        [JsonPropertyName("debitParty")]
        public DebitParty DebitParty { get; set; }

        [JsonPropertyName("creditParty")]
        public CreditParty CreditParty { get; set; }

        [JsonPropertyName("remittanceInformation")]
        public object RemittanceInformation { get; set; }

        [JsonPropertyName("currentBalance")]
        public double CurrentBalance { get; set; }

        [JsonPropertyName("oldBalance")]
        public double OldBalance { get; set; }

        [JsonPropertyName("dataInsercao")]
        public DateTime DataInsercao { get; set; }
    }

    public class CreditParty
    {
        [JsonPropertyName("bank")]
        public string Bank { get; set; }

        [JsonPropertyName("key")]
        public string Key { get; set; }

        [JsonPropertyName("account")]
        public string Account { get; set; }

        [JsonPropertyName("branch")]
        public string Branch { get; set; }

        [JsonPropertyName("taxId")]
        public string TaxId { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("accountType")]
        public string AccountType { get; set; }
    }

    public class DebitParty
    {
        [JsonPropertyName("bank")]
        public string Bank { get; set; }

        [JsonPropertyName("key")]
        public string Key { get; set; }

        [JsonPropertyName("account")]
        public string Account { get; set; }

        [JsonPropertyName("branch")]
        public string Branch { get; set; }

        [JsonPropertyName("taxId")]
        public string TaxId { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("accountType")]
        public string AccountType { get; set; }
    }
}

public class CelcoinWebhookReturn
{
    [JsonPropertyName("entity")]
    public string Entity { get; set; }

    [JsonPropertyName("createTimeStamp")]
    public DateTime CreateTimeStamp { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; }

    [JsonPropertyName("webhookId")]
    public string WebhookId { get; set; }
}


public class EventoCelcoin
{
    public string TipoBanco { get; set; }
    public string Tipo { get; set; }
    public string? ChanelType { get; set; }
    public string IdWebhook { get; set; }
    public Guid Id { get; set; }
    public DateTime CreationDate { get; set; }
    public CelcoinWebhookReturn Data { get; set; }

}
