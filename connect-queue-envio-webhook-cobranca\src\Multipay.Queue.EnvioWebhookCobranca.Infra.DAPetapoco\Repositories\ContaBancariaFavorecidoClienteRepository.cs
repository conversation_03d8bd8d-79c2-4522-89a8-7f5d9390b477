﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    public interface IContaBancariaFavorecidoClienteRepository
    {
        Task<ContaBancariaFavorecidoClienteModel?> ObterContaBancariaFavorecidoClientePorId(int value1, int value2);
    }

    internal class ContaBancariaFavorecidoClienteRepository : IContaBancariaFavorecidoClienteRepository
    {
        private readonly ILogger<UnitOfWork> logger;
        private readonly IDatabase database;

        public ContaBancariaFavorecidoClienteRepository(ILogger<UnitOfWork> logger, IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public async Task<ContaBancariaFavorecidoClienteModel?> ObterContaBancariaFavorecidoClientePorId(int idFavorecidoCliente, int id)
        {
            try
            {
                using var reader = await database.QueryProcAsync<ContaBancariaFavorecidoClienteModel>("Painel.Select_ContaBancariaFavorecidoClientePorId", new
                {
                    idFavorecidoCliente,
                    id
                });

                if (await reader.ReadAsync())
                    return reader.Poco;

                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(ContaBancariaFavorecidoClienteRepository), database.LastCommand);
                throw;
            }
        }
    }
}