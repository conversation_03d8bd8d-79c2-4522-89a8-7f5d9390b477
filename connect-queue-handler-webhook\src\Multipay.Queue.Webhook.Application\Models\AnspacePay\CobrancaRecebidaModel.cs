﻿using Multipay.Queue.Webhook.Core.Interfaces;
using System.Globalization;

namespace Multipay.Queue.Webhook.Application.Models.AnspacePay
{
    public class Beneficiary
    {
        public string Number { get; set; }
        public string Branch { get; set; }
        public string Type { get; set; }
        public Participant Participant { get; set; }
        public Holder Holder { get; set; }
    }

    public class Holder
    {
        public string Name { get; set; }
        public string Document { get; set; }
        public string Type { get; set; }
    }

    public class Participant
    {
        public string Name { get; set; }
        public string Ispb { get; set; }
    }

    public class Payer
    {
        public string Number { get; set; }
        public string Branch { get; set; }
        public string Type { get; set; }
        public Participant Participant { get; set; }
        public Holder Holder { get; set; }
    }


    public class CobrancaRecebidaModel : IPIXData
    {
        public string IdempotencyKey { get; set; }
        public string TransactionId { get; set; }
        public string Key { get; set; }
        public string InitiationType { get; set; }
        public string OperationType { get; set; }
        public Beneficiary Beneficiary { get; set; }

        public int Nsu { get; set; }
        public string? CorrelationId { get; set; }
        public string? EndToEnd
        {
            get
            {
                try
                {
                    return IdempotencyKey;
                }
                catch (Exception)
                {
                    return ReferenceId ?? string.Empty;
                }
            }
        }
        public string ReferenceId { get; set; } = null!;
        public string Type { get; set; } = null!;
        public string Source { get; set; } = null!;
        public float Amount { get; set; }
        public DateTime CreatedAt { get; set; }
        public Payer Payer { get; set; } = null!;
        public ProofData Proof { get; set; } = null!;

        public string Txid
        {
            get
            {
                try
                {
                    return Guid.Parse(TransactionId!).ToString("N");
                }
                catch (Exception)
                {
                    return ReferenceId ?? string.Empty;
                }
            }
        }

        public string Valor => Amount.ToString(CultureInfo.GetCultureInfo("pt-BR"));

        public string CpfPagador => Proof == null ? Payer.Holder.Document : Proof.Payer.Holder.Document;

        public string NomePagador => Proof == null ? Payer.Holder.Name : Proof.Payer.Holder.Name!;

        public string? IspbPagador => Proof == null ? Payer.Participant.Ispb : Proof.Payer.Participant?.Ispb;
        public string? NomeBancoPagador => Proof == null ? Payer.Participant.Name : Proof.Payer.Participant?.Name;

        public string? ContaBancoPagador => Proof == null ? Payer.Branch : Proof.Payer.Branch;
        public string? AgenciaBancoPagador => Proof == null ? Payer.Number : Proof.Payer.Number;



        public string EndToEndId => Proof?.EndToEndId ?? EndToEnd!;

        public class PayerData
        {
            public string Name { get; set; } = null!;
            public string Document { get; set; } = null!;
        }

        public class ProofData
        {
            public string EndToEndId { get; set; } = null!;
            public string? Description { get; set; }
            public string TransactionId { get; set; } = null!;

            public string Key { get; set; } = null!;
            public float Amount { get; set; }
            public ParticipanteDate Payer { get; set; } = null!;
            public ParticipanteDate Payee { get; set; } = null!;
            public BeneficiaryData Beneficiary { get; set; } = null!;

            public class ParticipanteDate
            {
                public string Number { get; set; } = null!;
                public string Branch { get; set; } = null!;
                public string Type { get; set; } = null!;
                public HolderData Holder { get; set; } = null!;
                public ParticipantData Participant { get; set; } = null!;
            }

            public class BeneficiaryData
            {
                public string Number { get; set; } = null!;
                public string Branch { get; set; } = null!;
                public string Type { get; set; } = null!;
                public HolderData Holder { get; set; } = null!;
                public ParticipantData Participant { get; set; } = null!;
            }

            public class HolderData
            {
                public string? Name { get; set; }
                public string Document { get; set; } = null!;
            }

            public class ParticipantData
            {
                public string Ispb { get; set; } = null!;
                public string? Name { get; set; }
            }
        }
    }
}