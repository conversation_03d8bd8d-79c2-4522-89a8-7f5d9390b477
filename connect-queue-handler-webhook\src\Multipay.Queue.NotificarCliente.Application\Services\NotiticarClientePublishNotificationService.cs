﻿using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.NotificarCliente.Application.Services
{
    public interface INotiticarClientePublishNotificationService
    {
        Task<bool> EnviarNotificacaoCombrancaConcluida(CobrancaConfirmadaNotificarClienteEvent cobrancaConfirmadaNotificarClienteEvent);

        Task<bool> EnviarNotificacaoSaqueConcluido(SaqueConfirmadoNotificarClienteEvent saqueConfirmadoNotificarClienteEvent);
    }

    internal class NotiticarClientePublishNotificationService : INotiticarClientePublishNotificationService
    {
        public Task<bool> EnviarNotificacaoCombrancaConcluida(CobrancaConfirmadaNotificarClienteEvent cobrancaConfirmadaNotificarClienteEvent)
        {
            return Task.FromResult(true);
        }

        public Task<bool> EnviarNotificacaoSaqueConcluido(SaqueConfirmadoNotificarClienteEvent saqueConfirmadoNotificarClienteEvent)
        {
            return Task.FromResult(true);
        }
    }
}