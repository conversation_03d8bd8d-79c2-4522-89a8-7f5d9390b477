﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Services\BancoDock\**" />
    <EmbeddedResource Remove="Services\BancoDock\**" />
    <None Remove="Services\BancoDock\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
	  <ProjectReference Include="..\Multipay.Infrastructure.Plugin.AnspacePay\Multipay.Infrastructure.Plugin.AnspacePay.csproj" />
	  <ProjectReference Include="..\Multipay.Infrastructure.Plugin.Aarin\Multipay.Infrastructure.Plugin.Aarin.csproj" />
	  <ProjectReference Include="..\Multipay.Infrastructure.Plugin.BS2\Multipay.Infrastructure.Plugin.BS2.csproj" />
	  <ProjectReference Include="..\Multipay.Infrastructure.Plugin.Celcoin\Multipay.Infrastructure.Plugin.Celcoin.csproj" />
	  <ProjectReference Include="..\Multipay.Infrastructure.Plugin.DelBank\Multipay.Infrastructure.Plugin.DelBank.csproj" />
	  <ProjectReference Include="..\Multipay.Infrastructure.Plugin.Genial\Multipay.Infrastructure.Plugin.Genial.csproj" />
	  <ProjectReference Include="..\Multipay.Infrastructure.Plugin.Sicoob\Multipay.Infrastructure.Plugin.Sicoob.csproj" />
    <ProjectReference Include="..\Multipay.Queue.Webhook.Infrastructure.DAPetapoco\Multipay.Queue.Webhook.Infrastructure.DAPetapoco.csproj" />
    <ProjectReference Include="..\Multipay.Queue.Webhook.Infrastructure.RabbitMq\Multipay.Queue.Webhook.Infrastructure.RabbitMq.csproj" />
    <ProjectReference Include="..\Multipay.Queue.Webhook.Infrastructure\Multipay.Queue.Webhook.Infrastructure.csproj" />
  </ItemGroup>

</Project>
