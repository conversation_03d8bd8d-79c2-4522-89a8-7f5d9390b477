﻿using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Enums;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Serilog;
using Serilog.Context;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal abstract class CobrancaRecebidaService<T, TItem>
        where TItem : IPIXData
    {
        #region [ Propriedades ]

        protected readonly ICobrancaService CobrancaService;
        protected readonly ICentralizadorLogsService CentralizadorLogsService;
        protected readonly IUnitOfWork UnitOfWork;
        private readonly IBusNotificarCobrancaConfirmadaCliente busNotificarCliente;
        private readonly IInconsistenciaCobrancaService inconsistenciaCobranca;

        private readonly string instituicaoBancaria;

        protected T? Parameters { get; private set; }
        protected abstract string SerializeParameters { get; }
        protected abstract IEnumerable<TItem> ListPix { get; }

        #endregion [ Propriedades ]

        #region [ Construtores ]

        public CobrancaRecebidaService(
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente,
            IInconsistenciaCobrancaService inconsistenciaCobranca,
            string instituicaoBancaria)
        {
            this.CobrancaService = cobrancaService;
            this.CentralizadorLogsService = centralizadorLogsService;
            this.UnitOfWork = unitOfWork;
            this.busNotificarCliente = busNotificarCliente;
            this.inconsistenciaCobranca = inconsistenciaCobranca;
            this.instituicaoBancaria = instituicaoBancaria;
        }

        #endregion [ Construtores ]

        #region [ Metodos ]

        #region [ Privados ]

        private async Task SalvarPagadorCobranca(string idWebhook, int idCobranca, string codigoCobranca, string cpfPagadorPix, string nomePagadorPix,
            string endToEndId, string? ispbPagador, string? nomeBancoPagador)
        {
            try
            {
                await CobrancaService.AdicionarPagadorCobranca(idCobranca, cpfPagadorPix, nomePagadorPix, endToEndId, ispbPagador, nomeBancoPagador);
                CentralizadorLogsService.AdicionarLogCobranca(idCobranca, "Dados Pagador cobrança salvo com sucesso.", idWebhook, codigoCobranca);

                if (string.IsNullOrWhiteSpace(ispbPagador) || string.IsNullOrWhiteSpace(nomeBancoPagador))
                {
                    CentralizadorLogsService.AdicionarLogCobranca(idCobranca, $"Dados Banco Pagador não informado.", idWebhook, codigoCobranca);
                }
                else
                {
                    CentralizadorLogsService.AdicionarLogCobranca(idCobranca, $"Dados Banco Pagador salvo com sucesso. Ispb: {ispbPagador}, Banco: {nomeBancoPagador}.", idWebhook, codigoCobranca);
                }
            }
            catch (Exception ex)
            {
                CentralizadorLogsService.AdicionarLogCobranca(idCobranca, $"Falha ao salvar dados Pagador cobrança. -> {ex.Message}", idWebhook, codigoCobranca);
            }
        }

        #endregion [ Privados ]

        protected virtual ValueTask<CobrancaModel?> ObterCobranca(TItem dadosPIX)
        {
            var txId = ObterTxId(dadosPIX);

            return CobrancaService.ObterCobranca(txId);
        }

        protected virtual string? ObterEndToEndId(TItem dadosPIX)
        {
            return dadosPIX.EndToEndId;
        }

        protected virtual ValueTask<string> ObterCpfPagador(CobrancaModel cobranca, TItem dadosPIX)
        {
            return ValueTask.FromResult(dadosPIX.CpfPagador);
        }

        protected virtual ValueTask<string> ObterNomePagador(CobrancaModel cobranca, TItem dadosPIX)
        {
            return ValueTask.FromResult(dadosPIX.NomePagador);
        }

        protected virtual string ObterTxId(TItem dadosPIX)
        {
            return dadosPIX.Txid;
        }

        protected virtual decimal ObterValorPago(TItem dadosPIX)
        {
            return Convert.ToDecimal(dadosPIX.Valor.Replace(",", "."), CultureInfo.GetCultureInfo("en"));
        }

        public virtual Task<bool> Processar(string parameters, string idWebhook)
        {
            Parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(parameters);

            return Processar(Parameters, idWebhook);
        }

        public virtual async Task<bool> Processar(T? parameters, string idWebhook)
        {
            if (parameters == null)
                return false;

            this.Parameters = parameters;

            LogContext.PushProperty("ProcessarParameters", this.Parameters, true);
            LogContext.PushProperty("ListPIX", ListPix, true);

            Log.Logger.Information("[{Operacao}] CobrancaRecebidaService.Processar", "CobrancaRecebida");

            try
            {
                foreach (var dadosPIX in ListPix)
                {

                    if (!VerificarSeProcessaRecebimentoPix(dadosPIX))
                    {
                        continue;
                    }

                    var cobranca = await ObterCobranca(dadosPIX);

                    if (cobranca == null || cobranca.TransacaoPix == null)
                    {
                        continue;
                    }

                    var txId = cobranca.TransacaoPix!.TXId;

                    var endToEndId = ObterEndToEndId(dadosPIX);

                    Log.Logger.Information("EndToEndId da cobrança: {E2E}", endToEndId);

                    if (!string.IsNullOrEmpty(endToEndId))
                        await CobrancaService.AtualizarEndToEndPorTxId(txId, endToEndId);

                    // Atualiza o log no Mongo
                    CentralizadorLogsService.AdicionarLogWebhookFornecedorCobranca(idWebhook, cobranca.Id, "CONFIRMAÇÃO PAGAMENTO");
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Webhook recebido e registrado por mensageria", idWebhook, cobranca.Codigo);

                    if (cobranca.DataPagamento.HasValue)
                    {
                        CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Webhook recebido, mas cobranca consta como paga", idWebhook, cobranca.Codigo);
                        continue;
                    }

                    if (cobranca.DataCancelamento.HasValue)
                    {
                        CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Webhook recebido, mas cobranca consta como cancelada", idWebhook, cobranca.Codigo);
                        continue;
                    }

                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Iniciando processamento do webhook via mensageria", idWebhook, cobranca.Codigo);

                    var valorPago = ObterValorPago(dadosPIX);

                    var cpfPagadorPix = await ObterCpfPagador(cobranca, dadosPIX);

                    (string?, string?) dadosBancoPagador = (null, null);

                    if (!string.IsNullOrEmpty(endToEndId))
                    {
                        var nomePagadorPix = await ObterNomePagador(cobranca, dadosPIX);

                        dadosBancoPagador = await ObterDadosBancoPagador(dadosPIX);

                        await SalvarPagadorCobranca(idWebhook, cobranca.Id, cobranca.Codigo, cpfPagadorPix, nomePagadorPix, endToEndId, dadosBancoPagador.Item1, dadosBancoPagador.Item2);

                        var logPagadorCobranca = new
                        {
                            Banco = new
                            {
                                ISPB = dadosBancoPagador.Item1,
                                Nome = dadosBancoPagador.Item2
                            },
                            Pagador = new
                            {
                                CPF = cpfPagadorPix,
                                Nome = nomePagadorPix
                            }
                        };

                        LogContext.PushProperty("PagadorCobranca", logPagadorCobranca, true);

                        Log.Logger.Information("[{Operacao}] Cobrança Recebida. Banco {ISPB} ({Banco}) / Pagador: {CPF} ({Pagador})", "CobrancaRecebida", dadosBancoPagador.Item1, dadosBancoPagador.Item2, cpfPagadorPix, nomePagadorPix);
                    }

                    Task taskEstorno = null!;

                    if (!string.IsNullOrWhiteSpace(cpfPagadorPix))
                    {
                        var documentoWebhook = Regex.Replace(cpfPagadorPix, @"\D", string.Empty);

                        if (cobranca.DocumentoPagador!.Length == 11 && documentoWebhook.Length < 11)
                        {
                            documentoWebhook = documentoWebhook.PadLeft(11, '0');
                        }
                        else if (cobranca.DocumentoPagador!.Length == 14 && documentoWebhook.Length < 14)
                        {
                            documentoWebhook = documentoWebhook.PadLeft(14, '0');
                        }

                        var rs = await UnitOfWork.LimiteSaqueCobrancaRepository.ListarPorOperadorCliente(cobranca.IdCliente);

                        if (rs?.AtivarBloqueioCobrancaDocumentoDiferente == true)
                        {
                            if (!string.IsNullOrEmpty(cobranca.BankIspb) && !string.IsNullOrEmpty(cobranca.BankAccount) && !string.IsNullOrEmpty(cobranca.BankBranch))
                            {
                                if (cobranca.BankIspb != dadosPIX.IspbPagador || cobranca.BankAccount != dadosPIX.ContaBancoPagador || cobranca.BankBranch != dadosPIX.AgenciaBancoPagador)
                                {
                                    taskEstorno = EstornarCobranca(
                                        cobranca,
                                        1,
                                        txId,
                                        instituicaoBancaria,
                                        "Conta diferente da informada na geração do QRCode",
                                        endToEndId!,
                                        cpfPagadorPix,
                                        valorPago,
                                        $"Webhook: Conta diferente da informada na geração do QRCode do pagador ({cpfPagadorPix}). Iniciando o processo de devolução do PIX. Banco: {instituicaoBancaria}"
                                    );

                                    Log.Logger.Warning("[{Operacao}] Cobrança será Estornada (Conta diferente da informada na geração do QRCode). Banco {ISPB} / Pagador: {CPF}", "CobrancaRecebida", dadosBancoPagador.Item1, cpfPagadorPix);
                                }
                            }

                            if (cobranca.DocumentoPagador != documentoWebhook)
                            {
                                taskEstorno = EstornarCobranca(
                                    cobranca,
                                    1,
                                    txId,
                                    instituicaoBancaria,
                                    "Pagador diferente de devedor cobrança",
                                    endToEndId!,
                                    cpfPagadorPix,
                                    valorPago,
                                    $"Webhook: PIX com documento do pagador ({cpfPagadorPix}) diferente do documento do devedor da cobrança ({cobranca.DocumentoPagador}). Iniciando o processo de devolução do PIX. Banco: {instituicaoBancaria}"
                                );

                                Log.Logger.Warning("[{Operacao}] Cobrança será Estornada (Pagamento por Terceiros). Banco {ISPB} / Pagador: {CPF}", "CobrancaRecebida", dadosBancoPagador.Item1, cpfPagadorPix);
                            }
                        }

                        var blacklist = await ValidaDocumentoBlackListDeposito(documentoWebhook, cobranca.IdCliente);

                        if (blacklist)
                        {
                            taskEstorno = EstornarCobranca(
                                cobranca,
                                2,
                                txId,
                                instituicaoBancaria,
                                "Pagador bloqueado em blacklist",
                                endToEndId!,
                                cpfPagadorPix,
                                valorPago,
                                $"Webhook: ({instituicaoBancaria}), PIX com pagador documento ({cpfPagadorPix}) em blacklist. A cobrança será cancelada."
                            );

                            Log.Logger.Warning("[{Operacao}] Cobrança será Estornada (Pagador em Blacklist). Banco {ISPB} / Pagador: {CPF}", "CobrancaRecebida", dadosBancoPagador.Item1, cpfPagadorPix);
                        }
                    }

                    if (dadosBancoPagador.Item1 is not null && UnitOfWork.BlackListRepository.IspbBloqueadoBlackList(dadosBancoPagador.Item1, cobranca.IdCliente))
                    {
                        taskEstorno = EstornarCobranca(
                            cobranca,
                            2,
                            txId,
                            instituicaoBancaria,
                            "Banco pagador bloqueado em blacklist",
                            endToEndId!,
                            cpfPagadorPix,
                            valorPago,
                            $"Webhook: ({instituicaoBancaria}), PIX com banco pagador ISPB {dadosBancoPagador.Item1} ({dadosBancoPagador.Item2}) em blacklist. A cobrança será cancelada."
                        );

                        Log.Logger.Warning("[{Operacao}] Cobrança será Estornada (Banco em Blacklist). Banco {ISPB} / Pagador: {CPF}", "CobrancaRecebida", dadosBancoPagador.Item1, cpfPagadorPix);
                    }

                    if (VerificarValorPagoMenorQueCobranca(cobranca.ValorLiquido, valorPago))
                    {
                        var observacao = $"O valor pago R$ {valorPago} é menor do que o valor da cobrança R$ {cobranca.ValorLiquido}";
                        CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, observacao, idWebhook, cobranca.Codigo);

                        await inconsistenciaCobranca.InserirInconsistenciaAsync(new InconsistenciaCobrancaModel
                        {
                            IdCobranca = cobranca.Id,
                            IdFormaPagamento = (int)FormaPagamentoEnum.PIX,
                            IdTipoInconsistencia = 2,
                            ValorCobranca = cobranca.ValorBruto,
                            ValorCobrado = cobranca.ValorLiquido,
                            ValorPago = valorPago,
                            ComplementoPagamento = false,
                            Parcela = 1,
                            MoedaOrigem = cobranca.MoedaCobranca,
                            Moeda = Moeda.CodigoMoedaBRL,
                            DataPagamento = DateTime.Now,
                            Observacao = observacao
                        });

                        continue;
                    }

                    Log.Logger.Information("Vai confirmar pagamento da cobrança {Codigo}", cobranca.Codigo);

                    var pagamentoConfirmado = await ConfirmarPagamentoBRL(idWebhook, cobranca.Codigo, cobranca.Id, cobranca.IdCliente, cobranca.NumeroFatura, numeroParcelas: 1, valorPago, txId);

                    if (taskEstorno != null)
                    {
                        await taskEstorno;

                        return true;
                    }

                    if (pagamentoConfirmado)
                        busNotificarCliente.PublicarNotificacaoCliente(new Infrastructure.RabbitMq.Events.CobrancaConfirmadaNotificarClienteEvent(txId, valorPago, idWebhook));
                }

                return true;
            }
            catch (Exception ex)
            {
                while (ex.InnerException != null)
                    ex = ex.InnerException;

                Log.Logger.Error(ex, "Erro no processamento de recebimento de cobrança: {Erro}", ex.Message);

                throw;
            }
        }

        protected virtual ValueTask<(string?, string?)> ObterDadosBancoPagador(TItem dadosPIX)
        {
            return ValueTask.FromResult((dadosPIX.IspbPagador, dadosPIX.NomeBancoPagador));
        }

        protected virtual async Task<bool> ValidaDocumentoBlackListDeposito(string documento, int idCliente)
        {
            var lista = await UnitOfWork.BlackListRepository.SelectBlackListCPFCNPJ(documento, null, 1, "", "");

            if (lista != null && lista.Any())
            {
                var bloqueioGeral = lista.Where(x => x.IdCliente == null && x.BloqueioDeposito).FirstOrDefault();

                if (bloqueioGeral != null)
                    return true;

                var bloqueioCliente = lista.Where(x => x.IdCliente == idCliente && x.BloqueioDeposito).FirstOrDefault();

                if (bloqueioCliente != null)
                    return true;
            }

            return false;
        }

        protected virtual bool VerificarValorPagoMenorQueCobranca(decimal valorLiquido, decimal valorPago)
        {
            return valorLiquido > valorPago;
        }

        private async Task<bool> ConfirmarPagamentoBRL(string idWebhook, string codigoCobranca, int idCobranca, int idCliente, string numeroFatura, int numeroParcelas, decimal valorPago, string txIdPIX)
        {
            var taxaTarifa = await UnitOfWork.TaxaClienteOperacaoRepository.ObterTaxaPorClienteOperacaoPorCodigo(idCliente, "RECPIX");

            if (taxaTarifa == null)
            {
                CentralizadorLogsService.AdicionarLogCobranca(idCobranca, "Taxa tarifa não localizada para cliente.", idWebhook, codigoCobranca);
                return false;
            }

            await CobrancaService.ConfirmarPagamentoBRL(
                idCobranca,
                idCliente,
                (byte)FormaPagamentoEnum.PIX,
                valorPago,
                numeroParcelas,
                //"Pagamento fatura: " + numeroFatura,
                "RECEBIMENTO DEPÓSITO: " + codigoCobranca,
                valorPago,
                taxaTarifa.ObterPorcentagemTaxa(numeroParcelas),
                taxaTarifa.ObterValorTarifa(numeroParcelas),
                taxaTarifa.HorasResgate,
                txIdPIX);

            return true;
        }

        protected virtual bool VerificarSeProcessaRecebimentoPix(TItem dadosPIX)
        {
            return true;
        }

        private async Task EstornarCobranca(CobrancaModel cobranca, int tipoEstorno, string txId, string instituicaoBancaria, string motivo, string endToEndId, string pagadorPix, decimal valorPago, string mensagemLog)
        {
            CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, mensagemLog);

            var resultEstorno = await ProcessarDevolucaoBancoAsync(cobranca);

            switch (resultEstorno)
            {
                case ResultEstornoBanco.Sucesso:
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, $"Devolução realizada em {instituicaoBancaria} com sucesso");
                    break;

                case ResultEstornoBanco.Falha:
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, $"Falha ao realizar devolução em {instituicaoBancaria}. Favor prosseguir com o estorno manual");
                    break;

                case ResultEstornoBanco.NaoImplementado:
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, $"Rotina de devolução em {instituicaoBancaria} não foi implementado. Favor prosseguir com o estorno manual");
                    break;
            }
            
            //await UnitOfWork.CobrancaRepository.InsertCobrancaEstorno(cobranca.Id, DateTime.Now, tipoEstorno, motivo, endToEndId, pagadorPix, valorPago);

            //await UnitOfWork.CobrancaRepository.CancelarCobranca(cobranca.Id);
        }

        protected virtual ValueTask<ResultEstornoBanco> ProcessarDevolucaoBancoAsync(CobrancaModel cobranca) => ValueTask.FromResult(ResultEstornoBanco.NaoImplementado);

        #endregion [ Metodos ]

        protected enum ResultEstornoBanco
        {
            Sucesso,
            Falha,
            NaoImplementado
        }
    }

    internal abstract class CobrancaRecebidaService<T> : CobrancaRecebidaService<T, T> where T : IPIXData
    {
        protected CobrancaRecebidaService(
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            string instituicaoBancaria) : base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, instituicaoBancaria)
        {
        }
    }
}