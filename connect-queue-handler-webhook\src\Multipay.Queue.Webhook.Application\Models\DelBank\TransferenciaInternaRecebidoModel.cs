﻿using Multipay.Queue.Webhook.Application.Services.ContaGrafica;

namespace Multipay.Queue.Webhook.Application.Models.DelBank
{
    public class TransferenciaInternaRecebidoModel : IMovimentacaoSaidaModel
    {
        public string Id { get; set; } = null!;
        public string NSU { get; set; } = null!;
        public decimal Amount { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Type { get; set; } = null!;
        public TransactiontypeData TransactionType { get; set; } = null!;
        public string Modality { get; set; } = null!;
        public BalanceData Balance { get; set; } = null!;
        public ProofData Proof { get; set; } = null!;

        public string? CodigoSolicitacaoSaque => Proof.ExternalId;

        public string CodigoAutenticacao => Proof.Id;

        public string ObterOperacao() => "ENVIOTRANSF";

        public class TransactiontypeData
        {
            public string Name { get; set; } = null!;
            public string Description { get; set; } = null!;
            public bool IsCredit { get; set; }
        }

        public class BalanceData
        {
            public decimal balancePrevious { get; set; }
            public decimal currentBalance { get; set; }
        }

        public class ProofData
        {
            public string Id { get; set; } = null!;
            public string ExternalId { get; set; } = null!;
            public string Status { get; set; } = null!;
            public string Type { get; set; } = null!;
            public decimal Amount { get; set; }
            public ParticipanteData Payer { get; set; } = null!;
            public ParticipanteData Beneficiary { get; set; } = null!;

            public class ParticipanteData
            {
                public string Number { get; set; } = null!;
                public string Branch { get; set; } = null!;
                public string Type { get; set; } = null!;
                public HolderData Holder { get; set; } = null!;
                public ParticipantData Participant { get; set; } = null!;

                public class HolderData
                {
                    public string Document { get; set; } = null!;
                    public string Name { get; set; } = null!;
                    public string Type { get; set; } = null!;
                }

                public class ParticipantData
                {
                    public string Name { get; set; } = null!;
                    public string Ispb { get; set; } = null!;
                }
            }
        }
    }
}