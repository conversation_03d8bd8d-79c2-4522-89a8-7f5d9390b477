name: Deploy Background Worker

on:
  push:
    branches:
      - release
      - main

jobs:

  build:
    name: Build image
    runs-on: ubuntu-latest
    steps:

    - name: Declare variables
      id: vars
      shell: bash
      run: |
        echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
        echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Check vars
      run: |
        echo "Branch: ${{ steps.vars.outputs.branch }}"
        echo "Sha: ${{ steps.vars.outputs.sha_short }}"

    - name: Check out code
      uses: actions/checkout@v2

    - name: Configure AWS credentials HML
      uses: aws-actions/configure-aws-credentials@v1
      if: github.ref == 'refs/heads/release'
      with:
        aws-access-key-id: ${{ secrets.ACCESS_KEY_HML }}
        aws-secret-access-key: ${{ secrets.SECRET_KEY_HML }}
        aws-region: us-east-1

    - name: Configure AWS credentials PRD
      uses: aws-actions/configure-aws-credentials@v1
      if: github.ref == 'refs/heads/main'
      with:
        aws-access-key-id: ${{ secrets.ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.SECRET_KEY }}
        aws-region: us-east-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build docker, tag, and push image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: connect-background-worker
        IMAGE_TAG: ${{ steps.vars.outputs.branch }}-${{ github.sha }}
        NUGET_URL: ${{ secrets.NUGET_URL }}
        GPR_CONNECT_READ_TOKEN: ${{ secrets.GPR_CONNECT_READ_TOKEN }}
        GPR_CONNECT_READ_USER: ${{ secrets.GPR_CONNECT_READ_USER }}

      run: |
        docker build --build-arg NUGET_URL=$NUGET_URL --build-arg GPR_CONNECT_READ_USER=$GPR_CONNECT_READ_USER --build-arg GPR_CONNECT_READ_TOKEN=$GPR_CONNECT_READ_TOKEN -t $ECR_REGISTRY/$ECR_REPOSITORY:build-$IMAGE_TAG -f src/Worker/Dockerfile .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:build-$IMAGE_TAG

  deploy:

    name: Deploy manifest
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.ref_name }}
    needs: ['build']
    if: ${{ success() || contains(needs.*.result, 'success') }}
    steps:

    - name: Declare variables
      id: vars
      shell: bash
      run: |
        echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
        echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Check vars
      run: |
        echo "Branch: ${{ steps.vars.outputs.branch }}"
        echo "Sha: ${{ steps.vars.outputs.sha_short }}"

    - name: Checkout configs
      uses: MrSquaare/ssh-setup-action@v1
      with:
          host: github.com
          private-key: ${{ secrets.PRIVATE_KEY }}

    - name: Clone repository
      run: |
        <NAME_EMAIL>:ConnectPSP/connect-gitops.git
        git config --global user.email "<EMAIL>"
        git config --global user.name "Pipeline"

    - name: Update image
      run: |
        cd connect-gitops/apps/connect-background-worker/
        echo ${{ steps.vars.outputs.branch }}-${{ github.sha }}
        sed -i -e 's/build[0-9]*\-.*/'build-${{ steps.vars.outputs.branch }}-${{ github.sha }}'/g' values-${{ steps.vars.outputs.branch == 'release' && 'hml' || steps.vars.outputs.branch }}.yaml

    - name: Push to repository
      run: |
        cd connect-gitops/apps/connect-background-worker/
        git add .
        git commit -m "Update image for tag - connect-background-worker-${{ steps.vars.outputs.branch }}-${{ github.sha }}  values-${{ steps.vars.outputs.branch == 'release' && 'hml' || steps.vars.outputs.branch }}.yaml "
        echo ${{ steps.vars.outputs.branch }}-${{ github.sha }}
        git push
