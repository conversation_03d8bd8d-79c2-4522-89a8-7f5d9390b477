{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-handler-webhook\\src\\Multipay.Queue.Webhook.Core\\Multipay.Queue.Webhook.Core.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-handler-webhook\\src\\Multipay.Queue.Webhook.Core\\Multipay.Queue.Webhook.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-handler-webhook\\src\\Multipay.Queue.Webhook.Core\\Multipay.Queue.Webhook.Core.csproj", "projectName": "Multipay.Queue.Webhook.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-handler-webhook\\src\\Multipay.Queue.Webhook.Core\\Multipay.Queue.Webhook.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-handler-webhook\\src\\Multipay.Queue.Webhook.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Datadog.Trace": {"target": "Package", "version": "[2.53.2, )"}, "Datadog.Trace.Bundle": {"target": "Package", "version": "[2.53.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.1, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Exceptions": {"target": "Package", "version": "[8.4.0, )"}, "Serilog.Exceptions.SqlServer": {"target": "Package", "version": "[8.4.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.1, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.Datadog.Logs": {"target": "Package", "version": "[0.5.2, )"}, "Serilog.Sinks.Debug": {"target": "Package", "version": "[3.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.Map": {"target": "Package", "version": "[1.0.2, )"}, "SerilogAnalyzer": {"target": "Package", "version": "[0.15.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}