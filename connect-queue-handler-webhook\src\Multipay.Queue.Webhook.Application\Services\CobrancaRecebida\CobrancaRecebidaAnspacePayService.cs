﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.AnspacePay.Services;
using Multipay.Queue.Webhook.Application.Models.AnspacePay.Indirect;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaAnspacePayService : CobrancaRecebidaService<CobrancaRecebidaAnspacePayModel, CobrancaRecebidaAnspacePayModel>
    {
        private readonly ILogger<CobrancaRecebidaAnspacePayService> logger;
        private readonly IEstornarQrCodeService estornarService;

        public CobrancaRecebidaAnspacePayService(
            ILogger<CobrancaRecebidaAnspacePayService> logger,
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente,
            IEstornarQrCodeService estornarService) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.AnspacePay)
        {
            this.logger = logger;
            this.estornarService = estornarService;
        }

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);

        protected override IEnumerable<CobrancaRecebidaAnspacePayModel> ListPix => new List<CobrancaRecebidaAnspacePayModel>()
        {
            Parameters!
        };

        protected override async ValueTask<ResultEstornoBanco> ProcessarDevolucaoBancoAsync(CobrancaModel cobranca)
        {
            try
            {
                var valorPago = ObterValorPago(Parameters!);
                estornarService.SetIdContaBancariaEmpresa(cobranca.TransacaoPix!.IdContaBancariaEmpresa!.Value);
                var rs = await estornarService.SolicitarDevolucao(Parameters!.EndToEndId, valorPago);

                if (!rs.Item1)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Falha na devolucao -> " + rs.Item2);
                    return ResultEstornoBanco.Falha;
                }
                var txId = cobranca.TransacaoPix!.TXId;
                await CobrancaService.AtualizarEndToEndPorTxId(txId, rs.Item2);
                return ResultEstornoBanco.Sucesso;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Falha ao processar devolucao de cobranca: {id}", cobranca.Id);
                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, ex.ToString());
                return ResultEstornoBanco.Falha;
            }
        }
    }
}