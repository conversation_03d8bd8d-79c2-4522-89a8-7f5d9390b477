﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Settings;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Bus.Interfaces;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Bus
{
    public interface IBusConsumerCobrancaConfirmadaNotificarCliente : IBusConsumer<CobrancaConfirmadaNotificarClienteEvent>
    { }

    internal class BusConsumerCobrancaConfirmadaNotificarCliente : EventBusConsumerRabbitMQ<CobrancaConfirmadaNotificarClienteEvent>, IBusConsumerCobrancaConfirmadaNotificarCliente
    {
        public BusConsumerCobrancaConfirmadaNotificarCliente(
            IRabbitMQPersistentConnection persistentConnection,
            IOptions<RabbitMqSettings> options,
            ILogger<BusConsumerCobrancaConfirmadaNotificarCliente> logger)
            : base(persistentConnection, logger, options.Value.NotificarCobrancaCliente)
        { }
    }
}