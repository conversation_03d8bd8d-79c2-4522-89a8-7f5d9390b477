﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Datadog.Trace" Version="2.51.0" />
		<PackageReference Include="Datadog.Trace.Bundle" Version="2.51.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
		<PackageReference Include="Serilog" Version="4.0.0" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
		<PackageReference Include="Serilog.Exceptions.SqlServer" Version="8.4.0" />
		<PackageReference Include="Serilog.Sinks.Datadog.Logs" Version="0.5.2" />
		<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
		<PackageReference Include="Serilog.Sinks.Map" Version="1.0.2" />
	</ItemGroup>

</Project>
