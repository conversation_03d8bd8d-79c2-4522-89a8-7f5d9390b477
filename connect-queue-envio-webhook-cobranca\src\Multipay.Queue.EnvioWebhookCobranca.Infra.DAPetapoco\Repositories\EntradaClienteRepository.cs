﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Core.Constants;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    public interface IEntradaClienteRepository
    {
        Task<int> InserirRecebimentoOperacaoBancaria(int idRecebimento, byte idOperacaoServico, int idCliente, string descricao, decimal valor, TaxaClienteOperacaoModel taxaRecebimento);
    }

    internal class EntradaClienteRepository : IEntradaClienteRepository
    {
        private readonly ILogger logger;
        private readonly IDatabase database;

        public EntradaClienteRepository(
            ILogger logger,
            IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public async Task<int> InserirRecebimentoOperacaoBancaria(
            int idRecebimentoOperacaoBancaria,
            byte idOperacaoServico,
            int idCliente,
            string descricao,
            decimal valor,
            TaxaClienteOperacaoModel taxaRecebimento)
        {
            try
            {
                using var reader = await database.QueryProcAsync<int>("Arquitetura.Insert_EntradaClienteDiretoExtratoContabil", new
                {
                    idCliente,
                    descricao,
                    valor,
                    idMoeda = Moeda.IdMoedaBRL,
                    porcentagemTaxa = taxaRecebimento.TaxaAVista,
                    valortarifa = taxaRecebimento?.TarifaAVista,
                    valorAntecipado = 0.00M,
                    horasResgate = 0
                });

                if (await reader.ReadAsync() && reader.Poco > 0)
                {
                    await database.ExecuteNonQueryProcAsync("Painel.Insert_RecebimentoOperacaoBancariaExtratoContabilCliente", new
                    {
                        idRecebimentoOperacaoBancaria,
                        idCliente,
                        idExtratoContabilCliente = reader.Poco
                    });
                    return reader.Poco;
                }
                return 0;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(EntradaClienteRepository), database.LastCommand);
                throw;
            }
        }
    }
}