﻿namespace Multipay.Queue.Webhook.Core.Models
{
    public class CobrancaModel
    {
        private string? documentoPagador;

        public int IdCliente { get; private set; }
        public int Id { get; private set; }
        public string Codigo { get; private set; } = string.Empty;
        public DateTime? DataPagamento { get; private set; }
        public DateTime? DataCancelamento { get; private set; }
        public string NumeroFatura { get; private set; } = string.Empty;
        public QRCodePIXModel? TransacaoPix { get; private set; }
        public string UrlConfirmacao { get; private set; } = string.Empty;
        public decimal ValorBruto { get; private set; }
        public decimal ValorLiquido { get; private set; }
        public string? RequestIdWebhook { get; private set; }
        public Guid Guid { get; set; }
        public string? FormaPagamento { get; set; }
        public byte Parcelas { get; set; }
        public decimal? ValorPago { get; set; }
        public string MoedaOrigem { get; set; } = string.Empty;
        public string MoedaCobranca { get; set; } = string.Empty;
        public string? MoedaPagamento { get; set; }

        public string? BankIspb { get; set; }
        public string? BankBranch { get; set; }
        public string? BankAccount { get; set; }
        
        public string? DocumentoPagador { get => documentoPagador ?? CPFCNPJ; set => documentoPagador = CPFCNPJ = value; }
        public string? CPFCNPJ { get; set; }

        public void SetarTransacao(QRCodePIXModel transacaoPix)
        {
            TransacaoPix = transacaoPix;
        }
    }
}