﻿namespace Multipay.Queue.Webhook.Core.Entities;
public class QrCodePixEntity
{
    public int Id { get; set; }
    public int InvoiceId { get; set; }
    public byte BankId { get; set; }
    public int CompanyBankAccountId { get; set; }
    public string? TxId { get; set; }
    public decimal NetValue { get; set; }
    public decimal? PaidValue { get; set; }
    public string? QrCode { get; set; }
    public DateTime RegisterDate { get; set; }
    public DateTime? PaymentDate { get; set; }
    public DateTime? ExpirationDate { get; set; }
    public string? EndToEndPayment { get; set; }
}
