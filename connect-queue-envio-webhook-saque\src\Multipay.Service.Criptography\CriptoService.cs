﻿using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

namespace Multipay.Service.Criptography
{
    public class CriptoService
    {
        private string _key = null;
        private string _im = null;

        private AmazonSecretsManagerClient _secretsManager;

        public static string Theme { get; set; }

        private async Task CheckSecrets(CancellationToken cancellationToken)
        {
            if (!string.IsNullOrEmpty(_key))
                return;

            var regionEndpoint = Amazon.RegionEndpoint.USEast1;

            if ("9f".Equals(Theme, StringComparison.InvariantCultureIgnoreCase))
                regionEndpoint = Amazon.RegionEndpoint.SAEast1;

            _secretsManager = new AmazonSecretsManagerClient(regionEndpoint);

            var request = new GetSecretValueRequest { SecretId = "Key-master-secret" };
            var response = await _secretsManager.GetSecretValueAsync(request, cancellationToken);

            _key = response.SecretString.Split('|')[0];
            _im = response.SecretString.Split('|')[1];
        }

        public async Task<string> Encrypt(string plainText, CancellationToken cancellationToken = default)
        {
            await CheckSecrets(cancellationToken);

            using (var aes = Aes.Create())
            {
                aes.Key = Convert.FromBase64String(_key);
                aes.IV = Convert.FromBase64String(_im);

                ICryptoTransform cryptoTransform = aes.CreateEncryptor();

                byte[] encriptedData;

                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, cryptoTransform, CryptoStreamMode.Write))
                    {
                        using (var sw = new StreamWriter(cs))
                        {
                            sw.Write(plainText);
                        }
                    }

                    encriptedData = ms.ToArray();
                }

                return Convert.ToBase64String(encriptedData);
            }
        }

        public async Task<string> Decrypt(string encriptedText, CancellationToken cancellationToken = default)
        {
            await CheckSecrets(cancellationToken);

            using (var aes = Aes.Create())
            {
                aes.Key = Convert.FromBase64String(_key);
                aes.IV = Convert.FromBase64String(_im);

                ICryptoTransform cryptoTransform = aes.CreateDecryptor();

                string decriptedData;
                byte[] encryptedData = Convert.FromBase64String(encriptedText);

                using (var ms = new MemoryStream(encryptedData))
                {
                    using (var cs = new CryptoStream(ms, cryptoTransform, CryptoStreamMode.Read))
                    {
                        using (var sr = new StreamReader(cs))
                        {
                            decriptedData = sr.ReadToEnd();
                        }
                    }
                }

                return decriptedData;
            }
        }

        public async Task<string> GetSecret(string secretId, CancellationToken cancellationToken = default)
        {
            var request = new GetSecretValueRequest { SecretId = secretId };

            var response = await _secretsManager.GetSecretValueAsync(request, cancellationToken);

            return response.SecretString;
        }
    }
}