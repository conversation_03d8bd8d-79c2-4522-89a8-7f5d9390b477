﻿using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels
{
    [BsonIgnoreExtraElements]
    public class LogCobrancaModel : BaseModel
    {
        public const string CollectionName = nameof(LogCobrancaModel);

        public LogCobrancaModel(string codigoCobranca, string descricao, string ip, string json, bool exclusivo = false)
        {
            IdCobranca = 0;
            CodigoCobranca = codigoCobranca;
            Descricao = descricao;
            IP = ip;
            Json = json;
            Exclusivo = exclusivo;
            DataCriacaoUtc = DateTime.UtcNow;
        }

        public LogCobrancaModel(int idCobranca, string descricao, string ip, string json, bool exclusivo = false)
        {
            IdCobranca = idCobranca;
            Descricao = descricao;
            IP = ip;
            Json = json;
            Exclusivo = exclusivo;
            DataCriacaoUtc = DateTime.UtcNow;
        }

        public int IdCobranca { get; private set; }
        public string? CodigoCobranca { get; set; }
        public string Descricao { get; private set; }
        public string IP { get; private set; }
        public string Json { get; private set; }
        public bool Exclusivo { get; private set; }
        public string? IdWebhook { get; set; }
        public DateTime DataCriacaoUtc { get; private set; }
        public DateTime? DataAtualizacaoUtc { get; private set; }
    }
}