﻿namespace Multipay.Queue.Webhook.Core.Models
{
    public class SelectBlackListModel
    {
        public int Id { get; set; }
        public int? IdCliente { get; set; }
        public string Cliente { get; set; }
        public string CPFCNPJBloqueado { get; set; }
        public string ChavePix { get; set; }
        public bool BloqueioSaque { get; set; }
        public string BloqueioSaqueStatus
        { get { return (BloqueioSaque ? "Sim" : "Não"); } }
        public bool BloqueioDeposito { get; set; }
        public string BloqueioDepositoStatus
        { get { return (BloqueioDeposito ? "Sim" : "Não"); } }
        public bool Ativo { get; set; }
        public string AtivoStatus
        { get { return (Ativo ? "Bloqueado" : "Desbloqueado"); } }

        public string Justificativa { get; set; }
        public string DataBloqueio { get; set; }
        public string DataDesbloqueio { get; set; }

        public string NumeroBanco { get; set; }
        public string NomeReduzidoBanco { get; set; }
        public string NomeBanco { get; set; }
        public string ISPB { get; set; }
        public byte IdBanco { get; set; }
    }
}