﻿namespace Multipay.Queue.Webhook.Core.Entities;
public class WebhookProviderEntity
{
    public long Id { get; set; }
    public string? Provider {  get; set; }
    public string? Type { get; set; }
    public string? BodyJSON { get; set; }
    public string? OriginIp { get; set; }
    public DateTime Date { get; set; }

    public WebhookProviderEntity(string? provider, string? type, string? bodyJSON, string? originIp, DateTime date)
    {
        Provider = provider;
        Type = type;
        BodyJSON = bodyJSON;
        OriginIp = originIp;
        Date = date;
    }
}
