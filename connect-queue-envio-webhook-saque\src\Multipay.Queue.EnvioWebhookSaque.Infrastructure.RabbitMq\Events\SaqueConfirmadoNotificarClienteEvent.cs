﻿using Multipay.RabbitMQExtension.Events;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events
{
    public class SaqueConfirmadoNotificarClienteEvent : IntegrationEvent
    {
        public SaqueConfirmadoNotificarClienteEvent()
        { }

        public SaqueConfirmadoNotificarClienteEvent(bool? sucesso, int idSolicitacaoSaque, string? idWebhook, string? status, string? statusCode)
        {
            Sucesso = sucesso;
            IdSolicitacaoSaque = idSolicitacaoSaque;
            IdWebhook = idWebhook;
            Status = status;
            StatusCode = statusCode;
        }

        public bool? Sucesso { get; set; }
        public int IdSolicitacaoSaque { get; set; }
        public string? IdWebhook { get; set; }
        public string? Status { get; set; }
        public string? StatusCode { get; set; }
    }
}