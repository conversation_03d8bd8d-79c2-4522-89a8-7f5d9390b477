﻿using Multipay.Infrastructure.Plugin.AnspacePay.Services;
using Multipay.Queue.Webhook.Application.Models.AnspacePay;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services.AnspacePay
{
    internal class CobrancaRecebidaService : CobrancaRecebidaService<CobrancaRecebidaModel, CobrancaRecebidaModel>
    {
        private readonly IEstornarQrCodeService _estornarService;

        public CobrancaRecebidaService(
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente,
            IEstornarQrCodeService estornarService) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.AnspacePay)
        {
            _estornarService = estornarService;
        }

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);

        protected override IEnumerable<CobrancaRecebidaModel> ListPix => new List<CobrancaRecebidaModel>()
        {
            Parameters!
        };

        protected override async ValueTask<ResultEstornoBanco> ProcessarDevolucaoBancoAsync(CobrancaModel cobranca)
        {
            try
            {
                var valorPago = ObterValorPago(Parameters!);
                _estornarService.SetIdContaBancariaEmpresa(cobranca.TransacaoPix!.IdContaBancariaEmpresa!.Value);
                
                var rs = await _estornarService.SolicitarDevolucao(Parameters!.EndToEndId, valorPago);

                if (!rs.Item1)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Falha na devolucao -> " + rs.Item2);
                    return ResultEstornoBanco.Falha;
                }
                var txId = cobranca.TransacaoPix!.TXId;
                await CobrancaService.AtualizarEndToEndPorTxId(txId, rs.Item2);
                return ResultEstornoBanco.Sucesso;
            }
            catch (Exception ex)
            {
                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, ex.ToString());
                return ResultEstornoBanco.Falha;
            }
        }
    }
}