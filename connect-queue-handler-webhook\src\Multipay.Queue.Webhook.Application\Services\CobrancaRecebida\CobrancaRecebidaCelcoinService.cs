﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Celcoin.Abstractions;
using Multipay.Queue.Webhook.Application.Models;
using Multipay.Queue.Webhook.Application.Models.BancoDock;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaCelcoinService : CobrancaRecebidaService<CobrancaRecebidaCelcoinModel, CobrancaRecebidaCelcoinModel>
    {
        private readonly ILogger<CobrancaRecebidaCelcoinService> logger;
        private readonly IBrasilApiService brasilApiService;
        private readonly IEstornarQrCodeService estornarService;

        public CobrancaRecebidaCelcoinService(
            ILogger<CobrancaRecebidaCelcoinService> logger,
            IBrasilApiService brasilApiService,
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IEstornarQrCodeService estornarService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.Celcoin)
        {
            this.logger = logger;
            this.brasilApiService = brasilApiService;
            this.estornarService = estornarService;
        }

        protected override IEnumerable<CobrancaRecebidaCelcoinModel> ListPix => new List<CobrancaRecebidaCelcoinModel> { Parameters! };

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);

        protected override async ValueTask<ResultEstornoBanco> ProcessarDevolucaoBancoAsync(CobrancaModel cobranca)
        {
            try
            {
                var valorPago = ObterValorPago(Parameters!);

                var contaBancariaEmpresa = await UnitOfWork.ContaBancariaRepository.ObterContaBancariaEmpresa(cobranca.TransacaoPix!.IdContaBancariaEmpresa!.Value);

                if (contaBancariaEmpresa is null)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Não foi possível obter ContaBancariaEmpresa");
                    return ResultEstornoBanco.Falha;
                }

                var endToEndId = ObterEndToEndId(Parameters!)!;
                var rs = await estornarService.SolicitarDevolucao(contaBancariaEmpresa, endToEndId, valorPago, CancellationToken.None);

                if (!rs.Item1)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Falha na devolucao -> " + rs.Item2);
                    return ResultEstornoBanco.Falha;
                }
                var txId = cobranca.TransacaoPix!.TXId;

                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Registrando novo endtoend da cobrança estornada");

                // Parse do JSON
                JObject jsonObj = JObject.Parse(rs.Item2);

                // Acessando o valor (caminho completo para o campo)
                string returnIdentification = jsonObj["body"]?["returnIdentification"]?.ToString();

                await CobrancaService.AtualizarEndToEndPorTxId(txId, returnIdentification);//novo endtoend
                return ResultEstornoBanco.Sucesso;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Falha ao processar devolucao de cobranca: {id}", cobranca.Id);
                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, ex.ToString());
                return ResultEstornoBanco.Falha;
            }
        }

        protected override async ValueTask<(string?, string?)> ObterDadosBancoPagador(CobrancaRecebidaCelcoinModel dadosPIX)
        {
            var ispbPagador = dadosPIX.IspbPagador;
            string? nomeBanco = null;

            // Consulta o nome do banco usando o ISPB através do BrasilApiService
            if (!string.IsNullOrEmpty(ispbPagador))
            {
                var bancoParticipantesPix = await brasilApiService.ConsultarPorIspb(ispbPagador);
                if (bancoParticipantesPix != null)
                    nomeBanco = bancoParticipantesPix.Nome;
            }

            return (ispbPagador, nomeBanco);
        }
    }
}