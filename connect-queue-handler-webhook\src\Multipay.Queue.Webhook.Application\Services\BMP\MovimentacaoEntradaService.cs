﻿using Microsoft.Extensions.Configuration;
using Multipay.Queue.Webhook.Application.Models.BMP;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Emails;

namespace Multipay.Queue.Webhook.Application.Services.BMP
{
    public interface IMovimentacaoEntradaService
    {
        Task<bool> ProcessarDevolucaoPIX(CallbackRecebidoModel parameters);

        Task<bool> ProcessarDevolucaoTED(CallbackRecebidoModel parameters);

        Task<bool> ProcessarRecebimentoPIX(CallbackRecebidoModel parameters, string idWebhook);

        Task<bool> ProcessarRecebimentoTED(CallbackRecebidoModel parameters);
    }

    internal class MovimentacaoEntradaService : IMovimentacaoEntradaService
    {
        private readonly CobrancaRecebidaBMPService cobrancaRecebidaBMPService;
        private readonly IUnitOfWork unitOfWork;
        private readonly IEmailHandler _emailHandler;
        private readonly int idBancoDI;

        public MovimentacaoEntradaService(
            CobrancaRecebidaBMPService cobrancaRecebidaBMPService,
            IUnitOfWork unitOfWork,
            IConfiguration configuration,
            IEmailHandler emailHandler)
        {
            this.cobrancaRecebidaBMPService = cobrancaRecebidaBMPService;
            this.unitOfWork = unitOfWork;
            this._emailHandler = emailHandler;
            this.idBancoDI = configuration["BancoInfinity:IdBanco"].ToInt() ?? 0;
        }

        public async Task<bool> ProcessarDevolucaoPIX(CallbackRecebidoModel parameters)
        {
            return await ProcessarCreditoContaCliente(parameters, TipoOperacaoBancaria.PIX, OperacaoServico.RecebimentoPIX, "ESTORNO PIX");
        }

        public async Task<bool> ProcessarDevolucaoTED(CallbackRecebidoModel parameters)
        {
            return await ProcessarCreditoContaCliente(parameters, TipoOperacaoBancaria.TED, OperacaoServico.RecebimentoTED, "DEVOLUCAO TED");
        }

        public async Task<bool> ProcessarRecebimentoPIX(CallbackRecebidoModel parameters, string idWebhook)
        {
            var result = await ProcessarCreditoContaCliente(parameters, TipoOperacaoBancaria.PIX, OperacaoServico.RecebimentoPIX, "RECEBE PIX");

            if (!result || string.IsNullOrEmpty(parameters.Detalhes?.IdentificadorTransacao))
            {
                return result;
            }
            return await cobrancaRecebidaBMPService.Processar(parameters, idWebhook);
        }

        public async Task<bool> ProcessarRecebimentoTED(CallbackRecebidoModel parameters)
        {
            return await ProcessarCreditoContaCliente(parameters, TipoOperacaoBancaria.TED, OperacaoServico.RecebimentoTED, "RECEBE TED");
        }

        private async Task<bool> ProcessarCreditoContaCliente(CallbackRecebidoModel parameters, byte tipoOperacaoBancaria, byte operacaoServico, string descServico)
        {
            if (string.IsNullOrWhiteSpace(parameters.Detalhes?.CodigoMovimento))
                return false;

            var verificarRecebimentoPIX = await unitOfWork.RecebimentoOperacaoBancariaRepository.VerificarCodigoMovimentoExiste(parameters.Detalhes.CodigoMovimento);
            if (verificarRecebimentoPIX)
                return true;

            string body = string.Format("<br/><br />Agência: {0}<br/>Conta:{1}<br/>Chave:{2}<br /><br />JSON:{3}", parameters.Detalhes.RecebedorConta?.Agencia, string.Format("{0}-{1}", parameters.Detalhes.RecebedorConta?.Conta, parameters.Detalhes.RecebedorConta?.ContaDigito), parameters.Detalhes.RecebedorChave, parameters.ToJson());
            var dadosConta = await unitOfWork.ContaBancariaRepository.ObterIdContaIdClientePorDadosConta(
                             idBancoDI,
                            parameters.ObterAgenciaRecebedor(),
                            parameters.ObterContaRecebedor(),
                            parameters.ObterDigitoContaRecebedor());
            if (dadosConta == null)
            {
                await EnviarEmailSuporteOperacional($"CallBack BMP - {descServico} - Recebedor não encontrado",
                    $"Não foi encontrado um recebedor com os dados recebedidos!{body}");
                return false;
            }
            if (parameters.Detalhes?.VlrTransacao <= 0.00D)
            {
                await EnviarEmailSuporteOperacional($"CallBack BMP - {descServico} - Valor inválido",
                    $"O valor informado como recebido é inválido!{body}");
            }

            int idRecebimentoPIX = await InserirRecebimentoOperacaoBancaria(parameters, tipoOperacaoBancaria, dadosConta!.IdCliente, dadosConta!.Id);

            if (idRecebimentoPIX <= 0)
                return false;

            // Inserindo o credito em caso de deposito direto para a chave
            if (string.IsNullOrEmpty(parameters.Detalhes?.IdentificadorTransacao))
            {
                var taxaRecebimento = await unitOfWork.TaxaClienteOperacaoRepository.ObterTaxaPorClienteOperacao(dadosConta!.IdCliente, operacaoServico);
                if (taxaRecebimento == null)
                    return false;

                int idEntradaCliente = await unitOfWork.EntradaClienteRepository.InserirRecebimentoOperacaoBancaria(idRecebimentoPIX, operacaoServico, dadosConta!.IdCliente, descServico, Convert.ToDecimal(parameters.Detalhes?.VlrTransacao), taxaRecebimento);
                if (idEntradaCliente <= 0)
                    await EnviarEmailSuporteOperacional($"CallBack BMP - {descServico} - Crédito não efetuado",
                        $"Ocorreu um erro ao efetuar o crédito ao cliente!{body}");
            }

            return true;
        }

        private async Task<int> InserirRecebimentoOperacaoBancaria(CallbackRecebidoModel parameters, byte idTipoOperacaoBancaria, int? idCliente, int? idContaBancaria)
        {
            return await unitOfWork.RecebimentoOperacaoBancariaRepository.Inserir(
                            idTipoOperacaoBancaria,
                            parameters.Detalhes!.Identificador,
                            nomePagador: parameters.Detalhes.PagadorNome,
                            documentoPagador: parameters.Detalhes.PagadorDocumentoFederal,
                            ISPBPagador: parameters.Detalhes.PagadorParticipante,
                            chaveRecebedor: parameters.Detalhes.RecebedorChave,
                            parameters.Detalhes.CodigoMovimento,
                            parameters.Detalhes.CodigoMovimentoOrigem,
                            parameters.Detalhes.CodigoOperacaoCliente,
                            dataCadastro: DateTime.UtcNow.AddHours(-3),
                            dataTransacao: parameters.Detalhes.DtTransacao,
                            parameters.Detalhes.DescricaoCliente,
                            idTipoMovimentoContabil: parameters.Detalhes.TipoLancto == "C" ? TipoMovimentoContabil.Credito : TipoMovimentoContabil.Debito,
                            parameters.Detalhes.InformacoesAdicionais,
                            parameters.Detalhes.MotivoEstorno,
                            parameters.Detalhes.NSU,
                            valorTransacao: parameters.Detalhes.VlrTransacao ?? 0.00D,
                            idCliente,
                            idContaBancaria
                            );
        }

        private Task EnviarEmailSuporteOperacional(string titulo, string body)
        {
            this._emailHandler.EnviarEmailSuporteOperacional(titulo, body);
            return Task.CompletedTask;
        }
    }
}