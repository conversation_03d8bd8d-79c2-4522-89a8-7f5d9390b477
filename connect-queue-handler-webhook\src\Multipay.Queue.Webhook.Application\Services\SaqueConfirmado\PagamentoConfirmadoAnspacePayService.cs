﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.AnspacePay;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado
{
    public interface IPagamentoConfirmadoAnspacePayService : IPagamentoConfirmadoService
    {
    }

    internal class PagamentoConfirmadoAnspacePayService : PagamentoConfirmadoBaseService<PixPagamentoRecebidoModel>, IPagamentoConfirmadoAnspacePayService
    {
        public PagamentoConfirmadoAnspacePayService(
            ILogger<PagamentoConfirmadoAnspacePayService> logger,
            IEstornarSaqueService estornarSaqueService,
            ICentralizadorLogsService centralizadorLogsService, IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IBrasilApiService brasilApiService, IUnitOfWork unitOfWork) :
            base(logger, centralizadorLogsService, busNotificarSaqueConfirmado, brasilApiService, estornarSaqueService, unitOfWork)
        {
        }

        protected override string NomeBanco => Bancos.AnspacePay;

        protected override string ObterEndToEndId(PixPagamentoRecebidoModel parameters)
        {
            if (string.IsNullOrWhiteSpace(parameters.EndToEndIdOriginal))
                return parameters.EndToEndId;
            return parameters.EndToEndIdOriginal;
        }

        protected override Status? ObterStatus(PixPagamentoRecebidoModel parameters)
        {
            if (parameters.OperationType == "CREDIT_REFUND")
                return PagamentoConfirmadoBaseService<PixPagamentoRecebidoModel>.Status.DEVOLVIDO;
            if (parameters.OperationType == "PAYMENT_UNDO")
                return PagamentoConfirmadoBaseService<PixPagamentoRecebidoModel>.Status.ERRO;
            return PagamentoConfirmadoBaseService<PixPagamentoRecebidoModel>.Status.EFETIVADO;
        }
    }
}