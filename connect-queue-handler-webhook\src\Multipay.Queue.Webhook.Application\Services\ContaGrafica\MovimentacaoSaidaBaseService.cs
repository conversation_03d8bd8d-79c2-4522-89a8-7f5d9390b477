﻿using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Emails;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.ContaGrafica
{
    public interface IMovimentacaoSaidaService<T>
    {
        Task<bool> ProcessarEnvioPIX(string idWebhook, T parameters);

        Task<bool> ProcessarEnvioTED(string idWebhook, T parameters);

        Task<bool> ProcessarTransferenciaEntreContas(string idWebhook, T parameters);
    }

    public interface IMovimentacaoSaidaModel
    {
        string? CodigoSolicitacaoSaque { get; }
        string CodigoAutenticacao { get; }
        string NSU { get; }

        string ObterOperacao();
    }

    internal abstract class MovimentacaoSaidaBaseService<T> : IMovimentacaoSaidaService<T>
        where T : IMovimentacaoSaidaModel, new()
    {
        private readonly IUnitOfWork unitOfWork;
        private readonly IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado;
        private readonly IEmailHandler _emailHandler;

        public MovimentacaoSaidaBaseService(IUnitOfWork unitOfWork,
            IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado,
            IEmailHandler emailHandler)
        {
            this.unitOfWork = unitOfWork;
            this.busNotificarSaqueConfirmado = busNotificarSaqueConfirmado;
            _emailHandler = emailHandler;
        }

        protected abstract TipoBanco TipoBanco { get; }

        public Task<bool> ProcessarTransferenciaEntreContas(string idWebhook, T parameters)
        {
            return ProcessarEnvio(idWebhook, parameters);
        }

        public Task<bool> ProcessarEnvioTED(string idWebhook, T parameters)
        {
            return ProcessarEnvio(idWebhook, parameters);
        }

        public Task<bool> ProcessarEnvioPIX(string idWebhook, T parameters)
        {
            return ProcessarEnvio(idWebhook, parameters);
        }

        public async Task<bool> ProcessarEnvio(string idWebhook, T parameters)
        {
            if (parameters.CodigoSolicitacaoSaque == null)
            {
                //await EnviarEmailSuporteOperacionalSaqueNaoLocalizado(parameters);
                return true;
            }
            var solicitacaoSaque = await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigo(parameters.CodigoSolicitacaoSaque);

            if (solicitacaoSaque == null)
            {
                await EnviarEmailSuporteOperacionalSaqueNaoLocalizado(parameters);
                return true;
            }
            // Marcando como em processamento
            await unitOfWork.SolicitacaoSaqueRepository.UpdateMudarStatusEmProcessamento(solicitacaoSaque.Id);
            // Concluindo transação
            await unitOfWork.SolicitacaoSaqueRepository.UpdateMudarStatusConcluido(solicitacaoSaque.Id, parameters.NSU, idWebhook);

            busNotificarSaqueConfirmado.PublicarNotificacaoCliente(solicitacaoSaque.Id, idWebhook, true, "Completed");

            return true;
        }

        private Task EnviarEmailSuporteOperacionalSaqueNaoLocalizado(T parameters)
        {
            var operacao = parameters.ObterOperacao();

            var body = $"Não foi encontrada nenhuma solicitação de saque com o código de autenticação informado!<br/><br />Código de autenticação: {parameters.CodigoAutenticacao}<br/><br />JSON:{parameters.ToJson()}";
            return EnviarEmailSuporteOperacional($"CallBack {TipoBanco} - {operacao} - Solicitação saque não encontrada", body);
        }

        private Task EnviarEmailSuporteOperacional(string titulo, string body)
        {
            _emailHandler.EnviarEmailSuporteOperacional(titulo, body);
            return Task.CompletedTask;
        }
    }
}