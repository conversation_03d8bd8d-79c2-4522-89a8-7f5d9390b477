﻿namespace Multipay.Queue.Webhook.Application.Models.Saques;

internal class SolicitacaoSaqueRecebidoSulCredModel
{
    public DataObj Data { get; set; } = new DataObj();
    public string Type { get; set; } = string.Empty;
    public StatusEnum Status => Data.Status;

    public class DataObj
    {
        public int Id { get; set; }
        public string? TxId { get; set; }
        public string? Message { get; set; }
        public string? PixKey { get; set; }
        public StatusEnum Status { get; set; }
        public PaymentData Payment { get; set; } = null!;
        public object[]? Refunds { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? ErrorCode { get; set; }
        public string EndToEndId { get; set; } = string.Empty;
        public TicketdataObj? TicketData { get; set; }
        public string WebhookType { get; set; } = string.Empty;
        public AccountData DebtorAccount { get; set; } = null!;
        public string IdempotencyKey { get; set; } = string.Empty;
        public CreditDebitTypeEnum CreditDebitType { get; set; }
        public AccountData CreditorAccount { get; set; } = null!;
        public string LocalInstrument { get; set; } = string.Empty;
        public string TransactionType { get; set; } = string.Empty;
        public object? RemittanceInformation { get; set; }

        public class PaymentData
        {
            public decimal Amount { get; set; }
            public string Currency { get; set; } = string.Empty;
        }

        public class TicketdataObj
        {
        }

        public class AccountData
        {
            public string Ispb { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public string Issuer { get; set; } = string.Empty;
            public string Number { get; set; } = string.Empty;
            public string Document { get; set; } = string.Empty;
            public AccountTypeEnum AccountType { get; set; }
        }
    }
}