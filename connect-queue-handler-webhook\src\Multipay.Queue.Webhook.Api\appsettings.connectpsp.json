{"ConnectionStrings": {"MultiPayDatabase": "Data Source=************; Initial Catalog=CONNECT_DB; User ID=anspace_porhml; password=********************************************; Trusted_Connection=False; Connection Timeout=360; MultipleActiveResultSets=True; TrustServerCertificate=True; Encrypt=False;"}, "MongoStoreDatabaseSettings": {"ConnectionString": "mongodb://admin:dSV/TpM3V7y9cv09bH61KQ==@************/admin", "DatabaseName": "connectpsp_log_db", "CollectionNames": {"LogCobranca": "logcobranca", "RequisicoesAPI": "requisicoesapi", "RequisicoesBMP": "requisicoesbmp", "WebhookFornecedor": "webhookfornecedor", "LogSolicitacaoSaque": "logsolicitacaosaque", "EnvioWebhookCobranca": "enviowebhookcobranca", "RequisicoesFornecedor": "requisicoesfornecedor", "WebhookFornecedorCobranca": "webhookfornecedorcobranca", "EnvioWebhookSolicitacaoSaque": "enviowebhooksolicitacaosaque", "WebhookFornecedorSolicitacaoSaque": "webhookfornecedorsolicitacaosaque", "CobrancasGeradas": "cobrancasger<PERSON>s", "LogAberturaContaDelbank": "logaberturacontadelbank", "EnvioWebhookCliente": "envio_webhook_cliente", "AuditRequisicoesFornecedor": "auditRequisicoesfornecedor"}}, "RabbitMqSettings": {"Setup": {"EventBusConnection": "b-2bf62a71-b939-49d7-93f0-c3ce6c7545a4.mq.us-east-1.amazonaws.com", "EventBusPort": 5671, "EventBusVirtualHost": "connectpsp", "EventBusUserName": "admin", "EventBusPassword": "2dYCRFntHA1qHK3SWutNWw==", "EventBusRetryCount": "5", "EnableSsl": true}}}