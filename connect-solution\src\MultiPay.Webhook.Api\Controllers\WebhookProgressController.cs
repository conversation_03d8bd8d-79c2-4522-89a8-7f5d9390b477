using Microsoft.AspNetCore.Mvc;
using MultiPay.Webhook.Api.Services;

namespace MultiPay.Webhook.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class WebhookProgressController : ControllerBase
    {
        private readonly IWebhookProgressService _progressService;

        public WebhookProgressController(IWebhookProgressService progressService)
        {
            _progressService = progressService;
        }

        [HttpGet("{jobId}")]
        public async Task<IActionResult> GetProgress(string jobId)
        {
            var progress = await _progressService.GetProgress(jobId);
            
            if (progress == null)
                return NotFound($"Job {jobId} não encontrado");

            return Ok(progress);
        }
    }
}
