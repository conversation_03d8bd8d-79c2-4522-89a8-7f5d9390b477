﻿using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;
using System.Text;

namespace Multipay.Queue.NotificarCliente.Application.Services
{
    public interface INotiticarClienteSaqueConcluidoService
    {
        Task<bool> EnviarNotificacao(SaqueConfirmadoNotificarClienteEvent @event);
    }

    public class NotiticarClienteSaqueConcluidoService : INotiticarClienteSaqueConcluidoService
    {
        private readonly IHttpClientFactory httpClientFactory;
        private readonly IUnitOfWork unitOfWork;
        private readonly ICentralizadorLogsService centralizadorLogsService;

        public NotiticarClienteSaqueConcluidoService(
            IHttpClientFactory httpClientFactory,
            IUnitOfWork unitOfWork,
            ICentralizadorLogsService centralizadorLogsService)
        {
            this.httpClientFactory = httpClientFactory;
            this.unitOfWork = unitOfWork;
            this.centralizadorLogsService = centralizadorLogsService;
        }

        public async Task<bool> EnviarNotificacao(SaqueConfirmadoNotificarClienteEvent @event)
        {
            var solicitacaoSaque = await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorId(@event.IdSolicitacaoSaque);

            if (solicitacaoSaque == null)
                return false;

            if (string.IsNullOrEmpty(solicitacaoSaque.UrlConfirmacao))
            {
                return true;
            }

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, solicitacaoSaque.UrlConfirmacao)
            {
                Content = ObterBodyContent(@event, solicitacaoSaque, out string bodyJson)
            };

            if (!string.IsNullOrWhiteSpace(solicitacaoSaque.RequestIdWebhook))
                httpRequestMessage.Headers.Add("X-Request-Id", solicitacaoSaque.RequestIdWebhook);

            var httpClient = httpClientFactory.CreateClient();

            var httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);
            var isSuccessStatusCode = httpResponseMessage.IsSuccessStatusCode;

            if (@event.Sucesso)
            {
                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, "Enviando o webhook");
                await AdicionarLogEnvioWebhookSolicitacaoSaque(solicitacaoSaque, bodyJson, httpResponseMessage);
            }
            else
            {
                centralizadorLogsService.AdicionarLogWebhookFornecedorSolicitacaoSaque(@event.IdWebhook, solicitacaoSaque.Id, ObterAcao(@event.Status));
            }
            return isSuccessStatusCode;
        }

        private async Task AdicionarLogEnvioWebhookSolicitacaoSaque(ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string bodyJson, HttpResponseMessage httpResponseMessage)
        {
            centralizadorLogsService.AdicionarLogEnvioWebhookSolicitacaoSaque(
                solicitacaoSaque.Id,
                solicitacaoSaque.Codigo,
                //"ENVIO DE PIX",
                "PAGAMENTO DE SAQUE: " + solicitacaoSaque.Codigo,
                solicitacaoSaque.UrlConfirmacao!,
                bodyJson,
                $"{httpResponseMessage.StatusCode}",
                await httpResponseMessage.Content.ReadAsStringAsync());
        }

        private static string ObterAcao(string status)
        {
            return (status ?? string.Empty) switch
            {
                "REJECTED TRANSACTION" => "PIX REJEITADO",
                "RETURNED TRANSACTION" => "PIX EXPIRADO",
                "INSTABILITY" => "PIX COM ERRO",
                _ => status ?? string.Empty,
            };
        }

        private static StringContent ObterBodyContent(SaqueConfirmadoNotificarClienteEvent @event, ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, out string bodyJson)
        {
            if (@event.Sucesso)
                return ObterBodyContentSucesso(@event, solicitacaoSaque, out bodyJson);

            return ObterBodyContentFalha(@event, solicitacaoSaque, out bodyJson);
        }

        private static StringContent ObterBodyContentFalha(SaqueConfirmadoNotificarClienteEvent @event, ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, out string bodyJson)
        {
            var body = new
            {
                withdrawCode = solicitacaoSaque.Codigo,
                customId = solicitacaoSaque.CustomId,
                transactionCode = solicitacaoSaque.CodigoTransacao,
                updateCode = @event.StatusCode,
                updateMessage = @event.Status,
                amount = Convert.ToInt64(solicitacaoSaque.ValorSolicitado * 100)
            };
            
            return new StringContent(bodyJson = body.ToJson(), Encoding.UTF8, System.Net.Mime.MediaTypeNames.Application.Json);
        }

        private static StringContent ObterBodyContentSucesso(SaqueConfirmadoNotificarClienteEvent @event, ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, out string bodyJson)
        {
            var transacionData = new
            {
                operationType = solicitacaoSaque.TipoOperacaoBancaria,
                transactionDate = solicitacaoSaque.DataSolicitacao,
                completedDate = solicitacaoSaque.DataConclusao,
                chargebackDate = solicitacaoSaque.DataEstorno,
                paymentDate = solicitacaoSaque.DataPagamento
            };
            var recipientData = new
            {
                recipientName = solicitacaoSaque.NomeFavorecido,
                recipientDocumentID = solicitacaoSaque.CPFCNPJFavorecido,
                recipientBankAgency = solicitacaoSaque.AgenciaFavorecido,
                recipientBankAccount = solicitacaoSaque.ContaFavorecido,
                recipientPIXKeyType = solicitacaoSaque.TipoChavePIX,
                recipientPIXKey = solicitacaoSaque.ChavePIX
            };

            var body = new
            {
                error = false,
                returnCode = "00",
                returnMessage = "Success",
                withdrawCode = solicitacaoSaque.Codigo,
                customId = solicitacaoSaque.CustomId,
                authenticationCode = solicitacaoSaque.CodigoAutenticacao,
                transactionCode = solicitacaoSaque.CodigoTransacao,
                amount = Convert.ToInt64(solicitacaoSaque.ValorSolicitado * 100),
                transaction = transacionData,
                recipient = recipientData,
                requestDate = solicitacaoSaque.DataSolicitacao,
                paymentDate = solicitacaoSaque.DataPagamento,
                chargebackDate = solicitacaoSaque.DataEstorno,
                completionDate = solicitacaoSaque.DataConclusao,
                status = @event.Status
            };
            
            return new StringContent(bodyJson = body.ToJson(), Encoding.UTF8, System.Net.Mime.MediaTypeNames.Application.Json);
        }
    }
}