using Application.Services.Interfaces;
using Domain.Constants;
using Infra.Repositories.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using static Microsoft.AspNetCore.Http.Results;
using Microsoft.Extensions.Logging;

namespace Application.Handlers.Rebalance;

public class RebalanceHandler
(
    ILogger<RebalanceHandler> logger,
    IIntegrationRepository integrationRepository,
    ICredentialService credentialService,
    IWalletService walletService,
    IRebalanceService rebalanceService) : IRequestHandler<RebalanceCommand, IResult>
{
    public async Task<IResult> Handle(RebalanceCommand request, CancellationToken cancellationToken)
    {
        logger.Trace("Obtendo integrações de clientes");

        var integrations = await integrationRepository.GetCustomerIntegrations(cancellationToken);
        
        if (!integrations.Any())
        {
            logger.Error("Nenhuma integração encontrada para executar.");
            return NotFound(MessagesConstants.IntegrationNotFound);
        }

        foreach (var integration in integrations)
        {
            logger.Trace("Executando rebalance para o cliente Id: {CustomerId}", integration.CustomerId);

            var credentialResult = await credentialService.GetCredentialByCustomerId(integration.CustomerId, cancellationToken);
            
            if (!credentialResult.Success)
            {
                logger.Error("Dados da credencial não encontrados para o cliente Id: {CustomerId}",  integration.CustomerId);
                continue;
            }
            
            var credential = credentialResult.Value!;

            var walletResult = await walletService.GetWalletBalance(integration, cancellationToken);
            
            if (!walletResult.Success)
            {
                logger.Error("Erro ao ober o saldo da carteira do cliente Id: {CustomerId}", integration.CustomerId);
                continue;
            }
            
            var amountBalance = Convert.ToInt64(walletResult.Value!.Balance);

            var balanceResult = await rebalanceService.ExecuteRebalanceAsync(credential, amountBalance, cancellationToken);
            
            if (!balanceResult.Success)
            {
                logger.Error("Erro ao executar rebalance para o cliente Id: {CustomerId}", integration.CustomerId);
                continue;
            }

            logger.Trace("Rebalance executado com sucesso para o cliente Id: {CustomerId}", integration.CustomerId);
        }

        logger.Information("Rebalance executado com sucesso");

        return Ok();
    }
}
