﻿namespace Multipay.Queue.Webhook.Application.Models.Aarin;
public class ReversalPixAarinModel
{
    public string? PixId { get; set; }
    public string? AccountId { get; set; }
    public string? TransactionId { get; set; }
    public string? Value { get; set; }
    public string? Type { get; set; }
    public string? Status { get; set; }
    public string? ParentPixId { get; set; }
    public string? TxId { get; set; }
    public string? E2EId { get; set; }
    public string? RequestDescription { get; set; }
    public string? CreatedAt { get; set; }
    public string? AcceptedAt { get; set; }
    public string? SettledAt { get; set; }
    public string? PayeeName { get; set; }
    public string? PayeeDocumentType { get; set; }
    public string? PayeeDocument { get; set; }
    public string? PayeeBankBranch { get; set; }
    public string? PayeeBankAccount { get; set; }
    public string? PayeeBankIspb { get; set; }
    public string? PayeeBankAccountType { get; set; }
    public string? PayerName { get; set; }
    public string? PayerDocumentType { get; set; }
    public string? PayerDocument { get; set; }
    public string? PayerBankAccountType { get; set; }
    public string? PayerBankBranch { get; set; }
    public string? PayerBankAccount { get; set; }
    public object? Charge { get; set; }

}
