﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Sicoob.Core;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Responses;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.Repositories;
using Multipay.Service.Criptography;
using System.Net;
using System.Text.Json;

namespace Multipay.Infrastructure.Plugin.Sicoob.Services
{
    public interface IAuthService
    {
        Task<ApiResponse> GetToken(string clientId);
    }

    internal class AuthService : BaseService, IAuthService
    {
        private const string GET_TOKEN_KEY = "TokenSicoobEncrypted";
        private readonly ILogger<AuthService> logger;
        private readonly IErroRepository _erroRepository;
        private readonly ICentralizadorLogsService centralizadorLogsService;
        private readonly IMemoryCache memoryCache;

        public AuthService(
            ILogger<AuthService> logger,
            IEnvironment environment,
            IErroRepository erroRepository,
            ICentralizadorLogsService centralizadorLogsService,
            IMemoryCache memoryCache,
            CriptoService criptoService) : base(environment, criptoService)
        {
            this.logger = logger;
            _erroRepository = erroRepository;
            this.centralizadorLogsService = centralizadorLogsService;
            this.memoryCache = memoryCache;
        }

        public async Task<ApiResponse> GetToken(string clientId)
        {
            try
            {
                if (memoryCache.TryGetValue($"{GET_TOKEN_KEY}:{clientId}", out TokenResponseModel? cache))
                    return new ApiResponse(true, HttpStatusCode.OK, cache);

                var url = $"{_environment.AuthURL}/auth/realms/cooperado/protocol/openid-connect/token";
                var handler = CreateClientHandler();

                var body = new Dictionary<string, string>
                {
                    { "client_id", clientId },
                    { "grant_type", "client_credentials" },
                    { "scope", "cob.write cob.read cobv.write cobv.read lotecobv.write lotecobv.read pix.write pix.read webhook.read webhook.write payloadlocation.write payloadlocation.read" }
                };

                using var content = new FormUrlEncodedContent(body);
                using var _client = new HttpClient(handler);

                _client.DefaultRequestHeaders.Add("client_id", clientId);

                using HttpResponseMessage response = _client.PostAsync(url, content).Result;

                logger.LogInformation("{url}, Status: {status}, {response}",
                    response.RequestMessage!.RequestUri!.AbsoluteUri,
                    response.StatusCode,
                    (response.IsSuccessStatusCode ? string.Empty : (await response.Content.ReadAsStringAsync())));

                await response.EnsureSuccessStatusCodeAsync();

                var returnObj = GetObject<TokenResponseModel>(response);

                centralizadorLogsService.AdicionarLogRequisicoesFornecedor("Sicoob", "AUTENTICACAO GET TOKEN", "AuthService.GetToken", JsonSerializer.Serialize(body), (int)response.StatusCode, JsonSerializer.Serialize(returnObj));

                memoryCache.Set($"{GET_TOKEN_KEY}:{clientId}", returnObj, DateTimeOffset.Now.AddSeconds(returnObj?.Expires_in ?? 20 * 60));

                return new ApiResponse(true, response.StatusCode, returnObj);
            }
            catch (HttpResponseException ex)
            {
                await _erroRepository.InsertErro("AuthService", "GetToken", "MultiPay.Plugin.BancoSicoob.AuthService.GetToken", ex.Message, ex.StackTrace ?? string.Empty, ex.ToString());
                return new ApiResponse(false, ex.StatusCode, ex.Response);
            }
            catch (Exception exc)
            {
                await _erroRepository.InsertErro("AuthService", "GetToken", "MultiPay.Plugin.BancoSicoob.AuthService.GetToken", exc.Message, exc.StackTrace ?? string.Empty, exc.ToString());
                return new ApiResponse(false, HttpStatusCode.InternalServerError, exc.ToString());
            }
        }
    }
}