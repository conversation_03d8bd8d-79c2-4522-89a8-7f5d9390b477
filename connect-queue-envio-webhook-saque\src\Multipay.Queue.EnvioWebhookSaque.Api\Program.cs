using HealthChecks.UI.Client;
using Multipay.Queue.EnvioWebhookSaque.Api.Workers;
using Multipay.Queue.EnvioWebhookSaque.Application.Setup;
using Multipay.Queue.EnvioWebhookSaque.Core.Extensions;
using Multipay.Queue.EnvioWebhookSaque.Core.Utils;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.Setup;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Setup;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Services;
using Multipay.Service.Criptography;
using Serilog;

var builder = WebApplication.CreateBuilder(args);
var ambienteHelp = new AmbienteHelp(builder.Configuration);

try
{
    var env = builder.Environment;
    var config = builder.Configuration;

    Log.Information("Starting host");

    config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

    builder.Services.AddSingleton(ambienteHelp);

    string tema = ambienteHelp.Tema ?? throw new ArgumentNullException("Tema não localizado em AppSettings");

    config.AddJsonFile($"appsettings.{tema.ToLower()}.json", optional: true, reloadOnChange: true);

    builder.Host.UseSerilog((context, configuration) =>
        configuration.ReadFrom.Configuration(context.Configuration));

    Log.Information($"Tema: {tema}", tema);

    CriptoService.Theme = tema;
    builder.Services.AddSingleton<CriptoService>();

    var criptoService = new CriptoService();

    builder.Services.AddDependencyInjectionApplicationRepositoriesPetapoco(builder.Configuration, criptoService);

    var rabbitMqSettings = builder.Services.AddDependencyInjectionRabbitMqSaquesServices(builder.Configuration, criptoService);

    if (rabbitMqSettings.Setup == null)
    {
        throw new Exception("Não foi possível carregar o setup de configurações do rabbitmq");
    }

    builder.Services.AddHealthChecks()
                    .AddSqlServer(GetConnectionString(config, criptoService))
                    .AddMongoDb(GetConnectionStringMongo(config, criptoService))
                    .AddRabbitMQ(rabbitMqSettings.Setup.UriHost());

    builder.Services.AddControllers();

    builder.Services.AddHttpClient();

    builder.Services.AddDependencyInjectionApplicationServices(builder.Configuration, criptoService);

    builder.Services.AddHostedService<WorkerSaqueConfirmadoNotificarCliente>();
    //builder.Services.AddHostedService<WorkerSaqueConfirmadoNotificarClientePublishNotification>();
    builder.Services.AddHostedService<WorkerSolicitacaoSaquesEstornado>();

    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen();

    var app = builder.Build();

    var serviceMongo = app.Services.GetRequiredService<IConnectMongoService>();
    Log.Information("Verify Mongo: {Verify}", serviceMongo.Verify());

    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI();
    }

    app.UseHttpsRedirection();

    app.MapHealthChecks("/_health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
        ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
    });

    app.UseAuthorization();

    app.MapControllers();

    app.Run("http://*:0");

    return 0;
}
catch (Exception ex)
{
    Log.Fatal(ex, "Host terminated unexpectedly");

    return 1;
}
finally
{
    Log.CloseAndFlush();
}

string GetConnectionStringMongo(ConfigurationManager config, CriptoService criptoService)
{
    var cnn = ambienteHelp.Mongo_ConnectionString;

    if (string.IsNullOrWhiteSpace(cnn))
        cnn = config["MongoStoreDatabaseSettings:ConnectionString"]!;

    var start = cnn.IndexOf("//") + 2;
    var end = cnn.IndexOf("@", start);

    var password = cnn.Substring(start, end - start).Split(":")[1];
    var encryptedPassword = password;

    password = criptoService.Decrypt(password).Result;

    cnn = cnn.Replace(encryptedPassword, password);

    return cnn;
}

string GetConnectionString(ConfigurationManager config, CriptoService criptoService)
{
    var cnn = ambienteHelp.SqlServer_ConnectionString;

    if (string.IsNullOrWhiteSpace(cnn))
        cnn = config.GetConnectionString("MultiPayDatabase")!;

    var start = cnn.IndexOf("password=") + 9;
    var end = cnn.IndexOf(";", start);

    var password = cnn.Substring(start, end - start);
    var encryptedPassword = password;

    password = criptoService.Decrypt(password).Result;

    cnn = cnn.Replace(encryptedPassword, password);

    return cnn;
}