﻿using Multipay.Queue.EnvioWebhookSaque.Application.Services;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.EnvioWebhookSaque.Api.Workers
{
    public class WorkerSolicitacaoSaquesEstornado : BackgroundConsumerService<SolicitacaoSaquesEstornadoEvent>
    {
        private readonly IServiceProvider serviceProvider;

        public WorkerSolicitacaoSaquesEstornado(
            ILogger<WorkerSolicitacaoSaquesEstornado> logger,
            IServiceProvider serviceProvider,
            IBusConsumerSolicitacaoSaqueEstornado busConsumer) : base(logger, busConsumer)
        {
            this.serviceProvider = serviceProvider;
        }

        protected override async Task<bool> ProcessEventHandler(string mensagem, SolicitacaoSaquesEstornadoEvent @event, bool _)
        {
            using IServiceScope scope = serviceProvider.CreateScope();
            try
            {
                var service = scope.ServiceProvider.GetRequiredService<ISolicitacaoSaquesEstornadoNotificarClienteService>();
                return await service.EnviarNotificacao(@event.UrlAtualizacao, @event.CodigoSaque, @event.CustomId, @event.CustomUserId, @event.Guid, @event.CodigoTransacao);
            }
            finally
            {
                var logsService = scope.ServiceProvider.GetRequiredService<ICentralizadorLogsService>();
                logsService.Salvar();
            }
        }
    }
}