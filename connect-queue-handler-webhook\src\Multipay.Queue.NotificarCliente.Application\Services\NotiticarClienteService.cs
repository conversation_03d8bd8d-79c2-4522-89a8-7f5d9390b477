﻿using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;
using System.Text;

namespace Multipay.Queue.NotificarCliente.Application.Services
{
    public interface INotiticarClienteService
    {
        Task<bool> EnviarNotificacao(NotificarClienteEvent @event);
    }

    internal class NotiticarClienteService : INotiticarClienteService
    {
        private readonly IHttpClientFactory httpClientFactory;
        private readonly ICentralizadorLogsService centralizadorLogsService;

        public NotiticarClienteService(
            IHttpClientFactory httpClientFactory,
            ICentralizadorLogsService centralizadorLogsService)
        {
            this.httpClientFactory = httpClientFactory;
            this.centralizadorLogsService = centralizadorLogsService;
        }

        public async Task<bool> EnviarNotificacao(NotificarClienteEvent @event)
        {
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, @event.Url)
            {
                Content = new StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(@event.Body), Encoding.UTF8, System.Net.Mime.MediaTypeNames.Application.Json)
            };

            httpRequestMessage.Headers.Add("Type", @event.Type);

            var httpClient = httpClientFactory.CreateClient();

            var httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);
            var isSuccessStatusCode = httpResponseMessage.IsSuccessStatusCode;

            centralizadorLogsService.AdicionarLogEnvioWebhookCliente(@event.IdCliente, @event.Type, @event.Url, await httpRequestMessage.Content.ReadAsStringAsync(), ((int)httpResponseMessage.StatusCode).ToString(), await httpResponseMessage.Content.ReadAsStringAsync());

            return isSuccessStatusCode;
        }
    }
}