﻿using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class InconsistenciaCobrancaService : IInconsistenciaCobrancaService
    {
        private readonly IUnitOfWork unitOfWork;

        public InconsistenciaCobrancaService(IUnitOfWork unitOfWork)
        {
            this.unitOfWork = unitOfWork;
        }

        public Task InserirInconsistenciaAsync(InconsistenciaCobrancaModel inconsistenciaCobranca)
        {
            return unitOfWork.InconsistenciaCobrancaRepository.Inserir(inconsistenciaCobranca);
        }
    }
}