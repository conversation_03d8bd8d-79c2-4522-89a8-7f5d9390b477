﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Multipay.Queue.NotificarCliente.Application.Services;

namespace Multipay.Queue.NotificarCliente.Application.Setup
{
    public static class DependencyInjectionServices
    {
        public static void AddDependencyInjectionNotificarClienteApplicationServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<INotiticarClienteService, NotiticarClienteService>();
            services.AddScoped<INotiticarClienteCobrancaConcluidaService, NotiticarClienteCobrancaConcluidaService>();
            services.AddScoped<INotiticarClienteSaqueConcluidoService, NotiticarClienteSaqueConcluidoService>();
            services.AddScoped<INotiticarClientePublishNotificationService, NotiticarClientePublishNotificationService>();

            services.AddHttpClient();
        }
    }
}