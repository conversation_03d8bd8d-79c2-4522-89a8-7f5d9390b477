﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Interfaces\**" />
    <EmbeddedResource Remove="Interfaces\**" />
    <None Remove="Interfaces\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Multipay.Queue.Webhook.Core.Commons\Multipay.Queue.Webhook.Core.Commons.csproj" />
    <ProjectReference Include="..\Multipay.Queue.Webhook.Infrastructure.DAPetapoco\Multipay.Queue.Webhook.Infrastructure.DAPetapoco.csproj" />
    <ProjectReference Include="..\Multipay.Queue.Webhook.Infrastructure.RabbitMq\Multipay.Queue.Webhook.Infrastructure.RabbitMq.csproj" />
    <ProjectReference Include="..\Multipay.Queue.Webhook.Infrastructure\Multipay.Queue.Webhook.Infrastructure.csproj" />
  </ItemGroup>

</Project>
