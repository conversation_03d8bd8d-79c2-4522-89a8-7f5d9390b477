using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.DelBank;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Emails;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.DelBank
{
    public interface IMovimentacaoSaidaService
    {
        Task<bool> ProcessarPagamento(string sourceParamenters, string idWebhook);
    }

    internal class MovimentacaoSaidaService : IMovimentacaoSaidaService
    {
        private readonly IUnitOfWork unitOfWork;
        private readonly ICentralizadorLogsService centralizadorLogsService;
        private readonly IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado;
        private readonly IEmailHandler emailHandler;
        private readonly ILogger<MovimentacaoSaidaService> _logger;

        public MovimentacaoSaidaService(IUnitOfWork unitOfWork,
            ICentralizadorLogsService centralizadorLogsService,
            IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado,
            IEmailHandler emailHandler,
            ILogger<MovimentacaoSaidaService> logger)
        {
            this.unitOfWork = unitOfWork;
            this.centralizadorLogsService = centralizadorLogsService;
            this.busNotificarSaqueConfirmado = busNotificarSaqueConfirmado;
            this.emailHandler = emailHandler;
            _logger = logger;

        }

        public async Task<bool> ProcessarPagamento(string sourceParamenters, string idWebhook)
        {
            var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<PIXDelBankPagamentoRequestModelV1>(sourceParamenters)!;

            var solicitacaoSaque = await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigo(parameters.IdempotencyKey!);

            if (solicitacaoSaque == null)
                solicitacaoSaque = await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(parameters.EndToEndId!);

            if (solicitacaoSaque == null)
            {
                try
                {
                    Thread.Sleep(2000);

                    _logger.LogInformation("[{Evento}] - Verificando se é saque do tipo: Externo", parameters.Status + " - " + parameters.EventType);

                    var Payer = parameters.Payer;
                    var Beneficiary = parameters.Beneficiary;

                    var Sender = parameters.Sender;
                    var Recipient = parameters.Recipient;

                    string BeneficiaryBranch, BeneficiaryNumber, BeneficiaryHolderDocument, BeneficiaryHolderName;
                    string PayerBranch, PayerNumber, PayerHolderDocument, PayerHolderName;

                    if (Payer != null)
                    {
                        PayerBranch = Payer.Branch!;
                        PayerNumber = Payer.Number!;
                        PayerHolderDocument = Payer.Holder!.Document!;
                        PayerHolderName = Payer.Holder!.Name!;
                    }
                    else
                    {
                        _logger.LogInformation("[{Evento}] - Convertendo Payer em Sender", "EV2002");

                        PayerBranch = Sender!.Branch!;
                        PayerNumber = Sender.Number!;
                        PayerHolderDocument = Sender.Holder!.Document!;
                        PayerHolderName = Sender.Holder!.Name!;
                    }

                    if (Beneficiary != null)
                    {
                        BeneficiaryBranch = Beneficiary.Branch!;
                        BeneficiaryNumber = Beneficiary.Number!;
                        BeneficiaryHolderDocument = Beneficiary.Holder!.Document!;
                        BeneficiaryHolderName = Beneficiary.Holder!.Name!;
                    }
                    else
                    {
                        _logger.LogInformation("[{Evento}] - Convertendo Beneficiary em Recipient", "EV2002");

                        BeneficiaryBranch = Recipient!.Branch!;
                        BeneficiaryNumber = Recipient!.Number!;
                        BeneficiaryHolderDocument = Recipient!.Holder!.Document!;
                        BeneficiaryHolderName = Recipient!.Holder!.Name!;
                    }

                    var saqueManual = await unitOfWork.ContaBancariaRepository.BuscaSaqueManual(parameters.EndToEndId!);

                    if (parameters.Status != "PIX_REFUNDED" && saqueManual.Status != null)
                    {
                        _logger.LogInformation("[{Evento}] - Saque já lançado anteriormente. E2E: {E2E}", "EV2004", parameters.EndToEndId);

                        return true;
                    }

                    if (parameters.Status != "PIX_EFFECTIVE" && (parameters.Status == "PIX_REFUNDED" || saqueManual.Status == null))
                    {
                        _logger.LogInformation("[{Evento}] - Estorno - Dados de Agencia: {Agencia}, Conta: {Conta}, Documento: {Documento}, Nome: {Nome}", "EV2003", PayerBranch, PayerNumber, PayerHolderDocument, PayerHolderName);

                        if (parameters.Amount != saqueManual.Valor)
                        {
                            _logger.LogInformation("[{Evento}] - Valor do Estorno é divergente do Saque. E2E: {E2E}", "EV2003", parameters.EndToEndId);

                            return true;
                        }

                        //TODO Verificar valor do Saque Solicitado 
                        // Incluindo credito de saque Estornado
                        var idContaIdClientePayerEstorno = await unitOfWork.ContaBancariaRepository.BuscaClientePorAgenciaContaBancariaEmpresa(PayerBranch, PayerNumber);

                        if (idContaIdClientePayerEstorno.IdContaBancariaEmpresa != null)
                        {
                            _logger.LogInformation("[{Evento}] - Saque Estornado para o Pagador de {Valor}, IdCliente debitado {IdCliente}. Conta bancaria Empresa: {IdContaBancariaEmpresa}", "EV2003", parameters.Amount, idContaIdClientePayerEstorno.IdCliente, idContaIdClientePayerEstorno.IdContaBancariaEmpresa);

                            _ = await unitOfWork.ContaBancariaRepository.CreditoPorChavePixClienteId(parameters!.Amount!.ToString()!, (int)idContaIdClientePayerEstorno.IdCliente!, "ESTORNO " + PayerHolderDocument + " - " + PayerHolderName, (int)idContaIdClientePayerEstorno.IdContaBancariaEmpresa);
                        }

                        // Incluindo debito de saque Estornado
                        var idContaIdClienteBeneficiaryEstorno = await unitOfWork.ContaBancariaRepository.BuscaClientePorAgenciaContaBancariaEmpresa(BeneficiaryBranch, BeneficiaryNumber);

                        if (idContaIdClienteBeneficiaryEstorno.IdContaBancariaEmpresa != null)
                        {
                            _logger.LogInformation("[{Evento}] - Saque Estornado para o Pagador de {Valor}, IdCliente debitado {IdCliente}. Conta bancaria Empresa: {IdContaBancariaEmpresa}", "EV2003", parameters.Amount, idContaIdClienteBeneficiaryEstorno.IdCliente, idContaIdClienteBeneficiaryEstorno.IdContaBancariaEmpresa);

                            _ = await unitOfWork.ContaBancariaRepository.DebitoPorChavePixClienteId(parameters.Amount!.ToString()!, (int)idContaIdClienteBeneficiaryEstorno.IdCliente!, "ESTORNO " + PayerHolderDocument + " - " + PayerHolderName, (int)idContaIdClienteBeneficiaryEstorno.IdContaBancariaEmpresa);
                        }

                        await unitOfWork.ContaBancariaRepository.InsertSaqueManual((decimal)parameters.Amount!, (int)idContaIdClienteBeneficiaryEstorno.IdCliente!, parameters.EndToEndId!, DateTime.Now);

                        return true;
                    }

                    _logger.LogInformation("[{Evento}] - Dados de Agencia: {Agencia}, Conta: {Conta}, Documento: {Documento}, Nome: {Nome}", "EV2002", PayerBranch, PayerNumber, PayerHolderDocument, PayerHolderName);

                    _logger.LogInformation("[{Evento}] - PIX Enviado de {Valor}", "EV2002", parameters.Amount);

                    var VerificaContaInterna = await unitOfWork.ContaBancariaRepository.ControleMovimentacaoContaBancariaEmpresa(parameters!.ExternalId!);

                    _logger.LogInformation("[{Evento}] - Verificando se é transferencia do tipo REBALANCE", "EV2002");

                    if (VerificaContaInterna.IdContaBancariaEmpresaDestino != null)
                    {
                        _logger.LogInformation("[{Evento}] - Identificado REBALANCE. Nada será creditado.", "EV2002");

                        return true;
                    }

                    _logger.LogInformation("[{Evento}] - Não identificado REBALANCE.", "EV2002");

                    _logger.LogInformation("[{Evento}] - Buscando cliente.", "EV2002");

                    var idContaIdCliente = await unitOfWork.ContaBancariaRepository.BuscaClientePorAgenciaContaBancariaEmpresa(PayerBranch, PayerNumber);

                    _logger.LogInformation("[{Evento}] - Conta afetada {IdContaBancariaEmpresa} do idCliente {IdCliente}.", "EV2002", idContaIdCliente.IdContaBancariaEmpresa, idContaIdCliente.IdCliente);

                    if (idContaIdCliente.IdCliente == null)
                    {
                        _logger.LogInformation("[{Evento}] - A conta não é gerenciada, nada debitado.", "EV2002");

                        return true;
                    }

                    _ = await unitOfWork.ContaBancariaRepository.DebitoPorChavePixClienteId(parameters!.Amount!.ToString()!, (int)idContaIdCliente.IdCliente!, "SAQUE PIX " + BeneficiaryHolderDocument + " - " + BeneficiaryHolderName, (int)idContaIdCliente.IdContaBancariaEmpresa!);

                    _logger.LogInformation("[{Evento}] - PIX Enviado de {Valor}, IdCliente debitado {IdCliente}. Conta bancaria Empresa: {IdContaBancariaEmpresa}", "EV2002", parameters.Amount, idContaIdCliente.IdCliente, idContaIdCliente.IdContaBancariaEmpresa);

                    var idContaIdClienteBeneficiario = await unitOfWork.ContaBancariaRepository.BuscaClientePorAgenciaContaBancariaEmpresa(BeneficiaryBranch, BeneficiaryNumber);

                    if (idContaIdClienteBeneficiario.IdContaBancariaEmpresa != null)
                    {

                        _logger.LogInformation("[{Evento}] - PIX Creditado para o beneficiario de {Valor}, IdCliente debitado {IdCliente}. Conta bancaria Empresa: {IdContaBancariaEmpresa}", "EV2002", parameters.Amount, idContaIdClienteBeneficiario.IdCliente, idContaIdClienteBeneficiario.IdContaBancariaEmpresa);

                        _ = await unitOfWork.ContaBancariaRepository.CreditoPorChavePixClienteId(parameters!.Amount!.ToString()!, (int)idContaIdClienteBeneficiario.IdCliente!, "CRED PIX " + PayerHolderDocument + " - " + PayerHolderName, (int)idContaIdClienteBeneficiario.IdContaBancariaEmpresa);
                    }

                    await unitOfWork.ContaBancariaRepository.InsertSaqueManual((decimal)parameters.Amount, (int)idContaIdCliente.IdCliente, parameters.EndToEndId!, DateTime.Now);

                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[EV2002] - FALHA na Recepção do Saque de {Valor}. Erro: {Erro}", parameters.Amount, ex.Message);
                }

                return true;
            }

            if (solicitacaoSaque.DataEstorno.HasValue)
            {
                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Saque já estornado, interrompendo processo");
                return true;
            }

            if (parameters.Status == "PIX_EFFECTIVE")
            {
                await unitOfWork.SolicitacaoSaqueRepository.UpdateMudarStatusEmProcessamento(solicitacaoSaque.Id);
                await unitOfWork.SolicitacaoSaqueRepository.UpdateMudarStatusConcluido(solicitacaoSaque.Id, null, idWebhook);

                busNotificarSaqueConfirmado.PublicarNotificacaoCliente(solicitacaoSaque.Id, idWebhook, true, "Completed");
            }
            else
            {
                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"BANK STATUS: {parameters.Status}");
                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Aguardando webhook de estorno");

                if (parameters.Error != null)
                {
                    try
                    {
                        return await ProcessarPagamentoComFalha(solicitacaoSaque, parameters.Error, idWebhook);
                    }
                    catch (Exception e)
                    {
                        centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Falha ao realizar estorno - " + e.ToString());
                    }
                }
            }

            return true;
        }

        public async Task<bool> ProcessarPagamentoComFalha(ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, PIXDelBankPagamentoRequestModelV1.ErrorData error, string idWebhook)
        {
            string mensagemJustificativa = $"{error.Code} - {error.Description}";

            centralizadorLogsService.AdicionarLogWebhookFornecedorSolicitacaoSaque(idWebhook, solicitacaoSaque.Id, mensagemJustificativa);

            if (solicitacaoSaque.DataEstorno == null)
                await unitOfWork.SolicitacaoSaqueRepository.EstornarSaque(solicitacaoSaque.Id, mensagemJustificativa);

            busNotificarSaqueConfirmado.PublicarNotificacaoCliente(solicitacaoSaque.Id, idWebhook, false, "REJECTED TRANSACTION");

            return true;
        }
    }
}