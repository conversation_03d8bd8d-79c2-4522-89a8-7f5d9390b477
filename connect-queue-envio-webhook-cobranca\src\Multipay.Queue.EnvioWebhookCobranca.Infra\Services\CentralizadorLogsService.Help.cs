﻿using Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Services
{
    internal partial class CentralizadorLogsService
    {
        public class Help
        {
            public Help(CentralizadorLogsService centralizadorLogs)
            {
                this.centralizadorLogs = centralizadorLogs;
            }

            public CentralizadorLogsService centralizadorLogs { get; }

            public string AdicionarLogWebhookFornecedor(string fornecedor, string tipo, string body, string ipOprigem)
            {
                var document = new WebhookFornecedorModel(fornecedor, tipo, body, ipOprigem);

                centralizadorLogs.Add(document);

                return document.Id;
            }

            public string AdicionarLogEnvioWebhookCobranca(int idCobranca, string acao, string urlWebhook, string requestJson, string httpResponseCode, string responseJson)
            {
                var document = new EnvioWebhookCobrancaModel(idCobranca, acao, urlWebhook, requestJson, httpResponseCode, responseJson);
                centralizadorLogs.Add(document);

                return document.Id;
            }

            public string AdicionarLogWebhookFornecedorCobranca(string idWebhookFornecedor, int idCobranca, string acao)
            {
                var document = new WebhookFornecedorCobrancaModel(idCobranca, idWebhookFornecedor, acao);
                centralizadorLogs.Add(document);

                return document.Id;
            }

            public string AdicionarLogCobranca(int idCobranca, string texto)
            {
                var document = new LogCobrancaModel(idCobranca, texto, string.Empty, string.Empty, true);
                centralizadorLogs.Add(document);

                return document.Id;
            }

            public string AdicionarLogRequisicoesBMP(string idempotencyKey, string tipo, string bodyRequest, string bodyResponse)
            {
                var document = new RequisicoesBMPModel(idempotencyKey, tipo, bodyRequest, bodyResponse);

                centralizadorLogs.Add(document);
                return document.Id;
            }

            public string AdicionarLogRequisicoesFornecedor(string fornecedor, string tipo, string descricao, string bodyRequest, int statusCode, string bodyResponse)
            {
                var document = new RequisicoesFornecedorModel(fornecedor, tipo, descricao, bodyRequest, statusCode, bodyResponse);
                centralizadorLogs.Add(document);
                return document.Id;
            }

            public string AdicionarLogCobranca(string codigoCobranca, string texto)
            {
                var document = new LogCobrancaModel(codigoCobranca, texto, string.Empty, string.Empty, true);
                centralizadorLogs.Add(document);

                return document.Id;
            }

            public string AdicionarLogRequisicoesAPI(string requestIP, string requestUrl, string request, string method, string requestHeader)
            {
                RequisicoesAPIModel document = new RequisicoesAPIModel(requestUrl, method, requestHeader, request, requestIP);
                centralizadorLogs.Add(document);

                return document.Id;
            }
        }
    }
}