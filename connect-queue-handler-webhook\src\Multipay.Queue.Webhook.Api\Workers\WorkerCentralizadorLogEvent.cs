﻿using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;
using System.Diagnostics;

namespace Multipay.Queue.Webhook.Api.Workers
{
    public class WorkerCentralizadorLogEvent : BackgroundConsumerService<CentralizadorLogEvent>
    {
        private readonly ISalvarCentralizadorLogsService salvarCentralizadorLogsService;

        public WorkerCentralizadorLogEvent(
            ILogger<WorkerCentralizadorLogEvent> logger,
            ISalvarCentralizadorLogsService salvarCentralizadorLogsService,
            IBusConsumerCentralizadorLogEvent busConsumer) : base(logger, busConsumer)
        {
            this.salvarCentralizadorLogsService = salvarCentralizadorLogsService;
        }

        protected override async Task<bool> ProcessEventHandler(string mensagem, CentralizadorLogEvent @event, bool _)
        {
            Stopwatch stopwatch = new();
            stopwatch.Start();

            var result = await ProcessarMensagem(@event);

            stopwatch.Stop();
            TimeSpan elapsed = stopwatch.Elapsed;

            logger.LogInformation($"[CentralizadorLog]Elapsed time: {elapsed.TotalMilliseconds} ms");

            return result;
        }

        private async Task<bool> ProcessarMensagem(CentralizadorLogEvent @event)
        {
            var dicLog = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, dynamic[]>>(@event.SerializedLogs);

            return await salvarCentralizadorLogsService.SalvarAsync(dicLog);
        }
    }
}