﻿using Multipay.Queue.Webhook.Core.Interfaces;
using System.Globalization;

namespace Multipay.Queue.Webhook.Application.Models.AnspacePay.Indirect
{
    public class CobrancaRecebidaAnspacePayModel : IPIXData
    {
        public string EndToEndId { get; set; }
        public string? TransactionId { get; set; }
        public string IdempotencyKey { get; set; }
        public double Amount { get; set; }
        public string InitiationType { get; set; } = null!;
        public ParticipantData Payer { get; set; } = null!;
        public ParticipantData Beneficiary { get; set; } = null!;

        public string Txid => TransactionId!;

        public string Valor => Amount.ToString(CultureInfo.GetCultureInfo("pt-BR"));

        public string CpfPagador => Beneficiary.Holder.Document;

        public string NomePagador => Beneficiary.Holder.Name;
        public string? IspbPagador => Beneficiary.Participant?.Ispb;
        public string? NomeBancoPagador => Beneficiary.Participant?.Name;
        public string? AgenciaBancoPagador => Beneficiary.Branch;
        public string? ContaBancoPagador => Beneficiary.Number;
        public class ParticipantData
        {
            public string Number { get; set; } = null!;
            public string Branch { get; set; } = null!;
            public string Type { get; set; } = null!;
            public ParticipantBank Participant { get; set; } = null!;
            public HolderData Holder { get; set; } = null!;

            public class ParticipantBank
            {
                public string? Name { get; set; }
                public string Ispb { get; set; } = null!;
            }

            public class HolderData
            {
                public string Name { get; set; } = null!;
                public string Document { get; set; } = null!;
                public string Type { get; set; } = null!;
            }
        }
    }
}