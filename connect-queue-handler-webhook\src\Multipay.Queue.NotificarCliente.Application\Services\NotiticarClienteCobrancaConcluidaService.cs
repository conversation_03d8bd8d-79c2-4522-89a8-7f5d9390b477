﻿using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;
using System.Text;

namespace Multipay.Queue.NotificarCliente.Application.Services
{
    public interface INotiticarClienteCobrancaConcluidaService
    {
        Task<bool> EnviarNotificacao(CobrancaConfirmadaNotificarClienteEvent @event);
    }

    internal class NotiticarClienteCobrancaConcluidaService : INotiticarClienteCobrancaConcluidaService
    {
        private readonly IHttpClientFactory httpClientFactory;
        private readonly ICobrancaService cobrancaService;
        private readonly ICentralizadorLogsService centralizadorLogsService;

        public NotiticarClienteCobrancaConcluidaService(
            IHttpClientFactory httpClientFactory,
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService)
        {
            this.httpClientFactory = httpClientFactory;
            this.cobrancaService = cobrancaService;
            this.centralizadorLogsService = centralizadorLogsService;
        }

        public async Task<bool> EnviarNotificacao(CobrancaConfirmadaNotificarClienteEvent @event)
        {
            var cobranca = await cobrancaService.ObterCobranca(@event.TxId);

            if (cobranca == null)
            {
                return false;
            }

            if (string.IsNullOrEmpty(cobranca.UrlConfirmacao))
            {
                return true;
            }

            centralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Enviando para a fila a notificação de confirmação da cobrança", @event.IdWebhook, cobranca.Codigo);

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, cobranca.UrlConfirmacao)
            {
                Content = ObterBodyContent(cobranca)
            };

            if (!string.IsNullOrWhiteSpace(cobranca.RequestIdWebhook))
                httpRequestMessage.Headers.Add("X-Request-Id", cobranca.RequestIdWebhook);

            var httpClient = httpClientFactory.CreateClient();

            var httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);
            var isSuccessStatusCode = httpResponseMessage.IsSuccessStatusCode;

            centralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Enviado webhook ao cliente com " + (isSuccessStatusCode ? "sucesso." : "falha."));
            centralizadorLogsService.Salvar();

            return isSuccessStatusCode;
        }

        private static HttpContent ObterBodyContent(CobrancaModel cobranca)
        {
            var body = new
            {
                customId = cobranca.NumeroFatura,
                invoiceCode = cobranca.Codigo,
                id = cobranca.Guid.ToString(),
                paymentmethod = cobranca.FormaPagamento,
                installments = cobranca.Parcelas,
                paymentDate = cobranca.DataPagamento ?? DateTime.Now,
                total = Convert.ToInt64(cobranca.ValorLiquido * 100),
                totalPaid = Convert.ToInt64(cobranca.ValorPago ?? cobranca.ValorLiquido * 100),
                original_currency = cobranca.MoedaCobranca,
                payment_currency = cobranca.MoedaPagamento,
                status = InvoiceStatus.Paid.ToString(),
                paid_by_cripto_currency = cobranca.MoedaPagamento != "BRL" ? "true" : "false"
            };
            return new StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(body), Encoding.UTF8, System.Net.Mime.MediaTypeNames.Application.Json);
        }
    }
}