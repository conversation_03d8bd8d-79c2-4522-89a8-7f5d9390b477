﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Multipay.Queue.EnvioWebhookSaque.Core\Multipay.Queue.EnvioWebhookSaque.Core.csproj" />
    <ProjectReference Include="..\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj" />
    <ProjectReference Include="..\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj" />
    <ProjectReference Include="..\Multipay.Queue.EnvioWebhookSaque.Infrastructure\Multipay.Queue.EnvioWebhookSaque.Infrastructure.csproj" />
  </ItemGroup>

</Project>
