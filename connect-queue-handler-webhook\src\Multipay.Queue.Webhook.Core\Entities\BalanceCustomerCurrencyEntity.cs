﻿namespace Multipay.Queue.Webhook.Core.Entities;
public class BalanceCustomerCurrencyEntity
{
    public int CustomerId { get; set; }
    public int CoinId { get; set; }
    public decimal Balance { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public decimal? SpecialLimit { get; set; }

    public decimal GetDebitBalance(decimal value)
    {
        if (Balance < value)
            throw new Exception("InsufficientFunds");

        return Balance - value;
    }
}
