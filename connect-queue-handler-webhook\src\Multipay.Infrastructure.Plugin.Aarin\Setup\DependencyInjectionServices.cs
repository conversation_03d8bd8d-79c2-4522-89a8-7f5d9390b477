﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Multipay.Infrastructure.Plugin.Aarin.Abstractions;
using Multipay.Infrastructure.Plugin.Aarin.Services;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Service.Criptography; 
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace Multipay.Infrastructure.Plugin.Aarin.Setup
{
    public static class DependencyInjectionServices
    {
        public static void AddDependencyInjectionAarinServices(this IServiceCollection services, IConfiguration configuration)
        {

            services.AddHttpClient(nameof(TipoBanco.BancoAarin), (serviceProvider, client) =>
            {
                client.BaseAddress = GetUrl(configuration);
            })
                .ConfigurePrimaryHttpMessageHandler(serviceProvider =>
                {
                    var criptoService = serviceProvider.GetRequiredService<CriptoService>();
                    var handler = new HttpClientHandler();
                    handler.ClientCertificates.Add(GetCertificado(criptoService));
                    return handler;
                });

            services.AddSingleton<IAuthService, AuthService>();
            services.AddScoped<IEstornarQrCodeService, EstornarQrCodeService>();
        }

        public static X509Certificate2 GetCertificado(CriptoService criptoService)
        {
            var location = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, AppDomain.CurrentDomain.RelativeSearchPath ?? "");
            var pathCertificate = Path.Combine(location, "Utils", "aarin_homologacao.pfx");

            var certificatePassword = "ty3zNdMSQ2L7";
            //criptoService.GetSecret("Aarin-Certificate-Password-homologacao").Result;
            var binaryData = File.ReadAllBytes(pathCertificate);
            return new X509Certificate2(binaryData, certificatePassword);
            //return new X509Certificate2(pathCertificate, certificatePassword);
        }
        private static Uri GetUrl(IConfiguration configuration)
        {
            bool sandbox = false;

            var subdomain = "baas";
            if (sandbox)
            {
                subdomain += "-sandbox";
            }
            return new Uri($"https://{subdomain}.{configuration["Bancos:Aarin:Domain"]}");
        }
    }
}
