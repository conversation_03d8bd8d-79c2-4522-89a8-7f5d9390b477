﻿using Multipay.RabbitMQExtension.Bus.Interfaces;
using Multipay.RabbitMQExtension.Events;
using Serilog.Context;
using System.Diagnostics;

namespace Multipay.Queue.Webhook.Api.Workers;

public abstract class BackgroundConsumerService<T>(ILogger logger, IBusConsumer<T> busConsumer) : BackgroundService where T : IntegrationEvent
{
    protected readonly ILogger logger = logger;
    protected readonly IBusConsumer<T> busConsumer = busConsumer;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        busConsumer.ProcessEventHandler = async (mensagem, @event, retry) =>
        {
            LogContext.PushProperty("CorrelationID", Guid.NewGuid().ToString("N"));

            if (@event == null)
            {
                logger.LogWarning("Payload nulo. Mensagem: {mensagem}", mensagem);
                
                return true;
            }
            
            logger.LogInformation("Processando mensagem: {mensagem}", mensagem);
            
            Stopwatch stopwatch = Stopwatch.StartNew();

            try
            {
                return await ProcessEventHandler(mensagem, @event, retry);
            }
            finally
            {
                stopwatch.Stop();
                
                logger.LogInformation("Tempo processamento mensagem: {TotalMilliseconds} ms", stopwatch.ElapsedMilliseconds);
            }
        };
        
        var isConnected = busConsumer.Subscribe();

        while (isConnected && !stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(1000, stoppingToken);
        }
    }

    protected abstract Task<bool> ProcessEventHandler(string mensagem, T @event, bool retry);
}