﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Data;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Repositories
{
    public interface IConstantesRepository
    {
        Task<IEnumerable<ConstanteModel>> ListarConstantesPorTema(string tema);
    }

    internal class ConstantesRepository : IConstantesRepository
    {
        private readonly DbContext dbContext;

        public ConstantesRepository(DbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public async Task<IEnumerable<ConstanteModel>> ListarConstantesPorTema(string tema)
        {
            return await dbContext.CallStoredProcedureAsync<ConstanteModel>("[Manager].[Select_ConstantsByTema]", new SqlParameter { ParameterName = "@tema", Value = tema });
        }
    }
}