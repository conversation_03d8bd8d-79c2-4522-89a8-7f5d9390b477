using Microsoft.AspNetCore.SignalR;

namespace MultiPay.Webhook.Api.Hubs
{
    public class WebhookProgressHub : Hub
    {
        public async Task JoinGroup(string groupName)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        }

        public async Task LeaveGroup(string groupName)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        }
    }

    public class WebhookProgressModel
    {
        public string JobId { get; set; } = string.Empty;
        public int Total { get; set; }
        public int Processed { get; set; }
        public int Success { get; set; }
        public int Failed { get; set; }
        public decimal ProgressPercentage => Total > 0 ? (decimal)Processed / Total * 100 : 0;
        public string Status { get; set; } = "Running"; // Running, Completed, Failed
        public string? CurrentItem { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}
