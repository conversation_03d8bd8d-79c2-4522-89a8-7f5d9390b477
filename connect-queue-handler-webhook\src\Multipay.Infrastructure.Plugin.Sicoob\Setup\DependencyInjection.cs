﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Multipay.Infrastructure.Plugin.Sicoob.Core;
using Multipay.Infrastructure.Plugin.Sicoob.Handlers;
using Multipay.Infrastructure.Plugin.Sicoob.Services;
using Multipay.Service.Criptography;

namespace Multipay.Infrastructure.Plugin.Sicoob.Setup
{
    public static class DependencyInjectionServices
    {
        public static void AddDependencyInjectionSicoobServices(this IServiceCollection services)
        {
            var location = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, AppDomain.CurrentDomain.RelativeSearchPath ?? "");
            var jsonPath = Path.Combine(location, "appsettings.sicoob.json");

            var configuration = new ConfigurationBuilder().AddJsonFile(jsonPath).Build();

            services.AddMemoryCache();

            services.AddSingleton<IEnvironment>(ctx => new SicoobEnvironment(configuration));

            services.AddTransient<IAuthService, AuthService>();
            services.AddTransient<ICobrancaService, CobrancaService>();
            services.AddTransient<IPixService, PixService>();
            services.AddScoped<IAuthPagamentoService, AuthPagamentoService>();
            services.AddScoped<ValidateHeaderHandler>();

            services.AddHttpClient<IAuthPagamentoService, AuthPagamentoService>()
                .ConfigureCertificadoPagamento();

            services.AddHttpClient<IPagamentoPixService, PagamentoPixService>((serviceProvider, httpClient) =>
                {
                    var environment = serviceProvider.GetRequiredService<IEnvironment>();
                    httpClient.BaseAddress = new Uri(environment.PagamentoPixUrl);
                })
                .AddHttpMessageHandler<ValidateHeaderHandler>()
                .ConfigureCertificadoPagamento();
        }

        public static IHttpClientBuilder ConfigureCertificadoPagamento(this IHttpClientBuilder builder)
        {
            return builder.ConfigurePrimaryHttpMessageHandler(serviceProvider =>
            {
                var environment = serviceProvider.GetRequiredService<IEnvironment>();
                var criptoService = serviceProvider.GetRequiredService<CriptoService>();

                var httpClientHandler = new HttpClientHandler();
                httpClientHandler.ClientCertificates.Add(environment.GetCertificado(criptoService));

                return httpClientHandler;
            });
        }
    }
}