﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\createLogPath.sh" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\dd-dotnet.cmd" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\dd-dotnet.sh" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-arm64\Datadog.Linux.ApiWrapper.x64.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-arm64\Datadog.Profiler.Native.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-arm64\Datadog.Trace.ClrProfiler.Native.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-arm64\Datadog.Tracer.Native.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-arm64\dd-dotnet" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-arm64\libddwaf.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-arm64\loader.conf" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-musl-x64\Datadog.Linux.ApiWrapper.x64.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-musl-x64\Datadog.Profiler.Native.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-musl-x64\Datadog.Trace.ClrProfiler.Native.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-musl-x64\Datadog.Tracer.Native.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-musl-x64\dd-dotnet" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-musl-x64\libddwaf.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-musl-x64\loader.conf" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-x64\Datadog.Linux.ApiWrapper.x64.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-x64\Datadog.Profiler.Native.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-x64\Datadog.Trace.ClrProfiler.Native.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-x64\Datadog.Tracer.Native.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-x64\dd-dotnet" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-x64\libddwaf.so" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\linux-x64\loader.conf" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net461\Datadog.Trace.AspNet.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net461\Datadog.Trace.AspNet.pdb" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net461\Datadog.Trace.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net461\Datadog.Trace.MSBuild.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net461\Datadog.Trace.MSBuild.pdb" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net461\Datadog.Trace.pdb" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net6.0\Datadog.Trace.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net6.0\Datadog.Trace.MSBuild.deps.json" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net6.0\Datadog.Trace.MSBuild.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net6.0\Datadog.Trace.MSBuild.pdb" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\net6.0\Datadog.Trace.pdb" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netcoreapp3.1\Datadog.Trace.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netcoreapp3.1\Datadog.Trace.MSBuild.deps.json" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netcoreapp3.1\Datadog.Trace.MSBuild.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netcoreapp3.1\Datadog.Trace.MSBuild.pdb" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netcoreapp3.1\Datadog.Trace.pdb" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\Datadog.Trace.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\Datadog.Trace.MSBuild.deps.json" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\Datadog.Trace.MSBuild.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\Datadog.Trace.MSBuild.pdb" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\Datadog.Trace.pdb" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\System.Diagnostics.DiagnosticSource.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\System.Reflection.Emit.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\System.Reflection.Emit.ILGeneration.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\System.Reflection.Emit.Lightweight.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\netstandard2.0\System.Threading.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\osx\Datadog.Trace.ClrProfiler.Native.dylib" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\osx\Datadog.Tracer.Native.dylib" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\osx\libddwaf.dylib" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\osx\loader.conf" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x64\Datadog.Profiler.Native.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x64\Datadog.Trace.ClrProfiler.Native.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x64\Datadog.Tracer.Native.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x64\dd-dotnet.exe" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x64\ddwaf.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x64\loader.conf" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x86\Datadog.Profiler.Native.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x86\Datadog.Trace.ClrProfiler.Native.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x86\Datadog.Tracer.Native.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x86\ddwaf.dll" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\datadog.trace.bundle\2.53.2\contentFiles\any\any\datadog\win-x86\loader.conf" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Datadog.Trace" Version="2.53.2" />
    <PackageReference Include="Datadog.Trace.Bundle" Version="2.53.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Serilog" Version="4.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Exceptions.SqlServer" Version="8.4.0" />
    <PackageReference Include="Serilog.Sinks.Datadog.Logs" Version="0.5.2" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Map" Version="1.0.2" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.1" />
    <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageReference Include="SerilogAnalyzer" Version="0.15.0" />
  </ItemGroup>

</Project>
