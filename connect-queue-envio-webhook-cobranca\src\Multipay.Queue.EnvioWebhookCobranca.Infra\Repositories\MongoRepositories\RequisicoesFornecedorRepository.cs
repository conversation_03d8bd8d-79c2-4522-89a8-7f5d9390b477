﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Data;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Repositories.MongoRepositories
{
    public interface IRequisicoesFornecedorRepository : IBaseRepository<RequisicoesFornecedorModel>
    { }

    internal class RequisicoesFornecedorRepository : BaseRepository<RequisicoesFornecedorModel>, IRequisicoesFornecedorRepository
    {
        public RequisicoesFornecedorRepository(
            ILogger<RequisicoesFornecedorRepository> logger,
            AnspaceMongoContext context) : base(logger, context.RequisicoesFornecedor) { }
    }
}