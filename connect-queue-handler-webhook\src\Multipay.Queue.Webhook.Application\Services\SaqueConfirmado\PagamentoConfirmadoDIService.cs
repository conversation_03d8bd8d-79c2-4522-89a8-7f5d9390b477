﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.Saques;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Extensions;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado
{
    public interface IPagamentoConfirmadoDIService : IPagamentoConfirmadoService
    {
    }

    internal class PagamentoConfirmadoDIService : PagamentoConfirmadoBaseService<SolicitacaoSaqueRecebidoDIModel>, IPagamentoConfirmadoDIService
    {
        public PagamentoConfirmadoDIService(
            ILogger<PagamentoConfirmadoDIService> logger,
            ICentralizadorLogsService centralizadorLogsService,
            IEstornarSaqueService estornarSaqueService,
            IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IBrasilApiService brasilApiService,
            IUnitOfWork unitOfWork) : base(logger, centralizadorLogsService, busNotificarSaqueConfirmado, brasilApiService, estornarSaqueService, unitOfWork)
        {
        }

        protected override string NomeBanco => TipoBanco.BANCODI.GetDescription();

        protected override string ObterEndToEndId(SolicitacaoSaqueRecebidoDIModel parameters)
        {
            return parameters.WithdrawCode!;
        }

        protected override Status? ObterStatus(SolicitacaoSaqueRecebidoDIModel parameters)
        {
            if (string.IsNullOrWhiteSpace(parameters.UpdateCode))
                return Status.EFETIVADO;

            switch (parameters.UpdateCode)
            {
                case "01":
                    return Status.ERRO;

                case "02":
                    return Status.EXPIRADO;

                case "03":
                    return Status.REJEITADO;
            }
            return Status.ERRO;
        }
    }
}