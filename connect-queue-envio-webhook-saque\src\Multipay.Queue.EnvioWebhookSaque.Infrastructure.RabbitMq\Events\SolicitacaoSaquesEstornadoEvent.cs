﻿using Multipay.RabbitMQExtension.Events;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events
{
    public class SolicitacaoSaquesEstornadoEvent : IntegrationEvent
    {
        public string UrlAtualizacao { get; }
        public string CodigoSaque { get; }
        public string? CustomId { get; }
        public string? CustomUserId { get; }
        public Guid Guid { get; }
        public string? CodigoTransacao { get; }

        public SolicitacaoSaquesEstornadoEvent(string urlAtualizacao, string codigoSaque, string? customId, string? customUserId, Guid guid, string? codigoTransacao)
        {
            this.UrlAtualizacao = urlAtualizacao;
            this.CodigoSaque = codigoSaque;
            this.CodigoTransacao = codigoTransacao;
            this.CustomId = customId;
            this.CustomUserId = customUserId;
            this.Guid = guid;
        }
    }
}