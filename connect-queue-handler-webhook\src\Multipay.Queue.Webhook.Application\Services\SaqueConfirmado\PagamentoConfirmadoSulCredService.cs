﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.Saques;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Extensions;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;

public interface IPagamentoConfirmadoSulCredService : IPagamentoConfirmadoService
{
}

internal class PagamentoConfirmadoSulCredService : PagamentoConfirmadoBaseService<SolicitacaoSaqueRecebidoSulCredModel>,
    IPagamentoConfirmadoSulCredService
{
    private readonly ILogger<PagamentoConfirmadoSulCredService> logger;

    public PagamentoConfirmadoSulCredService(
        ILogger<PagamentoConfirmadoSulCredService> logger,
        IEstornarSaqueService estornarSaqueService,
        IBrasilApiService brasilApiService,
        ICentralizadorLogsService centralizadorLogsService, IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado, IUnitOfWork unitOfWork) :
        base(logger, centralizadorLogsService, busNotificarSaqueConfirmado, brasilApiService, estornarSaqueService, unitOfWork)
    {
        this.logger = logger;
    }

    protected override string NomeBanco => TipoBanco.SulCred.GetDescription();

    protected override string MsgRejeitado => "REJEITADO PELA INSTITUIÇÃO PAGADORA";

    protected override async Task ProcessarPagamentoComFalha(SolicitacaoSaqueRecebidoSulCredModel parameters, string idWebhook, ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string mensagem)
    {
        if (!string.IsNullOrWhiteSpace(parameters.Data?.Message))
            CentralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"[Sulcred] Motivo: {parameters.Data.Message}");

        await base.ProcessarPagamentoComFalha(parameters, idWebhook, solicitacaoSaque, mensagem);

        if (SeDevolvido(parameters))
            await base.MudarStatusSolicitacaoSaque(solicitacaoSaque.Id, (byte)9);//DEVOLVIDO
    }

    private bool SeDevolvido(SolicitacaoSaqueRecebidoSulCredModel parameters)
    {
        var status = ObterStatus(parameters);

        return status == Status.DEVOLVIDO;
    }

    protected override async Task ProcessarPagamentoEfetivado(SolicitacaoSaqueRecebidoSulCredModel parameters, string idWebhook,
        ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string endToEndId)
    {
        await base.ProcessarPagamentoEfetivado(parameters, idWebhook, solicitacaoSaque, endToEndId);

        await InserirRecebedorSolicitacaoSaque(solicitacaoSaque.Id,
            parameters.Data.CreditorAccount.Ispb,
            parameters.Data.CreditorAccount.Document,
            parameters.Data.CreditorAccount.Name,
            parameters.Data.CreditorAccount.Name,
            parameters.Data.CreditorAccount.Issuer,
            parameters.Data.CreditorAccount.Number,
            parameters.Data.CreditorAccount.AccountType.ToString());
    }

    protected override Status? ObterStatus(SolicitacaoSaqueRecebidoSulCredModel parameters)
    {
        return parameters.Status switch
        {
            StatusEnum.CANCELED => Status.ERRO,
            StatusEnum.LIQUIDATED => Status.EFETIVADO,
            StatusEnum.REFUNDED => Status.DEVOLVIDO,
            StatusEnum.PARTIALLY_REFUNDED => Status.DEVOLVIDO,
            StatusEnum.WAITING_SETTLEMENTCORE => null,
            StatusEnum.WAITING_CONFIRMATION => null,
            StatusEnum.PROCESSING => null,
            StatusEnum.ON_QUEUE => null,
            StatusEnum.REJECTED => Status.REJEITADO,
            _ => throw new ArgumentOutOfRangeException()
        };
    }

    protected override string ObterEndToEndId(SolicitacaoSaqueRecebidoSulCredModel parameters) =>
        parameters.Data.EndToEndId;
}