﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.Aarin;
using Multipay.Queue.Webhook.Application.Models.Genial;
using Multipay.Queue.Webhook.Application.Services.Aarin;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services
{
    public class WebhookRecebidoGenialService : IWebhookRecebidoService
    {
        private readonly ILogger<WebhookRecebidoGenialService> logger;

        public TipoBanco TipoBanco => TipoBanco.Genial;

        public WebhookRecebidoGenialService(ILogger<WebhookRecebidoGenialService> logger)
        {
            this.logger = logger;
        }

        public async Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
        {
            if (@event.Tipo == "pix/recebimento")
            {
                var service = serviceProvider.GetRequiredService<CobrancaRecebidaGenialService>();
                return await service.Processar($"{@event.Data}", @event.IdWebhook);
            }
            if (@event.Tipo == "pix/pagamento")
            {
                var service = serviceProvider.GetRequiredService<IPagamentoConfirmadoGenialService>();
                return await service.ProcessarPagamento(@event);
            }
            if (@event.Tipo == "pix/recebimento/devolucao")
            {
                this.logger.LogInformation("[{0}] Iniciando PIX devolucao, Tipo: {1}", nameof(PixReversalService), @event.Tipo);

                var service = serviceProvider.GetRequiredService<IPixReversalService>();
                return await service.ProcessReversalPixAsync<ReversalPixGenialModel>(@event, new BehaviorReversalPixModel
                {
                    CheckCorrelationId = true,
                    CheckStatus = true,
                    CheckValue = true,
                    StatusAttributeName = "Status",
                    CompletedStatusName = "Completed",
                    CorrelationIdAttributeName = "InstantPaymentId",
                    FinancialInstitution = "Genial",
                    OriginIp = "",
                    ValueAttributeName = "Value"
                }, default);
            }

            logger.LogWarning($"Mensagem não tratada. {@event}");

            return await Task.FromResult(true);
        }
    }
}