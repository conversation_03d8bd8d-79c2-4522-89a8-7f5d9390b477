﻿namespace Multipay.Queue.Webhook.Core.Interfaces
{
    public interface IPIXData
    {
        string Txid { get; }
        string Valor { get; }
        string CpfPagador { get; }
        string NomePagador { get; }
        string EndToEndId { get; }
        string? IspbPagador { get; }
        string? NomeBancoPagador { get; }
        string? AgenciaBancoPagador { get; }
        string? ContaBancoPagador { get; }
    }
}