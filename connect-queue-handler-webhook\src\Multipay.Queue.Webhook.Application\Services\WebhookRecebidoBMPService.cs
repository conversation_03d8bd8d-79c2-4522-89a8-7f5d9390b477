﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.BMP;
using Multipay.Queue.Webhook.Application.Services.BMP;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.Emails;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services
{
    public class WebhookRecebidoBMPService : IWebhookRecebidoService
    {
        private readonly ILogger<WebhookRecebidoBMPService> logger;
        private readonly IMovimentacaoEntradaService movimentacaoEntradaService;
        private readonly IMovimentacaoSaidaService movimentacaoSaidaService;
        private readonly IEmailHandler _emailHandler;

        public TipoBanco TipoBanco => TipoBanco.BMP;

        public WebhookRecebidoBMPService(
            ILogger<WebhookRecebidoBMPService> logger,
            IMovimentacaoEntradaService movimentacaoEntradaService,
            IMovimentacaoSaidaService movimentacaoSaidaService,
            IEmailHandler emailHandler)
        {
            this.logger = logger;
            this.movimentacaoEntradaService = movimentacaoEntradaService;
            this.movimentacaoSaidaService = movimentacaoSaidaService;
            this._emailHandler = emailHandler;
        }

        public async Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
        {
            var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<CallbackRecebidoModel>($"{@event.Data}");

            if (parameters == null)
                return false;

            try
            {
                return await ProcessarCallback(serviceProvider, @event.IdWebhook, parameters);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Falha ao processar mensagem: {@event}", @event);
                return false;
            }
        }

        private async Task<bool> ProcessarCallback(IServiceProvider serviceProvider, string idWebhook, CallbackRecebidoModel parameters)
        {
            if (parameters.VerificarEstornoPix())
            {
                return await movimentacaoEntradaService.ProcessarDevolucaoPIX(parameters);
            }
            var operacao = parameters.ObterOperacao();

            switch (operacao)
            {
                case "ENVIOTRANSF":
                    return await movimentacaoSaidaService.ProcessarTransferenciaEntreContas(idWebhook, parameters);

                case "ENVIOTED":
                    return await movimentacaoSaidaService.ProcessarEnvioTED(idWebhook, parameters);

                case "ENVIOPIX":
                    return await movimentacaoSaidaService.ProcessarEnvioPIX(idWebhook, parameters);

                case "RECEBEPIX":
                    return await movimentacaoEntradaService.ProcessarRecebimentoPIX(parameters, idWebhook);

                case "RECEBTED":
                    return await movimentacaoEntradaService.ProcessarRecebimentoTED(parameters);

                case "DEVOLUCAOTED":
                    return await movimentacaoEntradaService.ProcessarDevolucaoTED(parameters);
            }

            logger.LogWarning($"Mensagem não tratada: {idWebhook}. Operação não tratada: {operacao}.");

            return true;
        }
    }
}