using Amazon.Runtime;
using Application.Services.Interfaces;
using Domain.Constants;
using Domain.Models;
using Infra.Repositories.Interfaces;
using Microsoft.Extensions.Logging;
using ResultT;
using static ResultT.Results;

namespace Application.Services.Implementation.Authentication;

public class CredentialService(ILogger<CredentialService> logger, IIntegrationRepository integrationRepository) : ICredentialService
{
    public async Task<Result<CustomerCredential>> GetCredentialByCustomerId(int customerId, CancellationToken cancellationToken)
    {
        var customerApplication = await integrationRepository.GetApplicationByCustmerId(customerId, cancellationToken);
        if (customerApplication is null)
        {
            logger.Error("Nenhuma configuração de aplicação encontrada para o cliente Id: {CustomerId}", customerId);
            return Error(MessagesConstants.ApplicationNotFound);
        }

        var customerToken = await integrationRepository.GetTokenCryptographyByCustmerId(customerId, cancellationToken);
        if (customerToken is null)
        {
            logger.Error("Nenhuma Token encontrado para o cliente Id: {CustomerId}", customerId);
            return Error(MessagesConstants.TokenNotFound);
        }                        
        
        var credential = new CustomerCredential(customerApplication, customerToken);

        return Ok(credential);
    }

    public async Task<Result<List<CustomerCredential>>> AllGetCredential(CancellationToken cancellationToken)
    {
        var customerApplication = await integrationRepository.AllGetApplication(cancellationToken);

        var credentials = new List<CustomerCredential>();

        foreach (var customer in customerApplication)
        {
            var customerToken = await integrationRepository.GetTokenCryptographyByCustmerId(customer.CustomerId ,cancellationToken);
            credentials.Add(new CustomerCredential(customer, customerToken));
        }


        return Ok(credentials);
    }
}
