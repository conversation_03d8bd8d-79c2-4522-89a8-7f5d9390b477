using Connect.Integration.Bank.Services.Common.Models.Requests;
using Connect.Integration.Bank.Strategy.Interfaces;
using Connect.Integration.Bank.Tests.Fixtures;
using FluentAssertions;

namespace Connect.Integration.Bank.Tests.Services.Common;

public class ConsultPaymentServiceTests : IClassFixture<ServicesFixture>
{
    private readonly IIntegrationBankService _sut;
    private readonly ServicesFixture _fixture;

    public ConsultPaymentServiceTests(ServicesFixture fixture)
    {
        _fixture = fixture;
        _sut = fixture.IntegrationBankService;
    }

    [Fact]
    public async Task ShouldExecuteDelBankSuccess()
    {
        // Arrange        
        var endToEndId = "E3822485720250101032927208195477";
        var credential = _fixture.CredentialDelBankAccount;

        var request = new ConsultPaymentRequest(credential, endToEndId);

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();
        response.Result?.EndToEndId.Should().Be(request.EndToEndId);
    }

    [Fact]
    public async Task ShouldExecuteCelcoinSuccess()
    {
        // Arrange        
        var endToEndId = "E13935893202501082047XiaDgJ6OVua";
        var credential = _fixture.CredentialCelcoinAccount;

        var request = new ConsultPaymentRequest(credential, endToEndId);

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();
        response.Result?.EndToEndId.Should().Be(request.EndToEndId);
    }
}
