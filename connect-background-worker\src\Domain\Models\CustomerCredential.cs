using System.Security.Cryptography;
using Domain.Entities;

namespace Domain.Models;

public class CustomerCredential
{
    public string UserName { get; }
    public string Password { get; }
    public string ApplicationToken { get; }
    public string Token { get; }
    public int CustomerId { get; set; }

    public CustomerCredential(string userName, string password, string applicationToken, string token , int customerId = 0)
    {
        UserName = userName;
        Password = password;        
        Token = token;
        ApplicationToken = applicationToken;
        CustomerId = customerId;
    }

    public CustomerCredential(ApplicationEntity application, TokenCryptographyCustomerEntity tokenCryptography)
    {
        UserName = application.Terminal!.TerminalNumber!;
        Password = application.Customer!.Guid.ToString();
        ApplicationToken = application.Guid.ToString();
        Token = tokenCryptography.Token.ToString();
        CustomerId = application.CustomerId;
    }
    
    public string GetDigitalSignature(string accessToken)
    {        
        var encoding = new System.Text.ASCIIEncoding();        

        var messageBytes = encoding.GetBytes(accessToken);
        var keyBytes = encoding.GetBytes(Token.ToUpper());

        using var hmacsha256 = new HMACSHA256(keyBytes);
        var hashMessage = hmacsha256.ComputeHash(messageBytes);

        var result = Convert.ToHexString(hashMessage).ToLower();

        return result;
    }
}
