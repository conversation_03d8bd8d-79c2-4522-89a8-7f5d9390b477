﻿using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels
{
    [BsonIgnoreExtraElements]
    public class LogSolicitacaoSaqueModel : BaseModel
    {
        public LogSolicitacaoSaqueModel(int idSolicitacaoSaque, string texto)
        {
            IdSolicitacaoSaque = idSolicitacaoSaque;
            Texto = texto;
            DataCriacaoUtc = DateTime.UtcNow;
        }

        public int IdSolicitacaoSaque { get; private set; }
        public string Texto { get; private set; }
        public DateTime DataCriacaoUtc { get; private set; }
        public DateTime? DataAtualizacaoUtc { get; private set; }
    }
}