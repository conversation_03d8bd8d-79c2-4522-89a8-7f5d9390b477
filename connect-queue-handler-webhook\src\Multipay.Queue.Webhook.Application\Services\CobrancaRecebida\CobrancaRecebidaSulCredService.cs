﻿using Multipay.Queue.Webhook.Application.Models;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaSulCredService : CobrancaRecebidaService<CobrancaRecebidaSulCredModel>
    {
        public CobrancaRecebidaSulCredService(
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.SulCred)
        {
        }

        protected override IEnumerable<CobrancaRecebidaSulCredModel> ListPix => new List<CobrancaRecebidaSulCredModel>()
        {
            Parameters!
        };

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);
    }
}