using System.Text.Json.Serialization;
using System;
using System.Collections.Generic;

namespace Application.Services.Models.Responses;
public class BankStatement
{
    [JsonPropertyName("id")]
    public string Id { get; set; }

    [JsonPropertyName("nsu")]
    public int Nsu { get; set; }

    [JsonPropertyName("amount")]
    public double Amount { get; set; }

    [JsonPropertyName("notes")]
    public string Notes { get; set; }

    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("type")]
    public TypeModel Type { get; set; }

    [JsonPropertyName("balance")]
    public BalanceModel Balance { get; set; }

    [JsonPropertyName("proof")]
    public ProofModel Proof { get; set; }

    public class BalanceModel
    {
        [JsonPropertyName("balancePrevious")]
        public double BalancePrevious { get; set; }

        [JsonPropertyName("currentBalance")]
        public double CurrentBalance { get; set; }
    }

    public class TypeModel
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("isCredit")]
        public bool IsCredit { get; set; }
    }

    public class ProofModel
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("endToEndId")]
        public string EndToEndId { get; set; }
        

        [JsonPropertyName("externalId")]
        public string ExternalId { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("amount")]
        public double Amount { get; set; }

        [JsonPropertyName("createdAt")]
        public DateTime CreatedAt { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("payer")]
        public Payer Payer { get; set; }

        [JsonPropertyName("beneficiary")]
        public Beneficiary Beneficiary { get; set; }
    }

    public class Payer
    {
        [JsonPropertyName("number")]
        public string Number { get; set; }

        [JsonPropertyName("branch")]
        public string Branch { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("holder")]
        public Holder Holder { get; set; }

        [JsonPropertyName("participant")]
        public Participant Participant { get; set; }
    }

    public class Beneficiary
    {
        [JsonPropertyName("number")]
        public string Number { get; set; }

        [JsonPropertyName("branch")]
        public string Branch { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("holder")]
        public Holder Holder { get; set; }

        [JsonPropertyName("participant")]
        public Participant Participant { get; set; }
    }

    public class Holder
    {
        [JsonPropertyName("document")]
        public string Document { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }
    }

    public class Participant
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("ispb")]
        public string Ispb { get; set; }
    }
}