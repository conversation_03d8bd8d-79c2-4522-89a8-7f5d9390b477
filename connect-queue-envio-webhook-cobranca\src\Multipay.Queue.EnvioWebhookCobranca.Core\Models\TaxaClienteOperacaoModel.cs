﻿namespace Multipay.Queue.EnvioWebhookCobranca.Core.Models
{
    public class TaxaClienteOperacaoModel
    {
        public decimal TaxaAVista { get; set; }
        public decimal TarifaAVista { get; set; }
        public decimal TaxaParcelado2x6 { get; set; }
        public decimal TaxaParcelado7x12 { get; set; }
        public decimal TarifaParcelado2x6 { get; set; }
        public decimal TarifaParcelado7x12 { get; set; }
        public int HorasResgate { get; set; }

        public decimal ObterPorcentagemTaxa(int numeroParcelas)
        {
            return numeroParcelas == 1 ? TaxaAVista : numeroParcelas is > 1 and <= 6 ? TaxaParcelado2x6 : TaxaParcelado7x12;
        }

        public decimal ObterValorTarifa(int numeroParcelas)
        {
            return numeroParcelas == 1 ? TarifaAVista : numeroParcelas is > 1 and <= 6 ? TarifaParcelado2x6 : TarifaParcelado7x12;
        }
    }
}