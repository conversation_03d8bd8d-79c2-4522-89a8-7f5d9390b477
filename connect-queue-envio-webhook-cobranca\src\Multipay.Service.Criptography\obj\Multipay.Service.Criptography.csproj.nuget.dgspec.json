{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj", "projectName": "Multipay.Service.Criptography", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-cobranca\\src\\Multipay.Service.Criptography\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"AWSSDK.SecretsManager": {"target": "Package", "version": "[3.7.304.6, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}