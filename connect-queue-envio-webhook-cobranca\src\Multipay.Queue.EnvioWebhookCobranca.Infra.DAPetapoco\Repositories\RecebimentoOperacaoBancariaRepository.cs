﻿using Microsoft.Extensions.Logging;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    public interface IRecebimentoOperacaoBancariaRepository
    {
        Task<int> Inserir(byte idTipoOperacaoBancaria, string? identificador, string? nomePagador, string? documentoPagador, string? ISPBPagador, string? chaveRecebedor, string? codigoMovimento, string? codigoMovimentoOrigem, string? codigoOperacaoCliente, DateTime dataCadastro, DateTime? dataTransacao, string? descricaoCliente, byte idTipoMovimentoContabil, string? informacoesAdicionais, string? motivoEstorno, string? nSU, double valorTransacao, int? idCliente, int? idContaBancaria);

        Task<bool> VerificarCodigoMovimentoExiste(string codigoMovimento);
    }

    internal class RecebimentoOperacaoBancariaRepository : IRecebimentoOperacaoBancariaRepository
    {
        private readonly ILogger logger;
        private readonly IDatabase database;

        public RecebimentoOperacaoBancariaRepository(
            ILogger logger,
            IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public async Task<int> Inserir(
            byte idTipoOperacaoBancaria,
            string? identificador, string? nomePagador, string? documentoPagador,
            string? ISPBPagador, string? chaveRecebedor, string? codigoMovimento,
            string? codigoMovimentoOrigem, string? codigoOperacaoCliente,
            DateTime dataCadastro, DateTime? dataTransacao, string? descricaoCliente,
            byte idTipoMovimentoContabil, string? informacoesAdicionais,
            string? motivoEstorno, string? nSU, double valorTransacao, int? idCliente, int? idContaBancaria)
        {
            try
            {
                using var reader = await database.QueryProcAsync<int>("Painel.Insert_RecebimentoOperacaoBancaria", new
                {
                    idTipoOperacaoBancaria,
                    identificador,
                    nomePagador,
                    documentoPagador,
                    ISPBPagador,
                    chaveRecebedor,
                    codigoMovimento,
                    codigoMovimentoOrigem,
                    codigoOperacaoCliente,
                    dataTransacao,
                    descricaoCliente,
                    informacoesAdicionais,
                    motivoEstorno,
                    valorTransacao,
                    idTipoMovimentoContabil,
                    idCliente,
                    idContaBancaria
                });
                await reader.ReadAsync();
                return reader.Poco;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(RecebimentoOperacaoBancariaRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<bool> VerificarCodigoMovimentoExiste(string codigoMovimento)
        {
            try
            {
                using var reader = await database.QueryProcAsync<dynamic>("Painel.Select_RecebimentoOperacaoBancariaPorCodigoMovimento", new
                {
                    codigoMovimento
                });

                return await reader.ReadAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(RecebimentoOperacaoBancariaRepository), database.LastCommand);
                throw;
            }
        }
    }
}