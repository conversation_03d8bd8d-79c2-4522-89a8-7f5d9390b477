﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Sicoob.Core;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Responses;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.Repositories;
using Multipay.Service.Criptography;
using System.Net;
using System.Runtime.CompilerServices;

namespace Multipay.Infrastructure.Plugin.Sicoob.Services
{
    internal class BaseConsumoService : BaseService
    {
        private readonly ILogger logger;
        private readonly IErroRepository erroRepository;
        private readonly ICentralizadorLogsService centralizadorLogsService;
        private readonly IAuthService authService;

        public BaseConsumoService(
            ILogger logger,
            IEnvironment environment,
            IErroRepository erroRepository,
            ICentralizadorLogsService centralizadorLogsService,
            IAuthService authService, CriptoService criptoService) : base(environment, criptoService)
        {
            this.logger = logger;
            this.erroRepository = erroRepository;
            this.centralizadorLogsService = centralizadorLogsService;
            this.authService = authService;
        }

        protected async Task<ApiResponse> GetAsync<TModel, TResponseModel>(
            string url,
            string clientId,
            TModel model,
            string descricaoServico,
            [CallerMemberName] string callerName = "")
        {
            try
            {
                using (var _client = await CreateHttpClientConsumo(clientId, authService))
                {
                    using (var response = await _client.GetAsync(url))
                    {
                        logger.LogInformation("Status: {status}, {response}",
                            response.StatusCode,
                            await response.Content.ReadAsStringAsync());

                        await response.EnsureSuccessStatusCodeAsync();
                        var returnObj = GetObject<TResponseModel>(response);

                        centralizadorLogsService.AdicionarLogRequisicoesFornecedor("Sicoob",
                            $"[GET]{descricaoServico}",
                            $"{GetType().Name}.{callerName}",
                            Newtonsoft.Json.JsonConvert.SerializeObject(model),
                            (int)response.StatusCode,
                            Newtonsoft.Json.JsonConvert.SerializeObject(returnObj));

                        return new ApiResponse(true, response.StatusCode, returnObj);
                    }
                }
            }
            catch (HttpResponseException ex)
            {
                await erroRepository.InsertErro(GetType().Name, $"{callerName}", $"{GetType().FullName}.{callerName}", ex.Message, ex.StackTrace ?? string.Empty, ex.ToString());

                return new ApiResponse(false, ex.StatusCode, ex.Response);
            }
            catch (Exception exc)
            {
                await erroRepository.InsertErro(GetType().Name, $"{callerName}", $"{GetType().FullName}.{callerName}", exc.Message, exc.StackTrace ?? string.Empty, exc.ToString());

                return new ApiResponse(false, HttpStatusCode.InternalServerError, exc.ToString());
            }
        }

        protected async Task<ApiResponse> PutAsync<TModel, TResponseModel>(
            string url,
            string clientId,
            TModel model,
            string descricaoServico,
            [CallerMemberName] string callerName = "")
        {
            try
            {
                using (var _client = await CreateHttpClientConsumo(clientId, authService))
                {
                    var content = BuildHttpContent(model);

                    using (var response = await _client.PutAsync(url, content))
                    {
                        logger.LogInformation("{url}, {request}",
                            response.RequestMessage!.RequestUri!.AbsoluteUri,
                            await response.RequestMessage!.Content!.ReadAsStringAsync());

                        logger.LogInformation("Status: {status}, {response}",
                            response.StatusCode,
                            await response.Content.ReadAsStringAsync());

                        centralizadorLogsService.AdicionarLogRequisicoesFornecedor(
                            "Sicoob",
                            $"[PUT]{descricaoServico}",
                            $"{GetType().Name}.{callerName}",
                            Newtonsoft.Json.JsonConvert.SerializeObject(model),
                            (int)response.StatusCode,
                            await response.Content.ReadAsStringAsync());

                        await response.EnsureSuccessStatusCodeAsync();
                        var returnObj = GetObject<TResponseModel>(response);

                        return new ApiResponse(true, response.StatusCode, returnObj);
                    }
                }
            }
            catch (HttpResponseException ex)
            {
                await erroRepository.InsertErro(GetType().Name, $"{callerName}", $"{GetType().FullName}.{callerName}", ex.Message, ex.StackTrace ?? string.Empty, ex.ToString());

                return new ApiResponse(false, ex.StatusCode, ex.Response);
            }
            catch (Exception exc)
            {
                await erroRepository.InsertErro(GetType().Name, $"{callerName}", $"{GetType().FullName}.{callerName}", exc.Message, exc.StackTrace ?? string.Empty, exc.ToString());

                return new ApiResponse(false, HttpStatusCode.InternalServerError, exc.ToString());
            }
        }

        protected async Task<ApiResponse> PostAsync<TModel, TResponseModel>(
            string url,
            string clientId,
            TModel model,
            string descricaoServico,
            [CallerMemberName] string callerName = "")
        {
            try
            {
                using (var _client = await CreateHttpClientConsumo(clientId, authService))
                {
                    var content = BuildHttpContent(model);

                    using (var response = await _client.PostAsync(url, content))
                    {
                        logger.LogInformation("{url}, {request}",
                            response.RequestMessage!.RequestUri!.AbsoluteUri,
                            await response.RequestMessage!.Content!.ReadAsStringAsync());

                        logger.LogInformation("Status: {status}, {response}",
                            response.StatusCode,
                            await response.Content.ReadAsStringAsync());

                        await response.EnsureSuccessStatusCodeAsync();
                        var returnObj = GetObject<TResponseModel>(response);

                        centralizadorLogsService.AdicionarLogRequisicoesFornecedor(
                            "Sicoob",
                            $"[POST]{descricaoServico}",
                            $"{GetType().Name}.{callerName}",
                            Newtonsoft.Json.JsonConvert.SerializeObject(model),
                            (int)response.StatusCode,
                            Newtonsoft.Json.JsonConvert.SerializeObject(returnObj));

                        return new ApiResponse(true, response.StatusCode, returnObj);
                    }
                }
            }
            catch (HttpResponseException ex)
            {
                await erroRepository.InsertErro(GetType().Name, $"{callerName}", $"{GetType().FullName}.{callerName}", ex.Message, ex.StackTrace ?? string.Empty, ex.ToString());

                return new ApiResponse(false, ex.StatusCode, ex.Response);
            }
            catch (Exception exc)
            {
                await erroRepository.InsertErro(GetType().Name, $"{callerName}", $"{GetType().FullName}.{callerName}", exc.Message, exc.StackTrace ?? string.Empty, exc.ToString());

                return new ApiResponse(false, HttpStatusCode.InternalServerError, exc.ToString());
            }
        }
    }
}