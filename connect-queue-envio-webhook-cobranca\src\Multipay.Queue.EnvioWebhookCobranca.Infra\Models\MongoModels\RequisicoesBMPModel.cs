﻿using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels
{
    [BsonIgnoreExtraElements]
    public class RequisicoesBMPModel : BaseModel
    {
        public RequisicoesBMPModel(string idempotencyKey, string tipo, string bodyRequest, string bodyResponse)
        {
            IdempotencyKey = idempotencyKey;
            Tipo = tipo;
            BodyRequest = bodyRequest;
            BodyResponse = bodyResponse;
            DataCriacaoUtc = DateTime.UtcNow;
        }

        public string IdempotencyKey { get; private set; }
        public string Tipo { get; private set; }
        public string BodyRequest { get; private set; }
        public string BodyResponse { get; private set; }
        public DateTime DataCriacaoUtc { get; private set; }
        public DateTime? DataAtualizacaoUtc { get; set; }
        //public DateTime DataRequisicao { get; set; }
        //public DateTime? DataValidacaoHandShake { get; set; }

        public override Dictionary<string, object?> GetUpdateDictionary()
        {
            return new()
            {
                { nameof(DataAtualizacaoUtc), DataAtualizacaoUtc }
            };
        }

        public void SetResponseData(string bodyResponse)
        {
            BodyResponse = bodyResponse;
        }
    }
}