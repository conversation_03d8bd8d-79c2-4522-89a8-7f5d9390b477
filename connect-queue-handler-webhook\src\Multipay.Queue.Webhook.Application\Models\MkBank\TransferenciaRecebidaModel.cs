﻿using Multipay.Queue.Webhook.Application.Services.ContaGrafica;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Models.MkBank
{
    internal class TransferenciaRecebidaModel : IMovimentacaoSaidaModel
    {
        public string? CodigoSolicitacaoSaque => Owner?.CorrelationId;

        public string CodigoAutenticacao => Owner?.AuthenticationCode ?? string.Empty;

        public string NSU => $"{Id}";

        public string ObterOperacao() => "ENVIOTRANSF";

        public int Id { get; set; } = 0;
        public OwnerData? Owner { get; set; }

        public class OwnerData
        {
            public string? Status { get; set; }

            [JsonProperty("authentication_code")]
            public string? AuthenticationCode { get; set; }

            [JsonProperty("correlation_id")]
            public string? CorrelationId { get; set; }

            [JsonProperty("end_to_end_id")]
            public string? EndToEndId { get; set; }

            [JsonProperty("original_end_to_end_id")]
            public string? OriginalEndToEndId { get; set; }

            [JsonProperty("devolution_code")]
            public string? DevolutionCode { get; set; }
        }
    }
}