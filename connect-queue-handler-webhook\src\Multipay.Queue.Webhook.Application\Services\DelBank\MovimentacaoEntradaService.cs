﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.DelBank;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Emails;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Serilog.Context;
using static Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events.NotificarClienteEvent;
using static Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events.NotificarClienteEvent.BodyData;

namespace Multipay.Queue.Webhook.Application.Services.DelBank
{
    public interface IMovimentacaoEntradaService
    {
        Task<bool> ProcessarRecebimentoPIX(string sourceParameters, string idWebhook);
    }

    internal class MovimentacaoEntradaService : IMovimentacaoEntradaService
    {
        private readonly IUnitOfWork unitOfWork;
        private readonly CobrancaRecebidaService cobrancaRecebidaService;
        private readonly IEstornarSaqueService estornarSaqueService;
        private readonly IBusNotificarCliente busNotificarCliente;
        private readonly IEmailHandler emailHandler;
        private readonly ICentralizadorLogsService centralizadorLogsService;
        private readonly ILogger<MovimentacaoEntradaService> _logger;

        public MovimentacaoEntradaService(
            IUnitOfWork unitOfWork,
            CobrancaRecebidaService cobrancaRecebidaService,
            IEstornarSaqueService estornarSaqueService,
            IBusNotificarCliente busNotificarCliente,
            IEmailHandler emailHandler,
            ICentralizadorLogsService centralizadorLogsService,
            ILogger<MovimentacaoEntradaService> logger)
        {
            this.unitOfWork = unitOfWork;
            this.cobrancaRecebidaService = cobrancaRecebidaService;
            this.estornarSaqueService = estornarSaqueService;
            this.busNotificarCliente = busNotificarCliente;
            this.emailHandler = emailHandler;
            this.centralizadorLogsService = centralizadorLogsService;
            _logger = logger;
        }

        private static TipoBanco TipoBanco => TipoBanco.DelBank;

        public async Task<bool> ProcessarRecebimentoPIX(string sourceParameters, string idWebhook)
        {
            var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<CobrancaRecebidaModel>(sourceParameters)!;

            var result = await ProcessarCreditoContaCliente(parameters, TipoOperacaoBancaria.PIX, OperacaoServico.RecebimentoPIX, parameters.Type != "CREDIT_PIX_REFUND" ? "RECEBE PIX" : "DEVOLUÇÃO PIX");

            if (string.IsNullOrWhiteSpace(parameters.CorrelationId))
            {
                LogContext.PushProperty("ProcessarCreditoContaCliente", result, true);

                _logger.LogWarning("[{Classe}] ProcessarCreditoContaCliente Result", "Webhook Delbank");

                return result;
            }

            return await cobrancaRecebidaService.Processar(sourceParameters, idWebhook);
        }

        private async Task<bool> ProcessarCreditoContaCliente(CobrancaRecebidaModel parameters, byte tipoOperacaoBancaria, byte operacaoServico, string descServico)
        {
            if (string.IsNullOrWhiteSpace(parameters.EndToEndId))
                return true;

            var verificarRecebimentoPIX = await unitOfWork.RecebimentoOperacaoBancariaRepository.VerificarCodigoMovimentoExiste(parameters.EndToEndId);
            
            if (verificarRecebimentoPIX)
                return true;

            if (parameters is { Type: "PIX_ADMINISTRATIVE_RETURN" or "CREDIT_PIX_UNDONE" })
            {
                await estornarSaqueService.EstornarSaque(parameters.Proof.EndToEndId, parameters.Proof.Description ?? $"Webhook recebido delbank com status: {parameters.Type}");

                return true;
            }

            try
            {
                var depositoManual = await unitOfWork.ContaBancariaRepository.BuscaDepositoManual(parameters.EndToEndId);
                
                if (depositoManual.Status != null)
                {
                    _logger.LogInformation("[{Evento}] - Deposito já lançado anteriormente. E2E: {E2E}", "EV2006", parameters.EndToEndId);

                    return true;
                }

                if (parameters.Proof.type != null)
                {
                    _logger.LogInformation("[{Evento}] - Verificando se é transferencia do tipo: {Tipo}", "EV2001", parameters.Proof.type);
                }
                
                if (parameters.Proof.type == "PIX_KEY")
                {
                    _logger.LogInformation("[{Evento}] - Dados de Agencia: {Agencia}, Conta: {Conta}, Documento: {Documento}, Nome: {Nome}", "EV2001",
                        parameters.Proof.Payer.Branch, parameters.Proof.Payer.Number, parameters.Proof.Payer.Holder.Document, parameters.Proof.Payer.Holder.Name);

                    //verifica se e transferencia por pix de  uma conta nossa ( REBALANCE )
                    var verificaContaInterna = await unitOfWork.ContaBancariaRepository.BuscaClientePorAgenciaContaBancariaEmpresa(parameters.Proof.Payer.Branch, parameters.Proof.Payer.Number);
                    _logger.LogInformation("[{Evento}] - Verificando se é transferencia de movimentação entre contas internas", "EV2001");

                    //verifica conta interna - Se for, não credita
                    if (verificaContaInterna.IdCliente != null)
                    {
                        _logger.LogInformation("[{Evento}] - Identificado movimentação entre contas internas. Nada será creditado.", "EV2001");
                        
                        return true;
                    }

                    _logger.LogInformation("[{Evento}] - Não identificado movimentação entre contas internas.", "EV2001");

                    _logger.LogInformation("[{Evento}] - Buscando cliente.", "EV2001");

                    var idContaIdCliente = await unitOfWork.ContaBancariaRepository.BuscaClientePorAgenciaContaBancariaEmpresa(parameters.Proof.Beneficiary.Branch, parameters.Proof.Beneficiary.Number);

                    _logger.LogInformation("[{Evento}] - Conta afetada {IdContaBancariaEmpresa} do idCliente {IdCliente}.", "EV2001", idContaIdCliente.IdContaBancariaEmpresa, idContaIdCliente.IdCliente);

                    //verifica destinatatio é nosso, se sim, credita.
                    if (idContaIdCliente.IdCliente == null)
                    {
                        _logger.LogInformation("[{Evento}] - A conta não é gerenciada, nada creditado.","EV2001");
                        return true;
                    }

                    // acionar o credito 
                    var CreditoEfetuado = await unitOfWork.ContaBancariaRepository.CreditoPorChavePixClienteId(parameters.Valor, (int)idContaIdCliente.IdCliente, "CRED PIX " + parameters.Proof.Payer.Holder.Document + " - " + parameters.Proof.Payer.Holder.Name, (int)idContaIdCliente.IdContaBancariaEmpresa);

                    _logger.LogInformation("[{Evento}] - PIX Recebido de {Valor}, IdCliente creditado {IdCliente}. Conta bancaria Empresa: {IdContaBancariaEmpresa}", "EV2001", parameters.Valor, idContaIdCliente.IdCliente, idContaIdCliente.IdContaBancariaEmpresa);

                    await unitOfWork.ContaBancariaRepository.InsertDepositoManual((decimal)parameters.Amount, (int)idContaIdCliente.IdCliente, parameters.EndToEndId, DateTime.Now);

                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"[EV2001] - FALHA na Recepção do PIX de {parameters.Valor}. Ex. {ex}");
            }

            if (parameters.Proof.Payee == null)
            {
                if (parameters.Proof.Beneficiary != null)
                {
                    parameters.Proof.Payee = new CobrancaRecebidaModel.ProofData.ParticipanteDate()
                    {
                        Branch = parameters.Proof.Beneficiary.Branch,
                        Number = parameters.Proof.Beneficiary.Number,
                        Holder = new CobrancaRecebidaModel.ProofData.HolderData
                        {
                            Document = parameters.Proof.Beneficiary.Holder.Document,
                            Name = parameters.Proof.Beneficiary.Holder.Name
                        }
                    };
                }
                else
                {
                    _logger.LogWarning("[{Classe}] Payee e Beneficiary vieram nulos.", "Webhook Delbank");

                    return false;
                }
            }

            LogContext.PushProperty("Payee", parameters.Proof.Payee, true);

            var conta = parameters.Proof.Payee.Number[..^1];
            var digitoConta = $"{parameters.Proof.Payee.Number[^1]}";

            LogContext.PushProperty("Conta", $"{conta}-{digitoConta}");

            var dadosConta = await unitOfWork.ContaBancariaRepository.ObterIdContaIdClientePorDadosConta(
                (int)TipoBanco,
                parameters.Proof.Payee.Branch,
                conta,
                digitoVerificadorConta: digitoConta);

            if (dadosConta == null)
            {
                _logger.LogInformation("[{Classe}] Dados da conta não encontrados", "Webhook Delbank");
                
                return false;
            }

            LogContext.PushProperty("DadosConta", dadosConta, true);

            int idRecebimentoPIX = await InserirRecebimentoOperacaoBancaria(parameters, tipoOperacaoBancaria, dadosConta!.IdCliente, dadosConta!.Id);

            if (idRecebimentoPIX <= 0)
            {
                return false;
            }

            _logger.LogInformation("[{Classe}] CorrelationId dos parâmetros: {Parameters_CorrelationId}", "Webhook Delbank", parameters.CorrelationId);

            if (string.IsNullOrEmpty(parameters.CorrelationId))
            {
                var taxaRecebimento = await unitOfWork.TaxaClienteOperacaoRepository.ObterTaxaPorClienteOperacao(dadosConta!.IdCliente, operacaoServico);

                if (taxaRecebimento == null)
                {
                    _logger.LogWarning("[{Classe}] Não foi possível obter as taxas", "Webhook Delbank");
                    
                    return false;
                }

                int idEntradaCliente = await unitOfWork.EntradaClienteRepository.InserirRecebimentoOperacaoBancaria(idRecebimentoPIX, operacaoServico, dadosConta!.IdCliente, descServico, Convert.ToDecimal(parameters.Amount), taxaRecebimento);

                if (idEntradaCliente <= 0)
                    return true;

                var urlNotificacao = await unitOfWork.ClienteRepository.ObterUrlNotificacaoMovimentacaoPorIdCliente(dadosConta.IdCliente);

                if (!string.IsNullOrWhiteSpace(urlNotificacao))
                {
                    var contaPagadora = parameters.Proof.Payer.Number[..^1];
                    var digitoContaPagadora = $"{parameters.Proof.Payer.Number[^1]}";

                    busNotificarCliente.Publicar(new Infrastructure.RabbitMq.Events.NotificarClienteEvent(
                        dadosConta!.IdCliente,
                        urlNotificacao,
                        parameters.EndToEndId,
                        "PIX_RECEIVED",
                        new BodyData
                        {
                            Type = "PIX_RECEIVED",
                            EndToEndId = parameters.EndToEndId,
                            ReceivedAmount = (int)(parameters.Amount * 100),
                            TransactionCreationDateUTC = parameters.CreatedAt,
                            Receiver = new DadosConta
                            {
                                Agency = parameters.Proof.Payee.Branch,
                                AccountNumber = conta,
                                AccountNumberDigit = digitoConta,
                                Name = parameters.Proof.Payee.Holder.Name,
                                Document = parameters.Proof.Payee.Holder.Document
                            },
                            Payer = new DadosConta
                            {
                                Agency = parameters.Proof.Payer.Branch,
                                AccountNumber = contaPagadora,
                                AccountNumberDigit = digitoContaPagadora,
                                Name = parameters.Proof.Payer.Holder.Name,
                                Document = parameters.Proof.Payer.Holder.Document
                            }
                        })
                    );
                }
            }

            _logger.LogInformation("[{Classe}] Processado com sucesso", "Webhook Delbank");

            return true;
        }

        private async Task<int> InserirRecebimentoOperacaoBancaria(CobrancaRecebidaModel parameters, byte idTipoOperacaoBancaria, int? idCliente, int? idContaBancaria)
        {
            return await unitOfWork.RecebimentoOperacaoBancariaRepository.Inserir(
                idTipoOperacaoBancaria,
                parameters.Proof.TransactionId,
                nomePagador: parameters.Proof.Payer.Holder.Name,
                documentoPagador: parameters.Proof.Payer.Holder.Document,
                ISPBPagador: parameters.Proof.Payer.Participant.Ispb,
                chaveRecebedor: parameters.Proof.Key,
                !string.IsNullOrEmpty(parameters.ReferenceId) ? parameters.ReferenceId : parameters.EndToEndId,
                parameters.CorrelationId,
                parameters.ReferenceId,
                dataCadastro: DateTime.UtcNow.AddHours(-3),
                dataTransacao: parameters.CreatedAt,
                descricaoCliente: string.Empty,
                idTipoMovimentoContabil: TipoMovimentoContabil.Credito,
                informacoesAdicionais: string.Empty,
                motivoEstorno: string.Empty,
                $"{parameters.Nsu}",
                valorTransacao: parameters.Amount,
                idCliente,
                idContaBancaria
            );
        }
    }
}