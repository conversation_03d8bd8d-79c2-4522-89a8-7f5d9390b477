﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.DelBank;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Serilog.Context;

namespace Multipay.Queue.Webhook.Application.Services.DelBank;

public interface INotificacaoEstornoSaqueService
{
    Task<bool> ProcessarEstornoSaque(EstornoRecebidoModel? model);
}

internal class NotificacaoEstornoSaqueService(
    IUnitOfWork unitOfWork,
    IBusNotificarSaqueEstornadoCliente busNotificarSaqueEstornado,
    ILogger<NotificacaoEstornoSaqueService> logger) : INotificacaoEstornoSaqueService
{
    public async Task<bool> ProcessarEstornoSaque(EstornoRecebidoModel? model)
    {
        if (model == null)
            return true;

        var solicitacaoSaque = await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(model.OriginalEndToEndId);

        if (solicitacaoSaque == null || solicitacaoSaque.DataEstorno.HasValue)
            return true;

        using (LogContext.PushProperty("Payload", model, true))
        {
            logger.LogInformation("Evento de estorno de saque. E2E Transação: {E2ETransacao} / E2E Devolução: {E2EDevolucao}", model.OriginalEndToEndId, model.EndToEndId);

            await unitOfWork.SolicitacaoSaqueRepository.EstornarSaque(solicitacaoSaque.Id, model.Description ?? string.Empty);

            await unitOfWork.SolicitacaoSaqueRepository.RegistrarEndToEndDevolucao(model.OriginalEndToEndId, model.EndToEndId!);

            busNotificarSaqueEstornado.PublicarNotificacaoCliente(solicitacaoSaque.UrlAtualizacao ?? solicitacaoSaque.UrlConfirmacao!, solicitacaoSaque.Codigo, solicitacaoSaque.CustomId, solicitacaoSaque.CustomUserId, solicitacaoSaque.Guid, solicitacaoSaque.CodigoTransacao);
        }

        return true;
    }
}