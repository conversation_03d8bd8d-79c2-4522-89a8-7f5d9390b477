﻿using Microsoft.Extensions.DependencyInjection;
using Multipay.Infrastructure.Plugin.Aarin.Setup;
using Multipay.Infrastructure.Plugin.AnspacePay.Services;
using Multipay.Infrastructure.Plugin.BS2.Setup;
using Multipay.Infrastructure.Plugin.Celcoin.Setup;
using Multipay.Infrastructure.Plugin.DelBank.Setup;
using Multipay.Infrastructure.Plugin.Genial.Setup;
using Multipay.Infrastructure.Plugin.Sicoob.Setup;
using Multipay.Queue.Webhook.Application.Providers;
using Multipay.Queue.Webhook.Application.Services;
using Multipay.Queue.Webhook.Application.Services.Aarin;
using Multipay.Queue.Webhook.Application.Services.Celcoin;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.DelBank;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco.Repositories;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Bus;
using Multipay.Queue.Webhook.Infrastructure.Setup;
using Multipay.Service.Criptography;

namespace Multipay.Queue.Webhook.Application.Setup
{
    public static class DependencyInjectionServices
    {
        public static void AddDependencyInjectionApplicationServices(this IServiceCollection services, Microsoft.Extensions.Configuration.IConfiguration configuration, CriptoService criptoService)
        {
            services.AddDependencyInjectionApplicationRepositoriesMongo(configuration, criptoService);
            
            services.AddDependencyInjectionSicoobServices();
            services.AddDependencyInjectionGenial(configuration);
            services.AddDependencyInjectionAarinServices(configuration);
            services.AddDependencyInjectionBS2(configuration);
            services.AddDependencyInjectionDelBank(configuration);
            services.AddDependencyInjectionCelcoinServices(configuration);
            services.AddSingleton(typeof(IServicesProvider<,>), typeof(ServicesProvider<,>));

            services.AddScoped<ICobrancaService, CobrancaService>();
            services.AddScoped<IInconsistenciaCobrancaService, InconsistenciaCobrancaService>();

            services.AddScoped<IWebhookRecebidoService, WebhookRecebidoSicoobService>();
            services.AddScoped<IWebhookRecebidoService, WebhookRecebidoGenialService>();
            services.AddScoped<IWebhookRecebidoService, WebhookRecebidoBS2Service>();
            services.AddScoped<IWebhookRecebidoService, WebhookRecebidoBMPService>();
            services.AddScoped<IWebhookRecebidoService, WebhookRecebidoDIService>();
            services.AddScoped<IWebhookRecebidoService, WebhookRecebidoDelBankService>();
            services.AddScoped<IWebhookRecebidoService, WebhookRecebidoBancoDockService>();
            services.AddScoped<IWebhookRecebidoService, WebhookRecebidoAarinService>();
            services.AddScoped<IWebhookRecebidoService, WebhookRecebidoCelcoinService>();

            services.AddScoped<Services.BMP.IMovimentacaoEntradaService, Services.BMP.MovimentacaoEntradaService>();
            services.AddScoped<Services.BMP.IMovimentacaoSaidaService, Services.BMP.MovimentacaoSaidaService>();

            services.AddScoped<IEstornarSaqueService, EstornarSaqueService>();

            services.AddScoped<Services.DelBank.IMovimentacaoEntradaService, Services.DelBank.MovimentacaoEntradaService>();
            services.AddScoped<Services.DelBank.IMovimentacaoSaidaService, Services.DelBank.MovimentacaoSaidaService>();
            services.AddScoped<Services.DelBank.IMovimentacaoSaidaTransfInternaService, Services.DelBank.MovimentacaoSaidaTransfInternaService>();

            services.AddScoped<Services.AnspacePay.IMovimentacaoEntradaService, Services.AnspacePay.MovimentacaoEntradaService>();
            services.AddScoped<Services.AnspacePay.IMovimentacaoSaidaService, Services.AnspacePay.MovimentacaoSaidaService>();
            services.AddScoped<Services.AnspacePay.IMovimentacaoSaidaTransfInternaService, Services.AnspacePay.MovimentacaoSaidaTransfInternaService>();

            services.AddScoped<INotificacaoEstornoSaqueService, NotificacaoEstornoSaqueService>();
            services.AddScoped<INotificacaoEstornoDepositoService, NotificacaoEstornoDepositoService>();

            services.AddScoped<ICobrancaRepository, CobrancaRepository>();
            services.AddScoped<IQrCodePixRepository, QrCodePixRepository>();
            services.AddScoped<ISolicitacaoSaqueRepository, SolicitacaoSaqueRepository>();
            services.AddSingleton<IBusNotificarSaqueEstornadoCliente, BusNotificarSaqueEstornadoCliente>();


            services.AddScoped<ICelcoinPixReversalService, CelcoinPixReversalService>();

            // Os serviços de cada banco chamados EstornarQrCodeService vão precisar ser renomeados.
            services.AddScoped<IEstornarQrCodeService, EstornarQrCodeService>();
            
            services.AddScoped<Services.DelBank.CobrancaRecebidaService>();
            services.AddScoped<Services.AnspacePay.CobrancaRecebidaService>();

            services.AddScoped<CobrancaRecebidaGenialService>();
            services.AddScoped<CobrancaRecebidaAarinService>();
            services.AddScoped<CobrancaRecebidaCelcoinService>();

            services.AddScoped<CobrancaRecebidaDelBankService>();
            services.AddScoped<CobrancaRecebidaAnspacePayService>();

            services.AddScoped<CobrancaRecebidaBS2Service>();
            services.AddScoped<CobrancaRecebidaSicoobService>();
            services.AddScoped<CobrancaRecebidaBMPService>();
            services.AddScoped<CobrancaRecebidaDIService>();
            services.AddScoped<CobrancaRecebidaDockService>();
            services.AddScoped<CobrancaRecebidaSulCredService>();
            services.AddScoped<IValidacaoChaveRecebidoBS2Service, ValidacaoChaveRecebidoBS2Service>();

            services.AddScoped<IPixReversalService, PixReversalService>();
            services.AddScoped<IPagamentoConfirmadoAarinService, PagamentoConfirmadoAarinService>();
            services.AddScoped<IPagamentoConfirmadoCelcoinService, PagamentoConfirmadoCelcoinService>();
            services.AddScoped<IPagamentoConfirmadoGenialService, PagamentoConfirmadoGenialService>();
            services.AddScoped<IPagamentoConfirmadoSicoobService, PagamentoConfirmadoSicoobService>();
            services.AddScoped<IPagamentoConfirmadoSulCredService, PagamentoConfirmadoSulCredService>();
            services.AddScoped<IPagamentoConfirmadoBS2Service, PagamentoConfirmadoBS2Service>();
            services.AddScoped<IPagamentoConfirmadoDIService, PagamentoConfirmadoDIService>();
            services.AddScoped<IPagamentoConfirmadoDelBankService, PagamentoConfirmadoDelBankService>();
            services.AddScoped<IPagamentoConfirmadoAnspacePayService, PagamentoConfirmadoAnspacePayService>();
            services.AddScoped<IPagamentoConfirmadoDockService, PagamentoConfirmadoDockService>();
            

            services.AddScoped<Func<TipoBanco, IWebhookRecebidoService>>(sp => tipo =>
            {
                var services = sp.GetServices<IWebhookRecebidoService>();
                return services.First(item => item.TipoBanco == tipo);
            });
            
            services.AddHttpClient("BrasilApi", httpClient =>
            {
                httpClient.BaseAddress = new Uri("https://brasilapi.com.br/");
            });

            services.AddSingleton<IBrasilApiService, BrasilApiService>();
        }
    }
}