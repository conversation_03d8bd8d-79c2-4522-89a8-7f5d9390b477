﻿using Multipay.Queue.EnvioWebhookSaque.Application.Services;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.EnvioWebhookSaque.Api.Workers
{
    public class WorkerSaqueConfirmadoNotificarClientePublishNotification : BackgroundConsumerService<SaqueConfirmadoNotificarClienteEvent>
    {
        private readonly IServiceProvider serviceProvider;

        public WorkerSaqueConfirmadoNotificarClientePublishNotification(
            ILogger<WorkerSaqueConfirmadoNotificarClientePublishNotification> logger,
            IServiceProvider serviceProvider,
            IBusConsumerSolicitacaoSaqueConfirmadoNotificarClientePublishNotification busConsumer) : base(logger, busConsumer)
        {
            this.serviceProvider = serviceProvider;
        }

        protected override async Task<bool> ProcessEventHandler(string mensagem, SaqueConfirmadoNotificarClienteEvent @event, bool _)
        {
            using IServiceScope scope = serviceProvider.CreateScope();

            try
            {
                var service = scope.ServiceProvider.GetRequiredService<INotificarClientePublishNotificationService>();
                return await service.EnviarNotificacaoSaqueConcluido(@event);
            }
            finally
            {
                var logsService = scope.ServiceProvider.GetRequiredService<ICentralizadorLogsService>();
                logsService.Salvar();
            }
        }
    }
}