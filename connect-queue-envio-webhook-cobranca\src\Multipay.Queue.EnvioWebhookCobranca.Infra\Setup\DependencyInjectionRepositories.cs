﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Multipay.Queue.EnvioWebhookCobranca.Core.Utils;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Data;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Repositories;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Repositories.MongoRepositories;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Services;
using Multipay.Queue.Webhook.Infrastructure.Repositories;
using Multipay.Queue.Webhook.Infrastructure.Services;
using Multipay.Service.Criptography;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Setup
{
    public static class DependencyInjectionRepositories
    {
        public static void AddDependencyInjectionApplicationRepositoriesMongo(this IServiceCollection services, IConfiguration configuration, CriptoService criptoService)
        {
            var mongoSettings = new MongoStoreDatabaseSettings();

            configuration.GetSection(nameof(MongoStoreDatabaseSettings)).Bind(mongoSettings);

            var config = configuration.GetSection(key: nameof(MongoStoreDatabaseSettings));

            var connectionString = config.GetRequiredSection("ConnectionString").Value!;

            var start = connectionString.IndexOf("//") + 2;
            var end = connectionString.IndexOf("@", start);

            var password = connectionString.Substring(start, end - start).Split(":")[1];
            var encryptedPassword = password;

            password = criptoService.Decrypt(password).Result;

            connectionString = connectionString.Replace(encryptedPassword, password);

            config.GetSection("ConnectionString").Value = connectionString;

            mongoSettings.ConnectionString = connectionString;

            services.Configure<MongoStoreDatabaseSettings>(config)
                .PostConfigure<MongoStoreDatabaseSettings>(opt =>
                {
                    opt = mongoSettings;
                });

            var sp = services.BuildServiceProvider();
            using var scope = sp.CreateScope();
            var ambienteHelp = scope.ServiceProvider.GetRequiredService<AmbienteHelp>();

            services.AddSingleton<IConnectMongoService, ConnectMongoService>();
            services.AddSingleton<AnspaceMongoContext>();

            services.AddSingleton<ILogCobrancaRepository, LogCobrancaRepository>();
            services.AddSingleton<IWebhookFornecedorCobrancaRepository, WebhookFornecedorCobrancaRepository>();
            services.AddSingleton<IEnvioWebhookCobrancaRepository, EnvioWebhookCobrancaRepository>();
            services.AddSingleton<IRequisicoesFornecedorRepository, RequisicoesFornecedorRepository>();
            services.AddSingleton<IEntradaClienteRepository, EntradaClienteRepository>();
            services.AddScoped<ICentralizadorLogsService, CentralizadorLogsService>();

            services.AddTransient<IConstantesRepository, ConstantesRepository>();
            services.AddTransient<IErroRepository, ErroRepository>();

            var cnn = ambienteHelp.SqlServer_ConnectionString;

            if (string.IsNullOrWhiteSpace(cnn))
                cnn = configuration.GetConnectionString("MultiPayDatabase")!;

            start = cnn.IndexOf("password=") + 9;
            end = cnn.IndexOf(";", start);

            password = cnn.Substring(start, end - start);
            encryptedPassword = password;

            password = criptoService.Decrypt(password).Result;

            cnn = cnn.Replace(encryptedPassword, password);

            services.AddDbContext<DbContext, AnspaceContext>(
                options => options.UseSqlServer(cnn),
                ServiceLifetime.Transient,
                ServiceLifetime.Transient);
        }
    }
}