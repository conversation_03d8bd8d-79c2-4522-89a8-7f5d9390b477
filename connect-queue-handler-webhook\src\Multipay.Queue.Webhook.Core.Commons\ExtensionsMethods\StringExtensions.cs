﻿namespace Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods
{
    public static class StringExtensions
    {
        public static bool ContainsIgnoreCase(this string source, string value)
        {
            return source.IndexOf(value, StringComparison.OrdinalIgnoreCase) >= 0;
        }

        public static T? Parser<T>(this string source)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(source);
        }

        public static int? ToInt(this object? source)
        {
            try
            {
                return int.Parse($"{source}");
            }
            catch
            {
                return null;
            }
        }

        public static T? To<T>(this string? source)
        {
            if (source == null)
            {
                return default(T?);
            }
            return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(source);
        }
    }
}