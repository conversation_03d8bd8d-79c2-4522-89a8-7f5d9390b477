using Connect.Integration.Bank.Services.Common.Models.Requests;
using Connect.Integration.Bank.Strategy.Interfaces;
using Connect.Integration.Bank.Tests.Fixtures;
using FluentAssertions;

namespace Connect.Integration.Bank.Tests.Services.Common;

public class RemoveCacheServiceTests : IClassFixture<ServicesFixture>
{
    private readonly IIntegrationBankService _sut;
    private readonly ServicesFixture _servicesFixture;    

    public RemoveCacheServiceTests(ServicesFixture fixture)
    {
        _servicesFixture = fixture;
        _sut = fixture.IntegrationBankService;
    }

    [Fact]
    public async Task ShouldExecuteDelBankSuccess()
    {
        // Arrange
        var credential = _servicesFixture.CredentialDelBankAccount;
        var key = Guid.NewGuid().ToString();

        var request = new RemoveCacheRequest(credential, key);

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();        
    }
}
