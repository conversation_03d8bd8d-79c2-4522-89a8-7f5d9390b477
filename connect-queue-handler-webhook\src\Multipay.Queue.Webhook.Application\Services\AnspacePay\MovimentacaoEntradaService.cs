﻿using Amazon.SecretsManager.Model;
using Multipay.Queue.Webhook.Application.Models.AnspacePay;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Emails;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using static Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events.NotificarClienteEvent;
using static Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events.NotificarClienteEvent.BodyData;

namespace Multipay.Queue.Webhook.Application.Services.AnspacePay
{
    public interface IMovimentacaoEntradaService
    {
        Task<bool> ProcessarRecebimentoPIX(string sourceParameters, string idWebhook);
    }

    internal class MovimentacaoEntradaService : IMovimentacaoEntradaService
    {
        private readonly IUnitOfWork unitOfWork;
        private readonly CobrancaRecebidaService cobrancaRecebidaService;
        private readonly IEstornarSaqueService estornarSaqueService;
        private readonly IBusNotificarCliente busNotificarCliente;
        private readonly IEmailHandler emailHandler;
        private readonly ICentralizadorLogsService centralizadorLogsService;

        public MovimentacaoEntradaService(
            IUnitOfWork unitOfWork,
            CobrancaRecebidaService cobrancaRecebidaService,
            IEstornarSaqueService estornarSaqueService,
            IBusNotificarCliente busNotificarCliente,
            IEmailHandler emailHandler,
            ICentralizadorLogsService centralizadorLogsService)
        {
            this.unitOfWork = unitOfWork;
            this.cobrancaRecebidaService = cobrancaRecebidaService;
            this.estornarSaqueService = estornarSaqueService;
            this.busNotificarCliente = busNotificarCliente;
            this.emailHandler = emailHandler;
            this.centralizadorLogsService = centralizadorLogsService;
        }

        private TipoBanco TipoBanco => TipoBanco.AnspacePay;

        public async Task<bool> ProcessarRecebimentoPIX(string sourceParameters, string idWebhook)
        {
            var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<CobrancaRecebidaModel>(sourceParameters)!;

            var result = await ProcessarCreditoContaCliente(parameters, TipoOperacaoBancaria.PIX, OperacaoServico.RecebimentoPIX, parameters.Type != "CREDIT_PIX_REFUND" ? "RECEBE PIX" : "DEVOLUÇÃO PIX");

            if (string.IsNullOrWhiteSpace(parameters.IdempotencyKey))
            {
                return result;
            }
            if (parameters.InitiationType == "KEY" && parameters.Key is not null)
            {
                return true;
            }
                //parameters.Txid = parameters.TransactionId;
                return await cobrancaRecebidaService.Processar(sourceParameters, idWebhook);
        }

        private async Task<bool> ProcessarCreditoContaCliente(CobrancaRecebidaModel parameters, byte tipoOperacaoBancaria, byte operacaoServico, string descServico)
        {
            if (string.IsNullOrWhiteSpace(parameters.EndToEndId))
                return true;

            var verificarRecebimentoPIX = await unitOfWork.RecebimentoOperacaoBancariaRepository.VerificarCodigoMovimentoExiste(parameters.EndToEndId);
            if (verificarRecebimentoPIX)
                return true;

            if (parameters is { Type: "PIX_ADMINISTRATIVE_RETURN" or "CREDIT_PIX_UNDONE" })
            {
                //realizar estorno
                await estornarSaqueService.EstornarSaque(parameters.Proof.EndToEndId, parameters.Proof.Description ?? $"Webhook recebido AnspacePay com status: {parameters.Type}");
                return true;
            }

            if (parameters.InitiationType == "KEY" &&  parameters.Key is not null)
            {
                //verifica se e transferencia por pix
                var idCliente = await unitOfWork.ContaBancariaRepository.VerificaSeExisteDadosContaPorChavePix(parameters.Key);
                if (idCliente == null)
                    return false;

                // acionar o credito 
                var CreditoEfetuado = await unitOfWork.ContaBancariaRepository.CreditoPorChavePixClienteId(parameters.Valor,(int)idCliente, "CRED PIX "+parameters.Payer.Holder.Document+" - " + parameters.Payer.Holder.Name,0);
                return true;
            }


            if (parameters.Proof?.Payee == null)
                if (parameters.Proof?.Beneficiary != null)
                    parameters.Proof.Payee = new CobrancaRecebidaModel.ProofData.ParticipanteDate()
                    {
                        Branch = parameters.Proof.Beneficiary.Branch,
                        Number = parameters.Proof.Beneficiary.Number,
                        Holder = new CobrancaRecebidaModel.ProofData.HolderData
                        {
                            Document = parameters.Proof.Beneficiary.Holder.Document,
                            Name = parameters.Proof.Beneficiary.Holder.Name
                        }
                    };
                else return false;

            var conta = parameters.Proof.Payee.Number[..^1];
            var digitoConta = $"{parameters.Proof.Payee.Number[^1]}";
            string body = string.Format("<br/><br />Agência: {0}<br/>Conta:{1}<br/>Chave:{2}<br /><br />JSON:{3}",
                parameters.Proof.Payee.Branch,
                parameters.Proof.Payee.Number,
                parameters.Proof.Key,
                parameters.ToJson());

            var dadosConta = await unitOfWork.ContaBancariaRepository.ObterIdContaIdClientePorDadosConta(
                (int)TipoBanco,
                parameters.Proof.Payee.Branch,
                conta,
                digitoVerificadorConta: digitoConta);
            if (dadosConta == null)
            {
                //await EnviarEmailSuporteOperacional($"CallBack {TipoBanco} - {descServico} - Recebedor não encontrado",
                //    $"Não foi encontrado um recebedor com os dados recebedidos!{body}");
                return false;
            }
            if (parameters.Amount <= 0.00D)
            {
                //await EnviarEmailSuporteOperacional($"CallBack {TipoBanco} - {descServico} - Valor inválido",
                //    $"O valor informado como recebido é inválido!{body}");
            }

            int idRecebimentoPIX = await InserirRecebimentoOperacaoBancaria(parameters, tipoOperacaoBancaria, dadosConta!.IdCliente, dadosConta!.Id);
            if (idRecebimentoPIX <= 0)
                return false;

            // Inserindo o credito em caso de deposito direto para a chave
            if (string.IsNullOrEmpty(parameters.CorrelationId))
            {
                var taxaRecebimento = await unitOfWork.TaxaClienteOperacaoRepository.ObterTaxaPorClienteOperacao(dadosConta!.IdCliente, operacaoServico);
                if (taxaRecebimento == null)
                    return false;

                // Adicionando o credito na conta do cliente pois foi um deposito direto por chave
                int idEntradaCliente = await unitOfWork.EntradaClienteRepository.InserirRecebimentoOperacaoBancaria(idRecebimentoPIX, operacaoServico, dadosConta!.IdCliente, descServico, Convert.ToDecimal(parameters.Amount), taxaRecebimento);
                if (idEntradaCliente <= 0)
                {
                    //await EnviarEmailSuporteOperacional($"CallBack {TipoBanco} - {descServico} - Crédito não efetuado",
                    //    $"Ocorreu um erro ao efetuar o crédito ao cliente!{body}");

                    return true;
                }
                var urlNotificacao = await unitOfWork.ClienteRepository.ObterUrlNotificacaoMovimentacaoPorIdCliente(dadosConta.IdCliente);

                if (!string.IsNullOrWhiteSpace(urlNotificacao))
                {
                    var contaPagadora = parameters.Proof.Payer.Number[..^1];
                    var digitoContaPagadora = $"{parameters.Proof.Payer.Number[^1]}";
                    //notificar cliente
                    busNotificarCliente.Publicar(new Infrastructure.RabbitMq.Events.NotificarClienteEvent(
                        dadosConta!.IdCliente,
                        urlNotificacao,
                        parameters.EndToEndId,
                        "PIX_RECEIVED",
                        new BodyData
                        {
                            Type = "PIX_RECEIVED",
                            EndToEndId = parameters.EndToEndId,
                            ReceivedAmount = (int)(parameters.Amount * 100),
                            TransactionCreationDateUTC = parameters.CreatedAt,
                            Receiver = new DadosConta
                            {
                                Agency = parameters.Proof.Payee.Branch,
                                AccountNumber = conta,
                                AccountNumberDigit = digitoConta,
                                Name = parameters.Proof.Payee.Holder.Name,
                                Document = parameters.Proof.Payee.Holder.Document
                            },
                            Payer = new DadosConta
                            {
                                Agency = parameters.Proof.Payer.Branch,
                                AccountNumber = contaPagadora,
                                AccountNumberDigit = digitoContaPagadora,
                                Name = parameters.Proof.Payer.Holder.Name,
                                Document = parameters.Proof.Payer.Holder.Document
                            }
                        }));
                }
            }

            return true;
        }

        private async Task<int> InserirRecebimentoOperacaoBancaria(CobrancaRecebidaModel parameters, byte idTipoOperacaoBancaria, int? idCliente, int? idContaBancaria)
        {
            return await unitOfWork.RecebimentoOperacaoBancariaRepository.Inserir(
                            idTipoOperacaoBancaria,
                            parameters.Proof.TransactionId,
                            nomePagador: parameters.Proof.Payer.Holder.Name,
                            documentoPagador: parameters.Proof.Payer.Holder.Document,
                            ISPBPagador: parameters.Proof.Payer.Participant.Ispb,
                            chaveRecebedor: parameters.Proof.Key,
                            !string.IsNullOrEmpty(parameters.ReferenceId) ? parameters.ReferenceId : parameters.EndToEndId,
                            parameters.CorrelationId,
                            parameters.ReferenceId,
                            dataCadastro: DateTime.UtcNow.AddHours(-3),
                            dataTransacao: parameters.CreatedAt,
                            descricaoCliente: string.Empty,
                            idTipoMovimentoContabil: TipoMovimentoContabil.Credito,
                            informacoesAdicionais: string.Empty,
                            motivoEstorno: string.Empty,
                            $"{parameters.Nsu}",
                            valorTransacao: parameters.Amount,
                            idCliente,
                            idContaBancaria
                            );
        }

        private Task EnviarEmailSuporteOperacional(string titulo, string body)
        {
            this.emailHandler.EnviarEmailSuporteOperacional(titulo, body);
            return Task.CompletedTask;
        }
    }
}