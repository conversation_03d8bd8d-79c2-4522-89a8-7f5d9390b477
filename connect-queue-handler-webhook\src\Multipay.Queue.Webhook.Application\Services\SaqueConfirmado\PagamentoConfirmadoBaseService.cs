﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services.SaqueConfirmado
{
    internal abstract class PagamentoConfirmadoBaseService<T>
    {
        private readonly ILogger logger;
        protected readonly ICentralizadorLogsService CentralizadorLogsService;
        private readonly IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado;
        private readonly IBrasilApiService brasilApiService;
        private readonly IEstornarSaqueService estornarSaqueService;
        protected readonly IUnitOfWork unitOfWork;

        protected abstract string NomeBanco { get; }

        protected abstract Status? ObterStatus(T parameters);

        protected abstract string ObterEndToEndId(T parameters);

        protected PagamentoConfirmadoBaseService(
            ILogger logger,
            ICentralizadorLogsService centralizadorLogsService,
            IBusNotificarSaqueConfirmadoCliente busNotificarSaqueConfirmado,
            IBrasilApiService brasilApiService,
            IEstornarSaqueService estornarSaqueService,
            IUnitOfWork unitOfWork)
        {
            this.logger = logger;
            this.CentralizadorLogsService = centralizadorLogsService;
            this.busNotificarSaqueConfirmado = busNotificarSaqueConfirmado;
            this.brasilApiService = brasilApiService;
            this.estornarSaqueService = estornarSaqueService;
            this.unitOfWork = unitOfWork;
        }

        public virtual async Task<bool> ProcessarPagamento(WebhookRecebidoEvent @event)
        {
            var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<T>($"{@event.Data}");

            if (parameters == null)
                return false;

            var endToEndId = ObterEndToEndId(parameters);

            var idWebhook = CentralizadorLogsService.AdicionarLogWebhookFornecedor($"BANCO {NomeBanco}", "PAGAMENTOS", parameters.ToJson(), string.Empty);

            var solicitacaoSaque = await ObterSolicitacaoSaque(parameters);

            if (solicitacaoSaque == null)
                return true;

            bool sucesso = false;
            string? status;

            switch (ObterStatus(parameters))
            {
                case Status.EFETIVADO://EFETIVADO
                    await ProcessarPagamentoEfetivado(parameters, idWebhook, solicitacaoSaque, endToEndId);
                    sucesso = true;
                    status = "Completed";
                    break;

                case Status.REJEITADO://REJEITADO
                    await ProcessarPagamentoComFalha(parameters, idWebhook, solicitacaoSaque, MsgRejeitado);
                    status = "REJECTED TRANSACTION";
                    break;

                case Status.ERRO://ERRO
                    await ProcessarPagamentoComFalha(parameters, idWebhook, solicitacaoSaque, "BACEN RETORNOU ERRO ENTRE INSTITUIÇÕES");
                    status = "INSTABILITY";
                    break;

                case Status.EXPIRADO://EXPIRADO
                    await ProcessarPagamentoComFalha(parameters, idWebhook, solicitacaoSaque, "TEMPO MÁXIMO DA TRANSAÇÃO EXCEDIO NO BACEN");
                    status = "RETURNED TRANSACTION";
                    break;

                case null:
                default:
                    CentralizadorLogsService.AdicionarLogWebhookFornecedor($"BANCO {NomeBanco}", "PAGAMENTOS", $"parameters.Status desconhecido, parameters: {parameters.ToJson()}", "PigPag.Common.IP.IPPublico");
                    return true;
            }
            logger.LogInformation("PublicarNotificacaoCliente -> {solicitacaoSaque} - {idWebhook} - {sucesso} - {status}",
                solicitacaoSaque.Id,
                idWebhook,
                sucesso,
                status);

            busNotificarSaqueConfirmado.PublicarNotificacaoCliente(solicitacaoSaque.Id, idWebhook, sucesso, status);

            return true;
        }

        protected virtual async Task<ViewSolicitacaoSaqueDadosBasicos?> ObterSolicitacaoSaque(T parameters)
        {
            var endToEndId = ObterEndToEndId(parameters);
            return await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(endToEndId);
        }

        protected virtual string MsgRejeitado => "REJEITADO PELA INSTITUIÇÃO RECEBEDORA";

        protected virtual async Task ProcessarPagamentoComFalha(T parameters, string idWebhook,
            ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string mensagem)
        {
            CentralizadorLogsService.AdicionarLogWebhookFornecedorSolicitacaoSaque(idWebhook, solicitacaoSaque.Id,
                mensagem);

            if (solicitacaoSaque.DataEstorno == null)
            {
                CentralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Transação estornada, motivo: {mensagem}");
                await unitOfWork.SolicitacaoSaqueRepository.EstornarSaque(solicitacaoSaque.Id, mensagem);
            }
        }

        protected virtual Task MudarStatusSolicitacaoSaque(int idSaque, byte idStatus)
            => unitOfWork.SolicitacaoSaqueRepository.MudarStatusSolicitacaoSaque(idSaque, idStatus);

        protected virtual async Task ProcessarPagamentoEfetivado(T _, string idWebhook, ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string endToEndId)
        {
            CentralizadorLogsService.AdicionarLogWebhookFornecedorSolicitacaoSaque(idWebhook, solicitacaoSaque.Id, "PAGAMENTO EFETIVADO");

            await unitOfWork.SolicitacaoSaqueRepository.ConfirmarSolicitacaoSaque(solicitacaoSaque.Id, idWebhook);
        }

        public async Task InserirRecebedorSolicitacaoSaque(int idSaque, string ispb, string documento, string nome, string nomeFantasia, string agencia, string conta, string tipo)
        {
            try
            {
                var banco = string.Empty;
                var nomeBanco = string.Empty;

                var bancoParticipantesPix = await brasilApiService.ConsultarPorIspb(ispb);

                logger.LogInformation("ConsultarPorIspb: {ispb}, result: {bancoParticipantesPix}", ispb, bancoParticipantesPix?.Nome);

                if (bancoParticipantesPix != null)
                {
                    banco = bancoParticipantesPix.NomeReduzido;
                    nomeBanco = bancoParticipantesPix.Nome;
                }

                await unitOfWork.SolicitacaoSaqueRepository.InserirRecebedorSolicitacaoSaque(idSaque, ispb, documento, nome, nomeFantasia, banco, nomeBanco, agencia, conta, tipo);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Falha ao InserirRecebedorSolicitacaoSaque");
            }
        }

        protected enum Status
        {
            EFETIVADO,
            REJEITADO,
            DEVOLVIDO,
            ERRO,
            EXPIRADO
        }
    }
}