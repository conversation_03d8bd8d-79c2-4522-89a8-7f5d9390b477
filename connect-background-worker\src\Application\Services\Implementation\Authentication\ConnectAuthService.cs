using Application.Services.Interfaces;
using Application.Services.Interfaces.Api;
using Application.Services.Models.Requests;
using Application.Services.Models.Responses;
using Domain.Models;
using Microsoft.Extensions.Logging;
using ResultT;
using static ResultT.Results;

namespace Application.Services.Implementation;

public class ConnectAuthService(ILogger<ConnectAuthService> logger, IConnectTokenApi connectTokenApi) : IConnectAuthService
{
    public async Task<Result<TokenResponse>> GetAccessTokenAsync(CustomerCredential credential, CancellationToken cancellationToken)
    {
        var request = new TokenRequest(credential.UserName, credential.Password);

        var response = await connectTokenApi.GetAccessTokenAsync(request, cancellationToken);
        if (!response.IsSuccessStatusCode)
        {
            logger.Error("Erro ao obter o token: ({Status}): {Response}", response.StatusCode, $"{response.Error?.Content}");
            return Error();
        }
        var tokenResponse = response.Content;

        return Ok(tokenResponse);
    }
}
