﻿using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels
{
    [BsonIgnoreExtraElements]
    public class CobrancasGeradasModel : BaseModel
    {
        public const string CollectionName = nameof(CobrancasGeradasModel);

        public CobrancasGeradasModel(string codigoCobranca, int idCliente, int idCobranca, int idAplicacao = 0, string? numeroFatura = null, string? nomePessoa = null, string? cPFCNPJ = null, double valorLiquido = 0, double? valorPago = null, DateTime dataCriacaoUtc = default, DateTime? dataVencimentoUtc = null, DateTime? dataPagamentoUtc = null, string? txid = null, string? qrCode = null)
        {
            IdCliente = idCliente;
            IdCobranca = idCobranca;
            CodigoCobranca = codigoCobranca;
            DataCriacaoUtc = DateTime.UtcNow;
            IdAplicacao = idAplicacao;
            NumeroFatura = numeroFatura;
            NomePessoa = nomePessoa;
            CPFCNPJ = cPFCNPJ;
            ValorLiquido = valorLiquido;
            ValorPago = valorPago;
            DataVencimentoUtc = dataVencimentoUtc;
            DataPagamentoUtc = dataPagamentoUtc;
            TxId = txid;
            QRCode = qrCode;
        }

        public int IdCobranca { get; private set; }
        public int IdCliente { get; private set; }
        public int IdAplicacao { get; private set; }
        public string CodigoCobranca { get; private set; }
        public string? NumeroFatura { get; private set; }
        public string? NomePessoa { get; private set; }
        public string? CPFCNPJ { get; private set; }
        public double ValorLiquido { get; private set; }
        public double? ValorPago { get; private set; }
        public DateTime DataCriacaoUtc { get; private set; }
        public DateTime? DataVencimentoUtc { get; private set; }
        public DateTime? DataPagamentoUtc { get; private set; }
        public string? TxId { get; private set; }
        public string? QRCode { get; private set; }
    }
}