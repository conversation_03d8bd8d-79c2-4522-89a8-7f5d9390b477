﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.6.33829.357
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "1- Presentation", "1- Presentation", "{EF1A183E-19E0-4E9F-83F6-98C14ECDC49F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookCobranca.Api", "src\Multipay.Queue.EnvioWebhookCobranca.Api\Multipay.Queue.EnvioWebhookCobranca.Api.csproj", "{1999E008-9D62-45E0-A7D5-0A7B8632F349}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "4- Infrastructure", "4- Infrastructure", "{D479BAC6-97D8-40F2-AF8F-01D373194203}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq", "src\Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq\Multipay.Queue.EnvioWebhookCobranca.Infrastructure.RabbitMq.csproj", "{A8854AE3-3A25-47EC-B512-530E92AC67D8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "3- Business", "3- Business", "{5E22EB0D-8525-498D-ABB5-0247A68F93AC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookCobranca.Application", "src\Multipay.Queue.EnvioWebhookCobranca.Application\Multipay.Queue.EnvioWebhookCobranca.Application.csproj", "{F5C156AF-9BF7-4E94-B383-396A0BCEEB17}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "2- Core", "2- Core", "{3CDDF38A-94FB-4FD1-B881-11F3D763465C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookCobranca.Core", "src\Multipay.Queue.EnvioWebhookCobranca.Core\Multipay.Queue.EnvioWebhookCobranca.Core.csproj", "{993462BD-1D81-4A4C-AA67-9357FE42D860}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookCobranca.Infra", "src\Multipay.Queue.EnvioWebhookCobranca.Infra\Multipay.Queue.EnvioWebhookCobranca.Infra.csproj", "{07C7D1B6-C083-448F-A10B-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco", "src\Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco\Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.csproj", "{CA836B32-3B4D-41B1-AE9A-F33F7D4A33F2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{4E84021F-70B9-447A-894D-23ECF5BE1823}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Multipay.Service.Criptography", "src\Multipay.Service.Criptography\Multipay.Service.Criptography.csproj", "{4B754101-E9F4-4A1F-884E-91D7CDDF7864}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Test", "Test", "{4526FFF0-1E84-430D-80CC-669648D30B7E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Multpay.Queue.EnvioWebhookCobranca.Tests", "Multpay.Queue.EnvioWebhookCobranca.Tests\Multpay.Queue.EnvioWebhookCobranca.Tests.csproj", "{714DA4A7-44F9-48C2-B068-C5174BEE38DB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1999E008-9D62-45E0-A7D5-0A7B8632F349}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1999E008-9D62-45E0-A7D5-0A7B8632F349}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1999E008-9D62-45E0-A7D5-0A7B8632F349}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1999E008-9D62-45E0-A7D5-0A7B8632F349}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8854AE3-3A25-47EC-B512-530E92AC67D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8854AE3-3A25-47EC-B512-530E92AC67D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8854AE3-3A25-47EC-B512-530E92AC67D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8854AE3-3A25-47EC-B512-530E92AC67D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{F5C156AF-9BF7-4E94-B383-396A0BCEEB17}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5C156AF-9BF7-4E94-B383-396A0BCEEB17}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5C156AF-9BF7-4E94-B383-396A0BCEEB17}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5C156AF-9BF7-4E94-B383-396A0BCEEB17}.Release|Any CPU.Build.0 = Release|Any CPU
		{993462BD-1D81-4A4C-AA67-9357FE42D860}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{993462BD-1D81-4A4C-AA67-9357FE42D860}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{993462BD-1D81-4A4C-AA67-9357FE42D860}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{993462BD-1D81-4A4C-AA67-9357FE42D860}.Release|Any CPU.Build.0 = Release|Any CPU
		{07C7D1B6-C083-448F-A10B-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{07C7D1B6-C083-448F-A10B-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{07C7D1B6-C083-448F-A10B-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{07C7D1B6-C083-448F-A10B-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA836B32-3B4D-41B1-AE9A-F33F7D4A33F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA836B32-3B4D-41B1-AE9A-F33F7D4A33F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA836B32-3B4D-41B1-AE9A-F33F7D4A33F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA836B32-3B4D-41B1-AE9A-F33F7D4A33F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B754101-E9F4-4A1F-884E-91D7CDDF7864}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B754101-E9F4-4A1F-884E-91D7CDDF7864}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B754101-E9F4-4A1F-884E-91D7CDDF7864}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B754101-E9F4-4A1F-884E-91D7CDDF7864}.Release|Any CPU.Build.0 = Release|Any CPU
		{714DA4A7-44F9-48C2-B068-C5174BEE38DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{714DA4A7-44F9-48C2-B068-C5174BEE38DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{714DA4A7-44F9-48C2-B068-C5174BEE38DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{714DA4A7-44F9-48C2-B068-C5174BEE38DB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{1999E008-9D62-45E0-A7D5-0A7B8632F349} = {EF1A183E-19E0-4E9F-83F6-98C14ECDC49F}
		{A8854AE3-3A25-47EC-B512-530E92AC67D8} = {D479BAC6-97D8-40F2-AF8F-01D373194203}
		{F5C156AF-9BF7-4E94-B383-396A0BCEEB17} = {5E22EB0D-8525-498D-ABB5-0247A68F93AC}
		{993462BD-1D81-4A4C-AA67-9357FE42D860} = {3CDDF38A-94FB-4FD1-B881-11F3D763465C}
		{07C7D1B6-C083-448F-A10B-************} = {D479BAC6-97D8-40F2-AF8F-01D373194203}
		{CA836B32-3B4D-41B1-AE9A-F33F7D4A33F2} = {D479BAC6-97D8-40F2-AF8F-01D373194203}
		{4B754101-E9F4-4A1F-884E-91D7CDDF7864} = {D479BAC6-97D8-40F2-AF8F-01D373194203}
		{714DA4A7-44F9-48C2-B068-C5174BEE38DB} = {4526FFF0-1E84-430D-80CC-669648D30B7E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C6FE7C61-8CDB-43F6-B052-DF3F28AA30D4}
	EndGlobalSection
EndGlobal
