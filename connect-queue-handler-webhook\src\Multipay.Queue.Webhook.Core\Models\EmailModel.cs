﻿using Multipay.Queue.Webhook.Core.Enums;
using System.Net.Mail;

namespace Multipay.Queue.Webhook.Core.Models
{
    public class ImagemRenderizadaModel
    {
        public string CaminhoImagem { get; set; } = String.Empty;
        public string IdImagemCorpoEmail { get; set; } = String.Empty;
    }

    public class DestinatarioModel
    {
        private readonly string _email;

        public DestinatarioModel(string email, TipoDestinatarioEnum tipoDestinatario)
        {
            this._email = email;
            this.tipo = tipoDestinatario;
        }

        public MailAddress email
        { get { return new MailAddress(this._email); } }
        public TipoDestinatarioEnum tipo { get; set; }
    }
}