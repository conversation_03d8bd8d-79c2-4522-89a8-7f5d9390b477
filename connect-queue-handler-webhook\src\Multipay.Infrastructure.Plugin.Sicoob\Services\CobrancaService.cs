﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Sicoob.Core;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Requests;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Responses;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.Repositories;
using Multipay.Service.Criptography;

namespace Multipay.Infrastructure.Plugin.Sicoob.Services
{
    public interface ICobrancaService
    {
        Task<ApiResponse> Add(AddUpdateCobrancaRequestModel model);

        Task<ApiResponse> Get(string clientId, string txid);

        Task<ApiResponse> GetImage(string clientId, string txid);

        Task<ApiResponse> Update(string txid, AddUpdateCobrancaRequestModel model);

        Task<bool> VerificarSeCobrancaPaga(string clientId, string txid, decimal? valorAReceber = null);
    }

    internal class CobrancaService : BaseConsumoService, ICobrancaService
    {
        private const string _domain = "cob";
        private readonly IEnvironment environment;

        public CobrancaService(
            ILogger<CobrancaService> logger,
            IEnvironment environment,
            IErroRepository erroRepository,
            ICentralizadorLogsService centralizadorLogsService,
            IAuthService authService, CriptoService criptoService) : base(logger, environment, erroRepository, centralizadorLogsService, authService, criptoService)
        {
            this.environment = environment;
        }

        public async Task<ApiResponse> Add(AddUpdateCobrancaRequestModel model)
        {
            var url = $"{_environment.PixURL}/{_domain}";

            return await PostAsync<AddUpdateCobrancaRequestModel, AddUpdateCobrancaResponseModel>(url, model.clientId, model, "Criar cobrança");
        }

        public async Task<ApiResponse> Get(string clientId, string txid)
        {
            var url = $"{_environment.PixURL}/{_domain}/{txid}";

            return await GetAsync<string, CobrancaResponseModel>(url, clientId, txid, "Consultar cobrança");
        }

        public async Task<ApiResponse> GetImage(string clientId, string txid)
        {
            var result = await Get(clientId, txid);

            if (result.Success)
            {
                //new ApiResponse(true, result.StatusCode, new QRCode().GerarQRCodeBase64(result.Result.Brcode));
            }

            return result;
        }

        public async Task<ApiResponse> Update(string txid, AddUpdateCobrancaRequestModel model)
        {
            var url = $"{_environment.PixURL}/{_domain}/{txid}";

            return await PutAsync<AddUpdateCobrancaRequestModel, AddUpdateCobrancaResponseModel>(url, model.clientId, model, "Atualizar cobrança");
        }

        public async Task<bool> VerificarSeCobrancaPaga(string clientId, string txid, decimal? valorAReceber = null)
        {
            var result = await Get(clientId, txid);

            CobrancaResponseModel? response = result?.Result;

            if (response == null)
                return false;

            var pix = response.PIX?.FirstOrDefault(item => !string.IsNullOrEmpty(item.EndToEndId) && (item.Devolucoes?.Any() ?? false) == false);

            if (pix == null)
                return false;

            if (valorAReceber != null)
                return Convert.ToDecimal(pix.Valor) >= valorAReceber;

            return true;
        }
    }
}