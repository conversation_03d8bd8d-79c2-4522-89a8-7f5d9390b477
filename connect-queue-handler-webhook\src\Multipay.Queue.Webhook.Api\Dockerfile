#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/Multipay.Queue.Webhook.Api/Multipay.Queue.Webhook.Api.csproj", "src/Multipay.Queue.Webhook.Api/"]
COPY ["src/Multipay.Queue.Webhook.Application/Multipay.Queue.Webhook.Application.csproj", "src/Multipay.Queue.Webhook.Application/"]
COPY ["src/Multipay.Infrastructure.Plugin.Sicoob/Multipay.Infrastructure.Plugin.Sicoob.csproj", "src/Multipay.Infrastructure.Plugin.Sicoob/"]
COPY ["src/Multipay.Queue.Webhook.Core.Commons/Multipay.Queue.Webhook.Core.Commons.csproj", "src/Multipay.Queue.Webhook.Core.Commons/"]
COPY ["src/Multipay.Queue.Webhook.Infrastructure/Multipay.Queue.Webhook.Infrastructure.csproj", "src/Multipay.Queue.Webhook.Infrastructure/"]
COPY ["src/Multipay.Queue.Webhook.Core/Multipay.Queue.Webhook.Core.csproj", "src/Multipay.Queue.Webhook.Core/"]
COPY ["src/Multipay.Queue.Webhook.Infrastructure.DAPetapoco/Multipay.Queue.Webhook.Infrastructure.DAPetapoco.csproj", "src/Multipay.Queue.Webhook.Infrastructure.DAPetapoco/"]
COPY ["src/Multipay.Queue.Webhook.Infrastructure.RabbitMq/Multipay.Queue.Webhook.Infrastructure.RabbitMq.csproj", "src/Multipay.Queue.Webhook.Infrastructure.RabbitMq/"]
COPY ["src/Multipay.Queue.NotificarCliente.Application/Multipay.Queue.NotificarCliente.Application.csproj", "src/Multipay.Queue.NotificarCliente.Application/"]
RUN dotnet restore "src/Multipay.Queue.Webhook.Api/Multipay.Queue.Webhook.Api.csproj"
COPY . .
WORKDIR "/src/src/Multipay.Queue.Webhook.Api"
RUN dotnet build "Multipay.Queue.Webhook.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Multipay.Queue.Webhook.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Multipay.Queue.Webhook.Api.dll"]