﻿
using Newtonsoft.Json;
using System.Text.Json.Serialization;

namespace Multipay.Queue.Webhook.Application.Models.Saques
{
    internal class SolicitacaoSaqueRecebidoAarinModel
    {
        [JsonProperty("e2EId")]
        public string EndToEndId { get; set; } = string.Empty;

        [JsonPropertyName("transactionId")]
        public string TransactionId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }
}
