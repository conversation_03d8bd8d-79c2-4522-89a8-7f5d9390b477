﻿using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    public interface IConstantesPetapocoRepository
    {
        IEnumerable<ConstanteModel> ListarConstantesPorTema(string tema);
    }

    internal class ConstantesPetapocoRepository : IConstantesPetapocoRepository
    {
        private readonly IDatabase database;

        public ConstantesPetapocoRepository(IDatabase database)
        {
            this.database = database;
        }

        public IEnumerable<ConstanteModel> ListarConstantesPorTema(string tema)
        {
            return database.QueryProc<ConstanteModel>("[Manager].[Select_ConstantsByTema]", new
            {
                tema
            });
        }
    }
}