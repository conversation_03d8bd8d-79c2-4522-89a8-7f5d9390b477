﻿using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels
{
    [BsonIgnoreExtraElements]
    public class WebhookFornecedorSolicitacaoSaqueModel : BaseModel
    {
        public WebhookFornecedorSolicitacaoSaqueModel(int idSolicitacaoSaque, string acao, string idWebhookFornecedor)
        {
            IdSolicitacaoSaque = idSolicitacaoSaque;
            Acao = acao;
            IdWebhookFornecedor = idWebhookFornecedor;
            DataCriacaoUtc = DateTime.UtcNow;
        }

        public int IdSolicitacaoSaque { get; private set; }
        public string Acao { get; private set; }
        public string IdWebhookFornecedor { get; private set; }
        public DateTime DataCriacaoUtc { get; private set; }
        public DateTime? DataAtualizacaoUtc { get; private set; }

        [BsonIgnore]
        public WebhookFornecedorModel? WebhookFornecedor { get; set; }
    }
}