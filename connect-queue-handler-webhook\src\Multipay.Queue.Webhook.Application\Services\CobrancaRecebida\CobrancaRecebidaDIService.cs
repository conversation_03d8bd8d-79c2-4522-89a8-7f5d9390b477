﻿using Multipay.Queue.Webhook.Application.Models;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaDIService : CobrancaRecebidaService<CobrancaRecebidaDIModel>
    {
        public CobrancaRecebidaDIService(
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.BANCODI)
        { }

        protected override IEnumerable<CobrancaRecebidaDIModel> ListPix => new List<CobrancaRecebidaDIModel>() { Parameters! };

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);

        protected override bool VerificarValorPagoMenorQueCobranca(decimal valorLiquido, decimal valorRecebido)
        {
            return !(valorLiquido <= valorRecebido || (valorLiquido >= valorRecebido && Parameters!.Paid_by_cripto_currency));
        }

        protected override decimal ObterValorPago(CobrancaRecebidaDIModel dadosPIX)
        {
            return Math.Round(Convert.ToDecimal(Parameters!.TotalPaid / 100.00M), 2);
        }

        protected override ValueTask<CobrancaModel?> ObterCobranca(CobrancaRecebidaDIModel dadosPIX)
        {
            return CobrancaService.ObterCobrancaPorCodigo(Parameters!.CustomId);
        }

        protected override string? ObterEndToEndId(CobrancaRecebidaDIModel dadosPIX)
        {
            return null;
        }
    }
}