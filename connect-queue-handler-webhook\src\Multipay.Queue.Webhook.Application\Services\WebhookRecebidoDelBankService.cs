﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.DelBank;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.DelBank;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;
using Newtonsoft.Json;
using Serilog.Context;

namespace Multipay.Queue.Webhook.Application.Services;

public class WebhookRecebidoDelBankService(ILogger<WebhookRecebidoDelBankService> logger) : IWebhookRecebidoService
{
    public TipoBanco TipoBanco => TipoBanco.DelBank;

    public Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
    {
        if (@event.Tipo == "pix/recebimento")
        {
            var service = serviceProvider.GetRequiredService<IMovimentacaoEntradaService>();

            return service.ProcessarRecebimentoPIX($"{@event.Data}", @event.IdWebhook);
        }

        if (@event.Tipo == "pix-indirect/confirmacao-recebimento")
        {
            var service = serviceProvider.GetRequiredService<CobrancaRecebidaDelBankService>();

            return service.Processar($"{@event.Data}", @event.IdWebhook);
        }

        if (@event.Tipo?.StartsWith("pix/pagamento") == true)
        {
            var service = serviceProvider.GetRequiredService<IMovimentacaoSaidaService>();

            return service.ProcessarPagamento($"{@event.Data}", @event.IdWebhook);
        }

        if (@event.Tipo?.StartsWith("pix-indirect/confirmacao-transferencia") == true)
        {
            var service = serviceProvider.GetRequiredService<IPagamentoConfirmadoDelBankService>();

            return service.ProcessarPagamento(@event);
        }

        if (@event.Tipo == "transferencia-interna")
        {
            var service = serviceProvider.GetRequiredService<IMovimentacaoSaidaTransfInternaService>();

            return service.ProcessarTransferenciaEntreContas(@event.IdWebhook, $"{@event.Data}".To<TransferenciaInternaRecebidoModel>()!);
        }

        if (@event.Tipo == "pix/estorno-saque")
        {
            var service = serviceProvider.GetRequiredService<INotificacaoEstornoSaqueService>();

            return service.ProcessarEstornoSaque($"{@event.Data}".To<EstornoRecebidoModel>());
        }

        if (@event.Tipo == "pix/estorno-deposito")
        {
            var service = serviceProvider.GetRequiredService<INotificacaoEstornoDepositoService>();

            return service.ProcessarEstornoDeposito($"{@event.Data}".To<EstornoRecebidoModel>());
        }

        logger.LogWarning("Mensagem não tratada. {Payload}", JsonConvert.SerializeObject(@event));

        return Task.FromResult(false);
    }
}