﻿using System.ComponentModel;

namespace Multipay.Queue.Webhook.Core.Commons.Constants
{
    public enum TipoBanco
    {
        [Description("BS2")]
        BS2 = 115,

        [Description("GENIAL")]
        Genial = 137,

        [Description("BMP")]
        BMP = 224,

        [Description("SICOOB")]
        SICOOB = 229,

        [Description("BANCODI")]
        BANCODI = 225,

        [Description("DELBANK")]
        DelBank = 232,

        [Description("DOCK")]
        Dock = 151,

        [Description("MKBANK")]
        MkBank = 235,

        [Description("SulCred")]
        SulCred = 135,

        [Description("Aarin")]
        BancoAarin = 238,

        [Description("AnspacePay")]
        AnspacePay = 249,

        [Description("Celcoin")]
        Celcoin = 231
    }

    public static class Moeda
    {
        public const byte IdMoedaBRL = 1;
        public const byte IdMoedaUSD = 2;
        public const byte IdMoedaEUR = 3;
        public const byte IdMoedaBTC = 4;
        public const byte IdMoedaETH = 5;

        public const string CodigoMoedaBRL = "BRL";
    }
}