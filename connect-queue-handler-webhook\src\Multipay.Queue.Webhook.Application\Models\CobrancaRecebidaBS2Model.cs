﻿using Multipay.Queue.Webhook.Core.Interfaces;

namespace Multipay.Queue.Webhook.Application.Models
{
    public class CobrancaRecebidaBS2Model
    {
        public PIXData[] PIX { get; set; } = Array.Empty<PIXData>();

        public class PIXData : IPIXData
        {
            public string EndToEndId { get; set; } = string.Empty;
            public string Txid { get; set; } = string.Empty;
            public string Valor { get; set; } = string.Empty;
            public string Horario { get; set; } = string.Empty;

            public PagadorData Pagador { get; set; } = new();

            public string CpfPagador
            {
                get
                {
                    if (!string.IsNullOrWhiteSpace(Pagador?.cpf))
                        return Pagador.cpf;

                    if (!string.IsNullOrWhiteSpace(Pagador?.cnpj))
                        return Pagador.cnpj;

                    return string.Empty;
                }
            }

            public string NomePagador => Pagador?.Nome ?? string.Empty;

            public string? IspbPagador => null;
            public string? NomeBancoPagador => null;
            public string? AgenciaBancoPagador => null;
            public string? ContaBancoPagador => null;
            public class PagadorData
            {
                public string cpf { get; set; } = string.Empty;
                public string cnpj { get; set; } = string.Empty;
                public string Nome { get; set; } = string.Empty;
            }

            public List<DevolucaoData>? Devolucoes { get; set; }

            public class DevolucaoData
            {
                public string Id { get; set; } = string.Empty;
                public string RtrId { get; set; } = string.Empty;
                public string Valor { get; set; } = string.Empty;
                public HorarioData Horario { get; set; } = new();

                public class HorarioData
                {
                    public string Solicitacao { get; set; } = string.Empty;
                    public string Liquidacao { get; set; } = string.Empty;
                }

                public string Status { get; set; } = string.Empty;
            }
        }
    }
}