﻿using Microsoft.Extensions.Configuration;

namespace Multipay.Queue.EnvioWebhookSaque.Core.Utils
{
    public class AmbienteHelp
    {
        private readonly IConfiguration configuration;

        public AmbienteHelp(IConfiguration configuration)
        {
            this.configuration = configuration;
        }

        public string? Ambiente => Environment.GetEnvironmentVariable("AMBIENTE") ?? configuration["Ambiente"];
        public string? Mongo_ConnectionString => Environment.GetEnvironmentVariable("MONGO_CONNECTIONSTRING") ?? configuration["MONGO_CONNECTIONSTRING"] ?? configuration["MongoStoreDatabaseSettings:ConnectionString"];

        //public string? Mongo_ConnectionString => configuration["MongoStoreDatabaseSettings:ConnectionString"];
        public string? RabbitMQ_Connection => Environment.GetEnvironmentVariable("RABBITMQ_CONNECTION") ?? configuration["RabbitMQSolicitacaoSaque:HostName"];

        public string? RabbitMQ_Password => configuration["RabbitMqSettings:Setup:EventBusPassword"];
        public string? RabbitMQ_Port => configuration["RabbitMqSettings:Setup:EventBusPort"];
        public string? RabbitMQ_RetryCount => configuration["RabbitMqSettings:Setup:EventBusRetryCount"];
        public string? RabbitMQ_Username => configuration["RabbitMqSettings:Setup:EventBusUserName"];
        public string? RabbitMQ_VirtualHost => configuration["RabbitMqSettings:Setup:EventBusVirtualHost"];
        public string? SqlServer_ConnectionString => Environment.GetEnvironmentVariable("SQLSERVER_CONNECTIONSTRING") ?? configuration["SQLSERVER_CONNECTIONSTRING"] ?? configuration.GetConnectionString("MultiPayDatabase");

        //public string? SqlServer_ConnectionString => configuration["ConnectionStrings:MultiPayDatabase"];
        public string? Tema => Environment.GetEnvironmentVariable("TEMA") ?? configuration["Tema"];

        public bool EnableSsl => configuration["RabbitMQ:AtivaSslRabbitMQ"] == "1";
    }
}