﻿using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Driver;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Setup;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Data
{
    internal class AnspaceMongoContext
    {
        public IMongoCollection<LogSolicitacaoSaqueModel> LogSolicitacaoSaque { get; }
        public IMongoCollection<EnvioWebhookSolicitacaoSaqueModel> EnvioWebhookSolicitacaoSaque { get; }
        public IMongoCollection<WebhookFornecedorSolicitacaoSaqueModel> WebhookFornecedorSolicitacaoSaque { get; }
        public IMongoCollection<WebhookFornecedorModel> WebhookFornecedor { get; }

        private IMongoDatabase mongoDatabase;

        public AnspaceMongoContext(IOptions<MongoStoreDatabaseSettings> storeDatabaseSettings)
        {
            var mongoClient = new MongoClient(storeDatabaseSettings.Value.ConnectionString);

            mongoDatabase = mongoClient.GetDatabase(storeDatabaseSettings.Value.DatabaseName);

            LogSolicitacaoSaque = mongoDatabase.GetCollection<LogSolicitacaoSaqueModel>(storeDatabaseSettings.Value.CollectionNames.LogSolicitacaoSaque);
            EnvioWebhookSolicitacaoSaque = mongoDatabase.GetCollection<EnvioWebhookSolicitacaoSaqueModel>(storeDatabaseSettings.Value.CollectionNames.EnvioWebhookSolicitacaoSaque);
            WebhookFornecedorSolicitacaoSaque = mongoDatabase.GetCollection<WebhookFornecedorSolicitacaoSaqueModel>(storeDatabaseSettings.Value.CollectionNames.WebhookFornecedorSolicitacaoSaque);
            WebhookFornecedor = mongoDatabase.GetCollection<WebhookFornecedorModel>(storeDatabaseSettings.Value.CollectionNames.WebhookFornecedor);
        }

        internal bool CheckMongoDBConnection()
        {
            try
            {
                var pingCommand = new BsonDocument { { "ping", 1 } };
                var pingResultLog = mongoDatabase.RunCommand<BsonDocument>(pingCommand);

                return pingResultLog["ok"] == 1;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}