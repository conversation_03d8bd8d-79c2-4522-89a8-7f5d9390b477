using Application.Configurations;
using Application.Services.Interfaces;
using Domain.Constants;
using MediatR;
using Microsoft.AspNetCore.Http;
using static Microsoft.AspNetCore.Http.Results;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Application.Handlers.BankBalance;

public class BankBalanceHandler
(
    ILogger<BankBalanceHandler> logger,
    ICredentialService credentialService,
    IBankBalanceService bankBalanceService,
    IOptions<ConnectPSPConfiguration> configuration) : IRequestHandler<BankBalanceCommand, IResult>
{
    public async Task<IResult> Handle(BankBalanceCommand request, CancellationToken cancellationToken)
    {
        var customerId = configuration.Value.CustomerId;

        var credentialResult = await credentialService.GetCredentialByCustomerId(customerId, cancellationToken);
        
        if (!credentialResult.Success)
        {
            logger.Error("Dados da credencial não encontrados para o cliente Id: {CustomerId}",  customerId);
            return NotFound(credentialResult.ErrorMessage);
        }        
        
        var credential = credentialResult.Value!;

        var bankBalanceResult = await bankBalanceService.ExecuteBankBalanceAsync(credential, cancellationToken);
        
        if (!bankBalanceResult.Success)
        {
            logger.Error("Erro ao executar API de monitoramento de saldos dos clientes");
            return BadGateway(MessagesConstants.ErrorExcutingBankBalance);
        }

        logger.Information("Monitoramento de saldos dos clientes executado com sucesso");        
        
        return Ok();
    }
}
