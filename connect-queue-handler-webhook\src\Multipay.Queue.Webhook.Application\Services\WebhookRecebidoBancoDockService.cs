﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services
{
    public class WebhookRecebidoBancoDockService : IWebhookRecebidoService
    {
        private readonly ILogger<WebhookRecebidoBancoDockService> logger;

        public WebhookRecebidoBancoDockService(ILogger<WebhookRecebidoBancoDockService> logger)
        {
            this.logger = logger;
        }

        public TipoBanco TipoBanco => TipoBanco.Dock;

        public Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
        {
            if (@event.Tipo == "pix/recebimento")
            {
                var service = serviceProvider.GetRequiredService<CobrancaRecebidaDockService>();
                return service.Processar($"{@event.Data}", @event.IdWebhook);
            }

            if (@event.Tipo == "pix/pagamento")
            {
                var service = serviceProvider.GetRequiredService<IPagamentoConfirmadoDockService>();
                return service.ProcessarPagamento(@event);
            }

            logger.LogWarning($"Mensagem não tratada. {@event}");

            return Task.FromResult(true);
        }
    }
}