﻿using Microsoft.Extensions.Configuration;

namespace Multipay.Queue.EnvioWebhookCobranca.Core.Utils
{
    public class AmbienteHelp
    {
        private readonly IConfiguration configuration;

        public AmbienteHelp(IConfiguration configuration)
        {
            this.configuration = configuration;
        }

        public string? Ambiente => Environment.GetEnvironmentVariable("AMBIENTE") ?? configuration["Ambiente"];
        public string? Mongo_ConnectionString => Environment.GetEnvironmentVariable("MONGO_CONNECTIONSTRING") ?? configuration["MONGO_CONNECTIONSTRING"];

        //public string? Mongo_ConnectionString => configuration["MongoStoreDatabaseSettings:ConnectionString"];
        public string? RabbitMQ_Connection => Environment.GetEnvironmentVariable("RABBITMQ_CONNECTION") ?? configuration["RabbitMQSolicitacaoSaque:HostName"];

        public string? RabbitMQ_Password => configuration["RabbitMQSolicitacaoSaque:Password"];
        public string? RabbitMQ_Port => Environment.GetEnvironmentVariable("RABBITMQ_PORT") ?? configuration["RabbitMQSolicitacaoSaque:EventBusPort"];
        public string? RabbitMQ_RetryCount => configuration["RabbitMQSolicitacaoSaque:EventBusRetryCount"];
        public string? RabbitMQ_Username => configuration["RabbitMQSolicitacaoSaque:UserName"];
        public string? RabbitMQ_VirtualHost => configuration["RabbitMQSolicitacaoSaque:VirtualHost"];
        public string? SqlServer_ConnectionString => Environment.GetEnvironmentVariable("SQLSERVER_CONNECTIONSTRING") ?? configuration["SQLSERVER_CONNECTIONSTRING"];

        //public string? SqlServer_ConnectionString => configuration["ConnectionStrings:MultiPayDatabase"];
        public string? Tema => Environment.GetEnvironmentVariable("TEMA") ?? configuration["Tema"];

        public bool EnableSsl => configuration["RabbitMQ:AtivaSslRabbitMQ"] == "1";
    }
}