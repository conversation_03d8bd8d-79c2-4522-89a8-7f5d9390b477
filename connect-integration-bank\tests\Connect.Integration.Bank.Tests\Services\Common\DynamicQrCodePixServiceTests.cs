using Connect.Integration.Bank.Services.Common.Interfaces;
using Connect.Integration.Bank.Services.Common.Models.Requests;
using Connect.Integration.Bank.Services.Common.Models.Responses;
using Connect.Integration.Bank.Strategy.Interfaces;
using Connect.Integration.Bank.Tests.Fixtures;
using FluentAssertions;

namespace Connect.Integration.Bank.Tests.Services.Common;

public class DynamicQrCodePixServiceTests : IClassFixture<ServicesFixture>
{
    private readonly IIntegrationBankService _sut;
    private readonly ServicesFixture _fixture;

    public DynamicQrCodePixServiceTests(ServicesFixture fixture)
    {
        _fixture = fixture;        
        _sut = fixture.IntegrationBankService;
    }

    [Fact]
    public async Task ShouldExecuteDelBankSuccess()
    {
        // Arrange
        var credential = _fixture.CredentialDelBankAccount;

        var value = 0.10M;
        var payerName = "PAYER NAME";
        var payerDocument = "***********";
        var receiverName = "TESTE";
        var message = "TESTE";
        var additionalInfos = new DynamicQrCodeRequest.AdditionalInformation[] { new()
        {
            Name = "Teste Info",
            Value = "Teste Info Value"
        }};

        var request = new DynamicQrCodeRequest(credential, null, value, payerName, payerDocument, receiverName, message, additionalInfos);        

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();        
        response.Result?.TxId.Should().Be(request.TxId);

        VerifyQrCodeResponse(request, response);
    }

    [Fact]
    public async Task ShouldExecuteCelcoincSuccess()
    {
        // Arrange
        var credential = _fixture.CredentialCelcoinAccount;

        var dictKey = "38A0078E-675D-4D09-8797-9CABA01D2AB5";
        var value = 0.10M;
        var payerName = "PAYER NAME";
        var payerDocument = "***********";
        var receiverName = "TESTE";
        var message = "TESTE";
        var additionalInfos = new DynamicQrCodeRequest.AdditionalInformation[] { new()
        {
            Name = "Teste Info",
            Value = "Teste Info Value"
        }};

        var request = new DynamicQrCodeRequest(credential, dictKey, value, payerName, payerDocument, receiverName, message, additionalInfos);        

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();        
        response.Result?.TxId.Should().Be(request.TxId);

        VerifyQrCodeResponse(request, response);
    }

    [Fact(Skip = "Sandbox is not auhuthorized")]
    public async Task ShouldExecuteAarinSuccess()
    {
        // Arrange
        var credential = _fixture.CredentialAarinAccount;

        var dictKey = "0b753863-898a-4987-b7ed-8fa0199ba04c";
        var value = 1.50M;
        var payerName = "Ariel Nepomuceno Cursino de Sena";
        var payerDocument = "***********";
        var receiverName = "TESTE";
        var message =  "teste cobrança";
        var additionalInfos = new DynamicQrCodeRequest.AdditionalInformation[] { new()
        {
            Name = "nome 1",
            Value = "valor 1"
        }};

        var request = new DynamicQrCodeRequest(credential, dictKey, value, payerName, payerDocument, receiverName, message, additionalInfos);

        // Act
        var response = await _sut.ExecuteAsync(request, default);

        // Assert
        response.Success.Should().BeTrue();

        response.Result?.TxId?.Should().Be(request.TxId);
        response.Result?.Id?.Should().NotBeNullOrEmpty();

        VerifyQrCodeResponse(request, response);
    }  

    private static void VerifyQrCodeResponse(DynamicQrCodeRequest request, IBaseResult<DynamicQrCodeResponse> response)
    {
        response.Result?.QrCode?.Payload?.Should().NotBeNullOrEmpty();
        response.Result?.QrCode?.Base64Image?.Should().NotBeNullOrEmpty();

        response.Result?.Value?.Should().Be(request.Value);        
        response.Result?.AdditionalInformations.Should().BeEquivalentTo(request.AdditionalInformations);
    }
}
