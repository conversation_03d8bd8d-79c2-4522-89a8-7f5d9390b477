﻿using Microsoft.Extensions.Logging;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    public interface IChavePIXContaBancariaFavorecidoClienteRepository
    {
        Task<bool> ExistePorIdValidacao(string v);

        Task MarcarRespostaChave(string chave, bool valida, string v);
    }

    internal class ChavePIXContaBancariaFavorecidoClienteRepository : IChavePIXContaBancariaFavorecidoClienteRepository
    {
        private readonly ILogger<UnitOfWork> logger;
        private readonly IDatabase database;

        public ChavePIXContaBancariaFavorecidoClienteRepository(ILogger<UnitOfWork> logger, IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public async Task<bool> ExistePorIdValidacao(string idValidacao)
        {
            try
            {
                var select = Sql.Builder
                    .Select("1")
                    .From("ChavePIXContaBancariaFavorecidoCliente")
                    .Where("IdValidacao=@0", idValidacao);
                using var reader = await database.QueryAsync<int>(select);

                return await reader.ReadAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(ChavePIXContaBancariaFavorecidoClienteRepository), database.LastCommand);
                //throw;
                return false;
            }
        }

        public async Task MarcarRespostaChave(string chave, bool valida, string origem)
        {
            try
            {
                await database.ExecuteNonQueryProcAsync("[Webhook].[Update_ChavePIXContaBancariaFavorecidoClienteRetorno]", new
                {
                    chave,
                    valida,
                    origem
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(ChavePIXContaBancariaFavorecidoClienteRepository), database.LastCommand);
                throw;
            }
        }
    }
}