{"version": 3, "targets": {"net8.0": {"Datadog.Trace/2.51.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "4.4.1"}, "compile": {"lib/net6.0/Datadog.Trace.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Datadog.Trace.dll": {"related": ".xml"}}}, "Datadog.Trace.Bundle/2.51.0": {"type": "package", "dependencies": {"Datadog.Trace": "2.51.0"}, "contentFiles": {"contentFiles/any/any/datadog/createLogPath.sh": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/createLogPath.sh"}, "contentFiles/any/any/datadog/dd-dotnet.cmd": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/dd-dotnet.cmd"}, "contentFiles/any/any/datadog/dd-dotnet.sh": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/dd-dotnet.sh"}, "contentFiles/any/any/datadog/linux-arm64/Datadog.Linux.ApiWrapper.x64.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-arm64/Datadog.Linux.ApiWrapper.x64.so"}, "contentFiles/any/any/datadog/linux-arm64/Datadog.Profiler.Native.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-arm64/Datadog.Profiler.Native.so"}, "contentFiles/any/any/datadog/linux-arm64/Datadog.Trace.ClrProfiler.Native.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-arm64/Datadog.Trace.ClrProfiler.Native.so"}, "contentFiles/any/any/datadog/linux-arm64/Datadog.Tracer.Native.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-arm64/Datadog.Tracer.Native.so"}, "contentFiles/any/any/datadog/linux-arm64/dd-dotnet": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-arm64/dd-dotnet"}, "contentFiles/any/any/datadog/linux-arm64/libddwaf.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-arm64/libddwaf.so"}, "contentFiles/any/any/datadog/linux-arm64/loader.conf": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-arm64/loader.conf"}, "contentFiles/any/any/datadog/linux-musl-x64/Datadog.Linux.ApiWrapper.x64.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-musl-x64/Datadog.Linux.ApiWrapper.x64.so"}, "contentFiles/any/any/datadog/linux-musl-x64/Datadog.Profiler.Native.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-musl-x64/Datadog.Profiler.Native.so"}, "contentFiles/any/any/datadog/linux-musl-x64/Datadog.Trace.ClrProfiler.Native.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-musl-x64/Datadog.Trace.ClrProfiler.Native.so"}, "contentFiles/any/any/datadog/linux-musl-x64/Datadog.Tracer.Native.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-musl-x64/Datadog.Tracer.Native.so"}, "contentFiles/any/any/datadog/linux-musl-x64/dd-dotnet": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-musl-x64/dd-dotnet"}, "contentFiles/any/any/datadog/linux-musl-x64/libddwaf.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-musl-x64/libddwaf.so"}, "contentFiles/any/any/datadog/linux-musl-x64/loader.conf": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-musl-x64/loader.conf"}, "contentFiles/any/any/datadog/linux-x64/Datadog.Linux.ApiWrapper.x64.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-x64/Datadog.Linux.ApiWrapper.x64.so"}, "contentFiles/any/any/datadog/linux-x64/Datadog.Profiler.Native.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-x64/Datadog.Profiler.Native.so"}, "contentFiles/any/any/datadog/linux-x64/Datadog.Trace.ClrProfiler.Native.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-x64/Datadog.Trace.ClrProfiler.Native.so"}, "contentFiles/any/any/datadog/linux-x64/Datadog.Tracer.Native.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-x64/Datadog.Tracer.Native.so"}, "contentFiles/any/any/datadog/linux-x64/dd-dotnet": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-x64/dd-dotnet"}, "contentFiles/any/any/datadog/linux-x64/libddwaf.so": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-x64/libddwaf.so"}, "contentFiles/any/any/datadog/linux-x64/loader.conf": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/linux-x64/loader.conf"}, "contentFiles/any/any/datadog/net461/Datadog.Trace.AspNet.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net461/Datadog.Trace.AspNet.dll"}, "contentFiles/any/any/datadog/net461/Datadog.Trace.AspNet.pdb": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net461/Datadog.Trace.AspNet.pdb"}, "contentFiles/any/any/datadog/net461/Datadog.Trace.MSBuild.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net461/Datadog.Trace.MSBuild.dll"}, "contentFiles/any/any/datadog/net461/Datadog.Trace.MSBuild.pdb": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net461/Datadog.Trace.MSBuild.pdb"}, "contentFiles/any/any/datadog/net461/Datadog.Trace.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net461/Datadog.Trace.dll"}, "contentFiles/any/any/datadog/net461/Datadog.Trace.pdb": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net461/Datadog.Trace.pdb"}, "contentFiles/any/any/datadog/net6.0/Datadog.Trace.MSBuild.deps.json": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net6.0/Datadog.Trace.MSBuild.deps.json"}, "contentFiles/any/any/datadog/net6.0/Datadog.Trace.MSBuild.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net6.0/Datadog.Trace.MSBuild.dll"}, "contentFiles/any/any/datadog/net6.0/Datadog.Trace.MSBuild.pdb": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net6.0/Datadog.Trace.MSBuild.pdb"}, "contentFiles/any/any/datadog/net6.0/Datadog.Trace.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net6.0/Datadog.Trace.dll"}, "contentFiles/any/any/datadog/net6.0/Datadog.Trace.pdb": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/net6.0/Datadog.Trace.pdb"}, "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.MSBuild.deps.json": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netcoreapp3.1/Datadog.Trace.MSBuild.deps.json"}, "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.MSBuild.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netcoreapp3.1/Datadog.Trace.MSBuild.dll"}, "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.MSBuild.pdb": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netcoreapp3.1/Datadog.Trace.MSBuild.pdb"}, "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netcoreapp3.1/Datadog.Trace.dll"}, "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.pdb": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netcoreapp3.1/Datadog.Trace.pdb"}, "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.MSBuild.deps.json": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/Datadog.Trace.MSBuild.deps.json"}, "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.MSBuild.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/Datadog.Trace.MSBuild.dll"}, "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.MSBuild.pdb": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/Datadog.Trace.MSBuild.pdb"}, "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/Datadog.Trace.dll"}, "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.pdb": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/Datadog.Trace.pdb"}, "contentFiles/any/any/datadog/netstandard2.0/System.Diagnostics.DiagnosticSource.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/System.Diagnostics.DiagnosticSource.dll"}, "contentFiles/any/any/datadog/netstandard2.0/System.Reflection.Emit.ILGeneration.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/System.Reflection.Emit.ILGeneration.dll"}, "contentFiles/any/any/datadog/netstandard2.0/System.Reflection.Emit.Lightweight.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/System.Reflection.Emit.Lightweight.dll"}, "contentFiles/any/any/datadog/netstandard2.0/System.Reflection.Emit.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/System.Reflection.Emit.dll"}, "contentFiles/any/any/datadog/netstandard2.0/System.Threading.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/netstandard2.0/System.Threading.dll"}, "contentFiles/any/any/datadog/osx/Datadog.Trace.ClrProfiler.Native.dylib": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/osx/Datadog.Trace.ClrProfiler.Native.dylib"}, "contentFiles/any/any/datadog/osx/Datadog.Tracer.Native.dylib": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/osx/Datadog.Tracer.Native.dylib"}, "contentFiles/any/any/datadog/osx/libddwaf.dylib": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/osx/libddwaf.dylib"}, "contentFiles/any/any/datadog/osx/loader.conf": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/osx/loader.conf"}, "contentFiles/any/any/datadog/win-x64/Datadog.Profiler.Native.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x64/Datadog.Profiler.Native.dll"}, "contentFiles/any/any/datadog/win-x64/Datadog.Trace.ClrProfiler.Native.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x64/Datadog.Trace.ClrProfiler.Native.dll"}, "contentFiles/any/any/datadog/win-x64/Datadog.Tracer.Native.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x64/Datadog.Tracer.Native.dll"}, "contentFiles/any/any/datadog/win-x64/dd-dotnet.exe": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x64/dd-dotnet.exe"}, "contentFiles/any/any/datadog/win-x64/ddwaf.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x64/ddwaf.dll"}, "contentFiles/any/any/datadog/win-x64/loader.conf": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x64/loader.conf"}, "contentFiles/any/any/datadog/win-x86/Datadog.Profiler.Native.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x86/Datadog.Profiler.Native.dll"}, "contentFiles/any/any/datadog/win-x86/Datadog.Trace.ClrProfiler.Native.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x86/Datadog.Trace.ClrProfiler.Native.dll"}, "contentFiles/any/any/datadog/win-x86/Datadog.Tracer.Native.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x86/Datadog.Tracer.Native.dll"}, "contentFiles/any/any/datadog/win-x86/ddwaf.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x86/ddwaf.dll"}, "contentFiles/any/any/datadog/win-x86/loader.conf": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": true, "outputPath": "datadog/win-x86/loader.conf"}}}, "Microsoft.Extensions.Configuration/2.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/2.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Primitives": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "Serilog/4.0.0": {"type": "package", "compile": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}}, "Serilog.Enrichers.Environment/3.0.1": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"related": ".xml"}}}, "Serilog.Exceptions/8.4.0": {"type": "package", "dependencies": {"Serilog": "2.8.0", "System.Reflection.TypeExtensions": "4.7.0"}, "compile": {"lib/net6.0/Serilog.Exceptions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Serilog.Exceptions.dll": {"related": ".pdb;.xml"}}}, "Serilog.Exceptions.SqlServer/8.4.0": {"type": "package", "dependencies": {"Serilog.Exceptions": "8.4.0", "System.Data.SqlClient": "4.8.1"}, "compile": {"lib/net6.0/Serilog.Exceptions.SqlServer.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Serilog.Exceptions.SqlServer.dll": {"related": ".pdb;.xml"}}}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.Datadog.Logs/0.5.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1", "Serilog": "2.9.0", "Serilog.Sinks.PeriodicBatching": "3.0.0"}, "compile": {"lib/net5.0/Serilog.Sinks.Datadog.Logs.dll": {}}, "runtime": {"lib/net5.0/Serilog.Sinks.Datadog.Logs.dll": {}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "Serilog.Sinks.Map/1.0.2": {"type": "package", "dependencies": {"Serilog": "2.8.0"}, "compile": {"lib/net5.0/Serilog.Sinks.Map.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.Map.dll": {"related": ".xml"}}}, "Serilog.Sinks.PeriodicBatching/3.0.0": {"type": "package", "dependencies": {"Serilog": "2.0.0"}, "compile": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"related": ".xml"}}}, "System.Data.SqlClient/4.8.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.DiagnosticSource/4.4.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"Datadog.Trace/2.51.0": {"sha512": "P/i0vbMWswn/Ru150lj3JkUi7xCUhKNIRJ+LVv+reiP12+P76OqrmMyZLLVCT/t7ZkTfZ5IITR7gMl5hHO+wpg==", "type": "package", "path": "datadog.trace/2.51.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "datadog.trace.2.51.0.nupkg.sha512", "datadog.trace.nuspec", "lib/net461/Datadog.Trace.dll", "lib/net461/Datadog.Trace.xml", "lib/net6.0/Datadog.Trace.dll", "lib/net6.0/Datadog.Trace.xml", "lib/netcoreapp3.1/Datadog.Trace.dll", "lib/netcoreapp3.1/Datadog.Trace.xml", "lib/netstandard2.0/Datadog.Trace.dll", "lib/netstandard2.0/Datadog.Trace.xml", "packageIcon.png"]}, "Datadog.Trace.Bundle/2.51.0": {"sha512": "vLdX7rukUbpLErH4C+Usfox0oMhNq0RLSG2l0C3meAJalKkqe/KC3NUD3nCKGEVWp/hyLto897hxgChUAc/bJA==", "type": "package", "path": "datadog.trace.bundle/2.51.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "content/datadog/createLogPath.sh", "content/datadog/dd-dotnet.cmd", "content/datadog/dd-dotnet.sh", "content/datadog/linux-arm64/Datadog.Linux.ApiWrapper.x64.so", "content/datadog/linux-arm64/Datadog.Profiler.Native.so", "content/datadog/linux-arm64/Datadog.Trace.ClrProfiler.Native.so", "content/datadog/linux-arm64/Datadog.Tracer.Native.so", "content/datadog/linux-arm64/dd-dotnet", "content/datadog/linux-arm64/libddwaf.so", "content/datadog/linux-arm64/loader.conf", "content/datadog/linux-musl-x64/Datadog.Linux.ApiWrapper.x64.so", "content/datadog/linux-musl-x64/Datadog.Profiler.Native.so", "content/datadog/linux-musl-x64/Datadog.Trace.ClrProfiler.Native.so", "content/datadog/linux-musl-x64/Datadog.Tracer.Native.so", "content/datadog/linux-musl-x64/dd-dotnet", "content/datadog/linux-musl-x64/libddwaf.so", "content/datadog/linux-musl-x64/loader.conf", "content/datadog/linux-x64/Datadog.Linux.ApiWrapper.x64.so", "content/datadog/linux-x64/Datadog.Profiler.Native.so", "content/datadog/linux-x64/Datadog.Trace.ClrProfiler.Native.so", "content/datadog/linux-x64/Datadog.Tracer.Native.so", "content/datadog/linux-x64/dd-dotnet", "content/datadog/linux-x64/libddwaf.so", "content/datadog/linux-x64/loader.conf", "content/datadog/net461/Datadog.Trace.AspNet.dll", "content/datadog/net461/Datadog.Trace.AspNet.pdb", "content/datadog/net461/Datadog.Trace.MSBuild.dll", "content/datadog/net461/Datadog.Trace.MSBuild.pdb", "content/datadog/net461/Datadog.Trace.dll", "content/datadog/net461/Datadog.Trace.pdb", "content/datadog/net6.0/Datadog.Trace.MSBuild.deps.json", "content/datadog/net6.0/Datadog.Trace.MSBuild.dll", "content/datadog/net6.0/Datadog.Trace.MSBuild.pdb", "content/datadog/net6.0/Datadog.Trace.dll", "content/datadog/net6.0/Datadog.Trace.pdb", "content/datadog/netcoreapp3.1/Datadog.Trace.MSBuild.deps.json", "content/datadog/netcoreapp3.1/Datadog.Trace.MSBuild.dll", "content/datadog/netcoreapp3.1/Datadog.Trace.MSBuild.pdb", "content/datadog/netcoreapp3.1/Datadog.Trace.dll", "content/datadog/netcoreapp3.1/Datadog.Trace.pdb", "content/datadog/netstandard2.0/Datadog.Trace.MSBuild.deps.json", "content/datadog/netstandard2.0/Datadog.Trace.MSBuild.dll", "content/datadog/netstandard2.0/Datadog.Trace.MSBuild.pdb", "content/datadog/netstandard2.0/Datadog.Trace.dll", "content/datadog/netstandard2.0/Datadog.Trace.pdb", "content/datadog/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "content/datadog/netstandard2.0/System.Reflection.Emit.ILGeneration.dll", "content/datadog/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "content/datadog/netstandard2.0/System.Reflection.Emit.dll", "content/datadog/netstandard2.0/System.Threading.dll", "content/datadog/osx/Datadog.Trace.ClrProfiler.Native.dylib", "content/datadog/osx/Datadog.Tracer.Native.dylib", "content/datadog/osx/libddwaf.dylib", "content/datadog/osx/loader.conf", "content/datadog/win-x64/Datadog.Profiler.Native.dll", "content/datadog/win-x64/Datadog.Trace.ClrProfiler.Native.dll", "content/datadog/win-x64/Datadog.Tracer.Native.dll", "content/datadog/win-x64/dd-dotnet.exe", "content/datadog/win-x64/ddwaf.dll", "content/datadog/win-x64/loader.conf", "content/datadog/win-x86/Datadog.Profiler.Native.dll", "content/datadog/win-x86/Datadog.Trace.ClrProfiler.Native.dll", "content/datadog/win-x86/Datadog.Tracer.Native.dll", "content/datadog/win-x86/ddwaf.dll", "content/datadog/win-x86/loader.conf", "contentFiles/any/any/datadog/createLogPath.sh", "contentFiles/any/any/datadog/dd-dotnet.cmd", "contentFiles/any/any/datadog/dd-dotnet.sh", "contentFiles/any/any/datadog/linux-arm64/Datadog.Linux.ApiWrapper.x64.so", "contentFiles/any/any/datadog/linux-arm64/Datadog.Profiler.Native.so", "contentFiles/any/any/datadog/linux-arm64/Datadog.Trace.ClrProfiler.Native.so", "contentFiles/any/any/datadog/linux-arm64/Datadog.Tracer.Native.so", "contentFiles/any/any/datadog/linux-arm64/dd-dotnet", "contentFiles/any/any/datadog/linux-arm64/libddwaf.so", "contentFiles/any/any/datadog/linux-arm64/loader.conf", "contentFiles/any/any/datadog/linux-musl-x64/Datadog.Linux.ApiWrapper.x64.so", "contentFiles/any/any/datadog/linux-musl-x64/Datadog.Profiler.Native.so", "contentFiles/any/any/datadog/linux-musl-x64/Datadog.Trace.ClrProfiler.Native.so", "contentFiles/any/any/datadog/linux-musl-x64/Datadog.Tracer.Native.so", "contentFiles/any/any/datadog/linux-musl-x64/dd-dotnet", "contentFiles/any/any/datadog/linux-musl-x64/libddwaf.so", "contentFiles/any/any/datadog/linux-musl-x64/loader.conf", "contentFiles/any/any/datadog/linux-x64/Datadog.Linux.ApiWrapper.x64.so", "contentFiles/any/any/datadog/linux-x64/Datadog.Profiler.Native.so", "contentFiles/any/any/datadog/linux-x64/Datadog.Trace.ClrProfiler.Native.so", "contentFiles/any/any/datadog/linux-x64/Datadog.Tracer.Native.so", "contentFiles/any/any/datadog/linux-x64/dd-dotnet", "contentFiles/any/any/datadog/linux-x64/libddwaf.so", "contentFiles/any/any/datadog/linux-x64/loader.conf", "contentFiles/any/any/datadog/net461/Datadog.Trace.AspNet.dll", "contentFiles/any/any/datadog/net461/Datadog.Trace.AspNet.pdb", "contentFiles/any/any/datadog/net461/Datadog.Trace.MSBuild.dll", "contentFiles/any/any/datadog/net461/Datadog.Trace.MSBuild.pdb", "contentFiles/any/any/datadog/net461/Datadog.Trace.dll", "contentFiles/any/any/datadog/net461/Datadog.Trace.pdb", "contentFiles/any/any/datadog/net6.0/Datadog.Trace.MSBuild.deps.json", "contentFiles/any/any/datadog/net6.0/Datadog.Trace.MSBuild.dll", "contentFiles/any/any/datadog/net6.0/Datadog.Trace.MSBuild.pdb", "contentFiles/any/any/datadog/net6.0/Datadog.Trace.dll", "contentFiles/any/any/datadog/net6.0/Datadog.Trace.pdb", "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.MSBuild.deps.json", "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.MSBuild.dll", "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.MSBuild.pdb", "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.dll", "contentFiles/any/any/datadog/netcoreapp3.1/Datadog.Trace.pdb", "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.MSBuild.deps.json", "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.MSBuild.dll", "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.MSBuild.pdb", "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.dll", "contentFiles/any/any/datadog/netstandard2.0/Datadog.Trace.pdb", "contentFiles/any/any/datadog/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "contentFiles/any/any/datadog/netstandard2.0/System.Reflection.Emit.ILGeneration.dll", "contentFiles/any/any/datadog/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "contentFiles/any/any/datadog/netstandard2.0/System.Reflection.Emit.dll", "contentFiles/any/any/datadog/netstandard2.0/System.Threading.dll", "contentFiles/any/any/datadog/osx/Datadog.Trace.ClrProfiler.Native.dylib", "contentFiles/any/any/datadog/osx/Datadog.Tracer.Native.dylib", "contentFiles/any/any/datadog/osx/libddwaf.dylib", "contentFiles/any/any/datadog/osx/loader.conf", "contentFiles/any/any/datadog/win-x64/Datadog.Profiler.Native.dll", "contentFiles/any/any/datadog/win-x64/Datadog.Trace.ClrProfiler.Native.dll", "contentFiles/any/any/datadog/win-x64/Datadog.Tracer.Native.dll", "contentFiles/any/any/datadog/win-x64/dd-dotnet.exe", "contentFiles/any/any/datadog/win-x64/ddwaf.dll", "contentFiles/any/any/datadog/win-x64/loader.conf", "contentFiles/any/any/datadog/win-x86/Datadog.Profiler.Native.dll", "contentFiles/any/any/datadog/win-x86/Datadog.Trace.ClrProfiler.Native.dll", "contentFiles/any/any/datadog/win-x86/Datadog.Tracer.Native.dll", "contentFiles/any/any/datadog/win-x86/ddwaf.dll", "contentFiles/any/any/datadog/win-x86/loader.conf", "datadog.trace.bundle.2.51.0.nupkg.sha512", "datadog.trace.bundle.nuspec", "packageIcon.png"]}, "Microsoft.Extensions.Configuration/2.1.1": {"sha512": "LjVKO6P2y52c5ZhTLX/w8zc5H4Y3J/LJsgqTBj49TtFq/hAtVNue/WA0F6/7GMY90xhD7K0MDZ4qpOeWXbLvzg==", "type": "package", "path": "microsoft.extensions.configuration/2.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.2.1.1.nupkg.sha512", "microsoft.extensions.configuration.nuspec"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"sha512": "fcLCTS03poWE4v9tSNBr3pWn0QwGgAn1vzqHXlXgvqZeOc7LvQNzaWcKRQZTdEc3+YhQKwMsOtm3VKSA2aWQ8w==", "type": "package", "path": "microsoft.extensions.configuration.binder/2.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.2.1.1.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {"sha512": "MgYpU5cwZohUMKKg3sbPhvGG+eAZ/59E9UwPwlrUkyXU+PGzqwZg9yyQNjhxuAWmoNoFReoemeCku50prYSGzA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/2.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.2.1.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec"]}, "Microsoft.Extensions.Options/2.1.1": {"sha512": "V7lXCU78lAbzaulCGFKojcCyG8RTJicEbiBkPJjFqiqXwndEBBIehdXRMWEVU3UtzQ1yDvphiWUL9th6/4gJ7w==", "type": "package", "path": "microsoft.extensions.options/2.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.2.1.1.nupkg.sha512", "microsoft.extensions.options.nuspec"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"sha512": "NpGh3Y/VOBs6hvjKHMsdbtrvGvMO+cBqZ7YT/Rc4iFy0C4ogSnl1lBAq69L1LS6gzlwDBZDZ7WcvzSDzk5zfzA==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/2.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.2.1.1.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Serilog/4.0.0": {"sha512": "2jDkUrSh5EofOp7Lx5Zgy0EB+7hXjjxE2ktTb1WVQmU00lDACR2TdROGKU0K1pDTBSJBN1PqgYpgOZF8mL7NJw==", "type": "package", "path": "serilog/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net8.0/Serilog.dll", "lib/net8.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "serilog.4.0.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.Enrichers.Environment/3.0.1": {"sha512": "9BqCE4C9FF+/rJb/CsQwe7oVf44xqkOvMwX//CUxvUR25lFL4tSS6iuxE5eW07quby1BAyAEP+vM6TWsnT3iqw==", "type": "package", "path": "serilog.enrichers.environment/3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Enrichers.Environment.dll", "lib/net462/Serilog.Enrichers.Environment.xml", "lib/net471/Serilog.Enrichers.Environment.dll", "lib/net471/Serilog.Enrichers.Environment.xml", "lib/net6.0/Serilog.Enrichers.Environment.dll", "lib/net6.0/Serilog.Enrichers.Environment.xml", "lib/net8.0/Serilog.Enrichers.Environment.dll", "lib/net8.0/Serilog.Enrichers.Environment.xml", "lib/netstandard2.0/Serilog.Enrichers.Environment.dll", "lib/netstandard2.0/Serilog.Enrichers.Environment.xml", "serilog-enricher-nuget.png", "serilog.enrichers.environment.3.0.1.nupkg.sha512", "serilog.enrichers.environment.nuspec"]}, "Serilog.Exceptions/8.4.0": {"sha512": "nc/+hUw3lsdo0zCj0KMIybAu7perMx79vu72w0za9Nsi6mWyNkGXxYxakAjWB7nEmYL6zdmhEQRB4oJ2ALUeug==", "type": "package", "path": "serilog.exceptions/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net461/Serilog.Exceptions.dll", "lib/net461/Serilog.Exceptions.pdb", "lib/net461/Serilog.Exceptions.xml", "lib/net472/Serilog.Exceptions.dll", "lib/net472/Serilog.Exceptions.pdb", "lib/net472/Serilog.Exceptions.xml", "lib/net5.0/Serilog.Exceptions.dll", "lib/net5.0/Serilog.Exceptions.pdb", "lib/net5.0/Serilog.Exceptions.xml", "lib/net6.0/Serilog.Exceptions.dll", "lib/net6.0/Serilog.Exceptions.pdb", "lib/net6.0/Serilog.Exceptions.xml", "lib/netstandard1.3/Serilog.Exceptions.dll", "lib/netstandard1.3/Serilog.Exceptions.pdb", "lib/netstandard1.3/Serilog.Exceptions.xml", "lib/netstandard2.0/Serilog.Exceptions.dll", "lib/netstandard2.0/Serilog.Exceptions.pdb", "lib/netstandard2.0/Serilog.Exceptions.xml", "lib/netstandard2.1/Serilog.Exceptions.dll", "lib/netstandard2.1/Serilog.Exceptions.pdb", "lib/netstandard2.1/Serilog.Exceptions.xml", "serilog.exceptions.8.4.0.nupkg.sha512", "serilog.exceptions.nuspec"]}, "Serilog.Exceptions.SqlServer/8.4.0": {"sha512": "8VGHQjHabgyEOrY0pP6LzkFY2dvavV44Bu8jZD5XN8mxC/CvoeothQdzmVp9E62D1YOO1oJJVkTbHEjeRvkJXw==", "type": "package", "path": "serilog.exceptions.sqlserver/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net461/Serilog.Exceptions.SqlServer.dll", "lib/net461/Serilog.Exceptions.SqlServer.pdb", "lib/net461/Serilog.Exceptions.SqlServer.xml", "lib/net472/Serilog.Exceptions.SqlServer.dll", "lib/net472/Serilog.Exceptions.SqlServer.pdb", "lib/net472/Serilog.Exceptions.SqlServer.xml", "lib/net5.0/Serilog.Exceptions.SqlServer.dll", "lib/net5.0/Serilog.Exceptions.SqlServer.pdb", "lib/net5.0/Serilog.Exceptions.SqlServer.xml", "lib/net6.0/Serilog.Exceptions.SqlServer.dll", "lib/net6.0/Serilog.Exceptions.SqlServer.pdb", "lib/net6.0/Serilog.Exceptions.SqlServer.xml", "lib/netstandard1.3/Serilog.Exceptions.SqlServer.dll", "lib/netstandard1.3/Serilog.Exceptions.SqlServer.pdb", "lib/netstandard1.3/Serilog.Exceptions.SqlServer.xml", "lib/netstandard2.0/Serilog.Exceptions.SqlServer.dll", "lib/netstandard2.0/Serilog.Exceptions.SqlServer.pdb", "lib/netstandard2.0/Serilog.Exceptions.SqlServer.xml", "lib/netstandard2.1/Serilog.Exceptions.SqlServer.dll", "lib/netstandard2.1/Serilog.Exceptions.SqlServer.pdb", "lib/netstandard2.1/Serilog.Exceptions.SqlServer.xml", "serilog.exceptions.sqlserver.8.4.0.nupkg.sha512", "serilog.exceptions.sqlserver.nuspec"]}, "Serilog.Sinks.Console/6.0.0": {"sha512": "fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "type": "package", "path": "serilog.sinks.console/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Console.dll", "lib/net462/Serilog.Sinks.Console.xml", "lib/net471/Serilog.Sinks.Console.dll", "lib/net471/Serilog.Sinks.Console.xml", "lib/net6.0/Serilog.Sinks.Console.dll", "lib/net6.0/Serilog.Sinks.Console.xml", "lib/net8.0/Serilog.Sinks.Console.dll", "lib/net8.0/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.6.0.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.Datadog.Logs/0.5.2": {"sha512": "VOH2rbU+9Cx9GUUOUv2wUyMZSzB2mC/QxkE08ByfAptYoy2UI4pz2LCq5ynGuJYNsozhB6EejvuJj3lT64iUmw==", "type": "package", "path": "serilog.sinks.datadog.logs/0.5.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Serilog.Sinks.Datadog.Logs.dll", "lib/net461/Serilog.Sinks.Datadog.Logs.dll", "lib/net472/Serilog.Sinks.Datadog.Logs.dll", "lib/net5.0/Serilog.Sinks.Datadog.Logs.dll", "lib/netstandard1.3/Serilog.Sinks.Datadog.Logs.dll", "lib/netstandard2.0/Serilog.Sinks.Datadog.Logs.dll", "serilog.sinks.datadog.logs.0.5.2.nupkg.sha512", "serilog.sinks.datadog.logs.nuspec"]}, "Serilog.Sinks.File/5.0.0": {"sha512": "uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "type": "package", "path": "serilog.sinks.file/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net45/Serilog.Sinks.File.dll", "lib/net45/Serilog.Sinks.File.pdb", "lib/net45/Serilog.Sinks.File.xml", "lib/net5.0/Serilog.Sinks.File.dll", "lib/net5.0/Serilog.Sinks.File.pdb", "lib/net5.0/Serilog.Sinks.File.xml", "lib/netstandard1.3/Serilog.Sinks.File.dll", "lib/netstandard1.3/Serilog.Sinks.File.pdb", "lib/netstandard1.3/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.pdb", "lib/netstandard2.0/Serilog.Sinks.File.xml", "lib/netstandard2.1/Serilog.Sinks.File.dll", "lib/netstandard2.1/Serilog.Sinks.File.pdb", "lib/netstandard2.1/Serilog.Sinks.File.xml", "serilog.sinks.file.5.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "Serilog.Sinks.Map/1.0.2": {"sha512": "JbPBAeD5hxUQw8TZg3FlOnqVsSu1269nvqFm5DQ7hc+AmsB+hItl+zMSDphMbPJXjL8KdpMRSWNkGi7zTKRmCA==", "type": "package", "path": "serilog.sinks.map/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net5.0/Serilog.Sinks.Map.dll", "lib/net5.0/Serilog.Sinks.Map.xml", "lib/netstandard1.0/Serilog.Sinks.Map.dll", "lib/netstandard1.0/Serilog.Sinks.Map.xml", "lib/netstandard2.0/Serilog.Sinks.Map.dll", "lib/netstandard2.0/Serilog.Sinks.Map.xml", "serilog.sinks.map.1.0.2.nupkg.sha512", "serilog.sinks.map.nuspec"]}, "Serilog.Sinks.PeriodicBatching/3.0.0": {"sha512": "rgXWamyu3AMpswhJs8T/c5OKRhLDWYNt6it/6q328pl5K8rBLv79lZ9qJiYksoClnZNw9/omNfSFZ4RuH6qx9Q==", "type": "package", "path": "serilog.sinks.periodicbatching/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.PeriodicBatching.dll", "lib/net45/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard1.1/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard1.1/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard1.2/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard1.2/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard2.0/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard2.0/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.xml", "serilog.sinks.periodicbatching.3.0.0.nupkg.sha512", "serilog.sinks.periodicbatching.nuspec"]}, "System.Data.SqlClient/4.8.1": {"sha512": "HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "type": "package", "path": "system.data.sqlclient/4.8.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.1.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/4.4.1": {"sha512": "U/KcC19fyLsPN1GLmeU2zQq15MMVcPwMOYPADVo1+WIoJpvMHxrzvl+BLLZwTEZSneGwaPFZ0aWr0nJ7B7LSdA==", "type": "package", "path": "system.diagnostics.diagnosticsource/4.4.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "ref/netcoreapp2.0/_._", "system.diagnostics.diagnosticsource.4.4.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.TypeExtensions/4.7.0": {"sha512": "VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "type": "package", "path": "system.reflection.typeextensions/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net461/System.Reflection.TypeExtensions.dll", "lib/net461/System.Reflection.TypeExtensions.xml", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netcoreapp1.0/System.Reflection.TypeExtensions.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/System.Reflection.TypeExtensions.dll", "lib/netstandard1.3/System.Reflection.TypeExtensions.xml", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.xml", "lib/netstandard2.0/System.Reflection.TypeExtensions.dll", "lib/netstandard2.0/System.Reflection.TypeExtensions.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net461/System.Reflection.TypeExtensions.dll", "ref/net461/System.Reflection.TypeExtensions.xml", "ref/net472/System.Reflection.TypeExtensions.dll", "ref/net472/System.Reflection.TypeExtensions.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard2.0/System.Reflection.TypeExtensions.dll", "ref/netstandard2.0/System.Reflection.TypeExtensions.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "runtimes/aot/lib/uap10.0.16299/_._", "system.reflection.typeextensions.4.7.0.nupkg.sha512", "system.reflection.typeextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net8.0": ["Datadog.Trace >= 2.51.0", "Datadog.Trace.Bundle >= 2.51.0", "Microsoft.Extensions.Configuration.Abstractions >= 8.0.0", "Newtonsoft.Json >= 13.0.3", "Serilog >= 4.0.0", "Serilog.Enrichers.Environment >= 3.0.1", "Serilog.Exceptions >= 8.4.0", "Serilog.Exceptions.SqlServer >= 8.4.0", "Serilog.Sinks.Console >= 6.0.0", "Serilog.Sinks.Datadog.Logs >= 0.5.2", "Serilog.Sinks.File >= 5.0.0", "Serilog.Sinks.Map >= 1.0.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj", "projectName": "Multipay.Queue.EnvioWebhookSaque.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Datadog.Trace": {"target": "Package", "version": "[2.51.0, )"}, "Datadog.Trace.Bundle": {"target": "Package", "version": "[2.51.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Exceptions": {"target": "Package", "version": "[8.4.0, )"}, "Serilog.Exceptions.SqlServer": {"target": "Package", "version": "[8.4.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.Datadog.Logs": {"target": "Package", "version": "[0.5.2, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.Map": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}