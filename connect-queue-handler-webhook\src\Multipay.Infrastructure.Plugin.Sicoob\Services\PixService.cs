﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Sicoob.Core;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Requests;
using Multipay.Infrastructure.Plugin.Sicoob.Models.Responses;
using Multipay.Queue.Webhook.Core.Commons.ExtensionsMethods;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.Repositories;
using Multipay.Service.Criptography;

namespace Multipay.Infrastructure.Plugin.Sicoob.Services
{
    public interface IPixService
    {
        Task<ApiResponse> Get(string clientId, string endToEndId);

        Task<ApiResponse> GetAll(string clientId, DateTime inicio, DateTime fim, PixGetAllRequestModel model);

        Task<ApiResponse> GetDevolucao(string clientId, string endToEndId, string txId);

        Task<ApiResponse> SolicitarDevolucao(string clientId, string endToEndId, string txId, PixSolicitarDevolucaoRequestModel model);
    }

    internal class PixService : BaseConsumoService, IPixService
    {
        private const string _domain = "pix";

        public PixService(
            ILogger<PixService> logger,
            IEnvironment environment,
            IErroRepository erroRepository,
            ICentralizadorLogsService centralizadorLogsService,
            IAuthService authService,
            CriptoService criptoService)
            : base(logger, environment, erroRepository, centralizadorLogsService, authService, criptoService)
        { }

        public async Task<ApiResponse> Get(string clientId, string endToEndId)
        {
            var url = $"{_environment.PixURL}/{_domain}/{endToEndId}";
            var model = new { endToEndId };

            return await GetAsync<dynamic, PixResponseModel>(url, clientId, model, "Consultar PIX");
        }

        public async Task<ApiResponse> GetAll(string clientId, DateTime inicio, DateTime fim, PixGetAllRequestModel model)
        {
            var queryString = model.ToQueryString();
            queryString.Add("inicio", inicio.ToString("s") + "Z");
            queryString.Add("fim", fim.ToEndOfTheDay().ToString("s") + "Z");

            var url = $"{_environment.PixURL}/{_domain}?{queryString}";

            return await GetAsync<PixGetAllRequestModel, PixListResponseModel>(url, clientId, model, "Consultar PIX recebidos");
        }

        public async Task<ApiResponse> GetDevolucao(string clientId, string endToEndId, string txId)
        {
            var url = $"{_environment.PixURL}/{_domain}/{endToEndId}/devolucao/{txId}";
            var model = new { endToEndId, txId };

            return await GetAsync<dynamic, PixSolicitacaoDevolucaoResponseModel>(url, clientId, model, "Consultar devolução PIX");
        }

        public async Task<ApiResponse> SolicitarDevolucao(string clientId, string endToEndId, string txId, PixSolicitarDevolucaoRequestModel model)
        {
            var url = $"{_environment.PixURL}/{_domain}/{endToEndId}/devolucao/{txId}";

            return await PutAsync<PixSolicitarDevolucaoRequestModel, PixSolicitacaoDevolucaoResponseModel>(url, clientId, model, "Solicitação de devolução PIX");
        }
    }
}