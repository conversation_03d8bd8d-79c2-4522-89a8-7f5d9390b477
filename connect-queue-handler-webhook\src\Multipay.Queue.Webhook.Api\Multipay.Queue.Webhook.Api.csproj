﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>e207cb48-4cd9-4f39-bd00-7d0f8ffe33fa</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.MongoDb" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.Rabbitmq" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="8.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Multipay.Infrastructure.Plugin.AnspacePay\Multipay.Infrastructure.Plugin.AnspacePay.csproj" />
    <ProjectReference Include="..\Multipay.Infrastructure.Plugin.Aarin\Multipay.Infrastructure.Plugin.Aarin.csproj" />
    <ProjectReference Include="..\Multipay.Infrastructure.Plugin.BS2\Multipay.Infrastructure.Plugin.BS2.csproj" />
    <ProjectReference Include="..\Multipay.Infrastructure.Plugin.Celcoin\Multipay.Infrastructure.Plugin.Celcoin.csproj" />
    <ProjectReference Include="..\Multipay.Infrastructure.Plugin.DelBank\Multipay.Infrastructure.Plugin.DelBank.csproj" />
    <ProjectReference Include="..\Multipay.Infrastructure.Plugin.Genial\Multipay.Infrastructure.Plugin.Genial.csproj" />
    <ProjectReference Include="..\Multipay.Queue.Webhook.Application\Multipay.Queue.Webhook.Application.csproj" />
    <ProjectReference Include="..\Multipay.Queue.Webhook.Infrastructure.DAPetapoco\Multipay.Queue.Webhook.Infrastructure.DAPetapoco.csproj" />
    <ProjectReference Include="..\Multipay.Queue.Webhook.Infrastructure.RabbitMq\Multipay.Queue.Webhook.Infrastructure.RabbitMq.csproj" />
    <ProjectReference Include="..\Multipay.Queue.NotificarCliente.Application\Multipay.Queue.NotificarCliente.Application.csproj" />
    <ProjectReference Include="..\Multipay.Service.Criptography\Multipay.Service.Criptography.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.connectpsp.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
