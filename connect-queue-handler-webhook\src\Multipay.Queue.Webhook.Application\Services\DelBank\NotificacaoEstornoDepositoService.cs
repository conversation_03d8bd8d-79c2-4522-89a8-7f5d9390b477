﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.DelBank;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Serilog.Context;

namespace Multipay.Queue.Webhook.Application.Services.DelBank;

public interface INotificacaoEstornoDepositoService
{
    Task<bool> ProcessarEstornoDeposito(EstornoRecebidoModel? model);
}

internal class NotificacaoEstornoDepositoService(IUnitOfWork unitOfWork, ILogger<NotificacaoEstornoDepositoService> logger) : INotificacaoEstornoDepositoService
{
    public async Task<bool> ProcessarEstornoDeposito(EstornoRecebidoModel? model)
    {
        if (model == null)
            return true;

        var idDeposito = await unitOfWork.CobrancaRepository.RegistrarEndToEndDevolucaoCobranca(model.EndToEndId, model.OriginalEndToEndId);

        if (idDeposito == null)
            return true;

        var deposito = await unitOfWork.CobrancaRepository.ObterPorId(idDeposito.Value);

        if (deposito == null)
            return true;

        var transacaoPIX = await unitOfWork.QrCodePixRepository.ObterPorIdCobranca(deposito.Id);

        if (transacaoPIX != null)
            deposito.SetarTransacao(transacaoPIX);

        using (LogContext.PushProperty("Payload", model, true))
        {
            logger.LogInformation("Evento de estorno de depósito. E2E Transação: {E2ETransacao} / E2E Devolução: {E2EDevolucao}", model.OriginalEndToEndId, model.EndToEndId);

            await unitOfWork.CobrancaRepository.CancelarCobranca(idDeposito.Value);
            
            await unitOfWork.CobrancaRepository.InserirSaidaExtratoContabilCliente(deposito.IdCliente, $"ESTORNO DEPÓSITO - {deposito.Codigo}", deposito.ValorBruto, deposito.TransacaoPix!.TXId, deposito.TransacaoPix.IdContaBancariaEmpresa!.Value);
        }

        return true;
    }
}