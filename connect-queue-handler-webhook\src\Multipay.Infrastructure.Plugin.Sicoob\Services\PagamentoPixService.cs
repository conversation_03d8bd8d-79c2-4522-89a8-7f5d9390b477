﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Sicoob.Models;
using System.Net.Http.Json;

namespace Multipay.Infrastructure.Plugin.Sicoob.Services;

public interface IPagamentoPixService
{
    Task<PagamentoPixResponse> ConsultarPagamentoPix(string endtoEndId, CancellationToken cancellationToken);
}

internal class PagamentoPixService : IPagamentoPixService
{
    private readonly ILogger<PagamentoPixService> logger;
    private readonly HttpClient httpClient;

    public PagamentoPixService(
        ILogger<PagamentoPixService> logger,
        HttpClient httpClient)
    {
        this.logger = logger;
        this.httpClient = httpClient;
    }

    public async Task<PagamentoPixResponse> ConsultarPagamentoPix(string endtoEndId, CancellationToken cancellationToken)
    {
        try
        {
            var httpResponse = await httpClient.GetAsync($"pagamentos/{endtoEndId}", cancellationToken);

            if (httpResponse.IsSuccessStatusCode)
                return (await httpResponse.Content.ReadFromJsonAsync<PagamentoPixResponse>(cancellationToken: cancellationToken))!;

            return new PagamentoPixResponse
            {
                Erro = true,
                MensagemErro = $"Erro integração banco - {await httpResponse.Content.ReadAsStringAsync(cancellationToken)}"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Falha ao consultar pagamento: {endtoEndId}");

            return new PagamentoPixResponse
            {
                Erro = true,
                MensagemErro = $"Não foi possivel consultar pagamento: {endtoEndId}. "
            };
        }
    }
}