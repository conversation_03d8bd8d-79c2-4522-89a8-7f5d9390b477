﻿using Multipay.Queue.Webhook.Core.Models;

namespace Multipay.Queue.Webhook.Core.Interfaces
{
    public interface ICobrancaService
    {
        Task AdicionarPagadorCobranca(int idCobranca, string cpfPagadorPix, string nomePagadorPix, string endToEndId, string? ispbPagador, string? nomeBancoPagador);

        Task AtualizarEndToEndPorTxId(string txId, string endToEndId);

        Task ConfirmarPagamentoBRL(int idCobranca, int idCliente, byte pIX, decimal valorPago1, int numeroParcelas, string descricao, decimal valorPago2, decimal porcentagemTaxa, decimal valorTarifa, int horasResgate, string txIdPIX);

        ValueTask<CobrancaModel?> ObterCobranca(string txId);

        ValueTask<CobrancaModel?> ObterCobrancaPorCodigo(string customId);
    }
}