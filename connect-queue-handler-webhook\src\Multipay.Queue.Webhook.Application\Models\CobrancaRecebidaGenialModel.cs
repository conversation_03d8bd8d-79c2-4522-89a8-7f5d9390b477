﻿using Multipay.Queue.Webhook.Core.Interfaces;

namespace Multipay.Queue.Webhook.Application.Models
{
    public class CobrancaRecebidaGenialModel : IPIXData
    {
        public string InstantPaymentId { get; set; } = string.Empty;
        public string EndToEndId { get; set; } = string.Empty;
        public DateTime TimestampUTC { get; set; }
        public string SettlementId { get; set; } = string.Empty;
        public string ReceiverReconciliationIdentifier { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public ReceiverData Receiver { get; set; } = new();
        public PayerData Payer { get; set; } = new();
        public DateTime SpiTimestampUTC { get; set; }
        public DateTime CustomerInitiationTimestampUTC { get; set; }
        public string RemittanceInformation { get; set; } = string.Empty;
        public string MipTransactionType { get; set; } = string.Empty;
        public EventData EventType { get; set; } = new();
        public EventData EventStatus { get; set; } = new();
        public string InstructionType { get; set; } = string.Empty;
        public string InitiationForm { get; set; } = string.Empty;
        public string TransactionPurpose { get; set; } = string.Empty;
        public string InstructionPriority { get; set; } = string.Empty;
        public bool IsRigthsAndObligationSettlement { get; set; }
        public ReturnData? Return { get; set; } = null;

        public string Txid => ReceiverReconciliationIdentifier;

        public string Valor => Value;

        public string CpfPagador => Payer?.TaxId ?? string.Empty;
        public string NomePagador => Payer?.Name ?? string.Empty;
        public string? IspbPagador => Payer?.Psp?.Ispb;
        public string? NomeBancoPagador => Payer?.Psp?.Name;
        public string? ContaBancoPagador => Payer?.Account?.AccountNumber;
        public string? AgenciaBancoPagador => Payer?.Account?.Branch;
        public class PspData
        {
            public string Ispb { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
        }

        public class ReceiverData
        {
            public PspData Psp { get; set; } = new();
            public string Taxid { get; set; } = string.Empty;
            public string? AddressKey { get; set; }
            public AccountData Account { get; set; } = new();
        }

        public class PayerData
        {
            public PspData Psp { get; set; } = new();
            public string TaxId { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public AccountData Account { get; set; } = new();
        }

        public class EventData
        {
            public int Code { get; set; }
            public string Description { get; set; } = string.Empty;
        }

        public class AccountData
        {
            public string AccountNumber { get; set; } = string.Empty;
            public string AccountType { get; set; } = string.Empty;
            public string Branch { get; set; } = string.Empty;
        }

        public class ReturnData
        {
            public string OriginalEndToEndId { get; set; } = string.Empty;
            public string OriginalInstantPaymentId { get; set; } = string.Empty;
            public ReturnReasonInformationData ReturnReasonInformation { get; set; } = new();
        }

        public class ReturnReasonInformationData
        {
            public string ReasonCode { get; set; } = string.Empty;
            public string ReasonDescription { get; set; } = string.Empty;
            public string AdditionalInformation { get; set; } = string.Empty;
        }
    }
}