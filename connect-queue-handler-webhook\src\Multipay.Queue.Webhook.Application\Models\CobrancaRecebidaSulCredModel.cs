﻿using Multipay.Queue.Webhook.Application.Models.Saques;
using Multipay.Queue.Webhook.Core.Interfaces;

namespace Multipay.Queue.Webhook.Application.Models;

public class CobrancaRecebidaSulCredModel : IPIXData
{
    public string Txid => Data.TxId;
    public string Valor => Data.Payment.Amount;
    public string CpfPagador => Data.DebtorAccount.Document;
    public string NomePagador => Data.DebtorAccount.Name;
    public string EndToEndId => Data.EndToEndId;

    public string? IspbPagador => null;
    public string? NomeBancoPagador => null;
    public string? AgenciaBancoPagador => null;
    public string? ContaBancoPagador => null;
    public DataObj Data { get; set; } = new DataObj();
    public string Type { get; set; } = string.Empty;

    public class DataObj
    {
        public int Id { get; set; }
        public string TxId { get; set; } = string.Empty;
        public string? PixKey { get; set; }
        public StatusEnum Status { get; set; }
        public PaymentData Payment { get; set; } = null!;
        public object[]? Refunds { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? ErrorCode { get; set; }
        public string EndToEndId { get; set; } = string.Empty;
        public TicketdataObj? TicketData { get; set; }
        public string WebhookType { get; set; } = string.Empty;
        public AccountData DebtorAccount { get; set; } = null!;
        public string IdempotencyKey { get; set; } = string.Empty;
        public CreditDebitTypeEnum CreditDebitType { get; set; }
        public AccountData CreditorAccount { get; set; } = null!;
        public string LocalInstrument { get; set; } = string.Empty;
        public string TransactionType { get; set; } = string.Empty;
        public object? RemittanceInformation { get; set; }

        public class PaymentData
        {
            public string Amount { get; set; } = string.Empty;
            public string Currency { get; set; } = string.Empty;
        }

        public class TicketdataObj
        {
        }

        public class AccountData
        {
            public string Ispb { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public string Issuer { get; set; } = string.Empty;
            public string Number { get; set; } = string.Empty;
            public string Document { get; set; } = string.Empty;
            public AccountTypeEnum AccountType { get; set; }
        }
    }
}