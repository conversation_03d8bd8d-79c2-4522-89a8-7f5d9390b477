﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Settings;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Bus
{
    internal class BusConsumerSolicitacaoSaqueEstornado : EventBusConsumerRabbitMQ<SolicitacaoSaquesEstornadoEvent>, IBusConsumerSolicitacaoSaqueEstornado
    {
        public BusConsumerSolicitacaoSaqueEstornado(
            IRabbitMQPersistentConnection persistentConnection,
            ILogger<BusConsumerSolicitacaoSaqueEstornado> logger,
            IOptions<RabbitMqSettings> options) : base(persistentConnection, logger, options.Value.SolicitacaoSaquesEstornado)
        {
        }
    }
}