﻿namespace Multipay.Queue.Webhook.Core.Entities;
public class BillingEntity
{
    public int Id { get; set; }
    public int? ApplicationId { get; set; }
    public int CustomerId { get; set; }
    public int? PersonBillingId { get; set; }
    public byte? PaymentMethodId { get; set; }
    public int? LinkCustomerPaymentId { get; set; }
    public Guid Guid { get; set; }
    public string? Code { get; set; }
    public string? InvoiceNumber { get; set; }
    public decimal GrossValue { get; set; }
    public decimal DiscountValue { get; set; }
    public decimal RateValue { get; set; }
    public decimal ShippingValue { get; set; }
    public decimal NetValue { get; set; }
    public decimal? AmountPaid { get; set; }
    public decimal LateFineValue { get; set; }
    public decimal LateInterestPercentage { get; set; }
    public byte MaximumInstallments { get; set; }
    public byte ChosenInstallmentQuantity { get; set; }
    public string? ConfirmationUrl { get; set; }
    public string? ErrorUrl { get; set; }
    public string? RefuseUrl { get; set; }
    public string? UpdateUrl { get; set; }
    public string? ReturnSiteUrl { get; set; }
    public string? Observation { get; set; }
    public string? BillingCurrency { get; set; }
    public string? BillingPayment { get; set; }
    public bool IsCustomerConfirmed { get; set; }
    public bool IsPassCustomerFee { get; set; }
    public bool IsPassCustomerTariff { get; set; }
    public bool IsPassCustomerInterest { get; set; }
    public byte MinimumInstallmentInterestCustomer { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime MaximumPaymentDate { get; set; }
    public DateTime RegisterDate { get; set; }
    public DateTime? PaymentDate { get; set; }
    public DateTime? CancellationDate { get; set; }
    public bool IsAccountRecharge { get; set; }
    public DateTime? DiscountDate { get; set; }
    public int? MonthlyInvoiceId { get; set; }
    public string? RequestWebhookId { get; set; }
}
