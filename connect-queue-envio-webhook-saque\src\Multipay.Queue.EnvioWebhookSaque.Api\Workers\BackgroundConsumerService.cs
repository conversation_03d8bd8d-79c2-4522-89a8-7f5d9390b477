﻿using Multipay.RabbitMQExtension.Bus.Interfaces;
using Multipay.RabbitMQExtension.Events;
using Serilog.Context;
using System.Diagnostics;

namespace Multipay.Queue.EnvioWebhookSaque.Api.Workers
{
    public abstract class BackgroundConsumerService<T> : BackgroundService
        where T : IntegrationEvent
    {
        protected readonly ILogger logger;
        protected readonly IBusConsumer<T> busConsumer;

        public BackgroundConsumerService(ILogger logger, IBusConsumer<T> busConsumer)
        {
            this.logger = logger;
            this.busConsumer = busConsumer;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            busConsumer.ProcessEventHandler = async (mensagem, @event, retry) =>
            {
                return await Task.Run(async () =>
                {
                    var correlationId = Guid.NewGuid().ToString("N");

                    LogContext.PushProperty("CorrelationID", correlationId);
                    LogContext.PushProperty("Transacao", "SAQUE");

                    logger.LogInformation("Processando mensagem: {mensagem}", mensagem);

                    if (@event == null)
                    {
                        logger.LogWarning("Event is null, {mensagem}", mensagem);
                        return true;
                    }

                    bool result = false;

                    try
                    {
                        return result = await ProcessEventHandler(mensagem, @event, retry);
                    }
                    catch
                    {
                        return false;
                    }
                });
            };

            var isConnected = busConsumer.Subscribe();

            while (isConnected && !stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="mensagem"></param>
        /// <param name="event"></param>
        /// <param name="retry">true caso a mensagem seja um reprocessamento</param>
        /// <returns></returns>
        protected abstract Task<bool> ProcessEventHandler(string mensagem, T @event, bool retry);
    }
}