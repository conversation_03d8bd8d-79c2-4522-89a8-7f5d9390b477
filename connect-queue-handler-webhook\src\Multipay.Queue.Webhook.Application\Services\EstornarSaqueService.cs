﻿using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;

namespace Multipay.Queue.Webhook.Application.Services
{
    public interface IEstornarSaqueService
    {
        Task EstornarSaque(string endToEndId, string mensagemJustificativa);
    }

    internal class EstornarSaqueService : IEstornarSaqueService
    {
        private readonly ICentralizadorLogsService centralizadorLogsService;
        private readonly IBusNotificarSaqueEstornadoCliente busNotificarSaqueEstornadoCliente;
        private readonly IUnitOfWork unitOfWork;

        public EstornarSaqueService(
            ICentralizadorLogsService centralizadorLogsService,
            IBusNotificarSaqueEstornadoCliente busNotificarSaqueEstornadoCliente,

            IUnitOfWork unitOfWork)
        {
            this.centralizadorLogsService = centralizadorLogsService;
            this.busNotificarSaqueEstornadoCliente = busNotificarSaqueEstornadoCliente;
            this.unitOfWork = unitOfWork;
        }

        public async Task EstornarSaque(string endToEndId, string mensagemJustificativa)
        {
            var solicitacaoSaque = await unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorCodigoTransacao(endToEndId);

            if (solicitacaoSaque == null || solicitacaoSaque.DataEstorno.HasValue)
                return;
            try
            {
                await unitOfWork.SolicitacaoSaqueRepository.EstornarSaque(solicitacaoSaque.Id, mensagemJustificativa);

                AdicionarLog(solicitacaoSaque.Id, $"Transação estornada, motivo: {mensagemJustificativa}");

                if (!string.IsNullOrWhiteSpace(solicitacaoSaque.UrlAtualizacao))
                    busNotificarSaqueEstornadoCliente.PublicarNotificacaoCliente(solicitacaoSaque.UrlAtualizacao, solicitacaoSaque.Codigo, solicitacaoSaque.CustomId, solicitacaoSaque.CustomUserId, solicitacaoSaque.Guid, solicitacaoSaque.CodigoTransacao!);
            }
            catch (Exception ex)
            {
                AdicionarLog(solicitacaoSaque.Id, "Erro ao estornar saque");
                AdicionarLog(solicitacaoSaque.Id, ex.Message);
            }
        }

        protected void AdicionarLog(int idSolicitacaoSaque, string texto)
        {
            centralizadorLogsService.AdicionarLogSolicitacaoSaque(idSolicitacaoSaque, texto);
        }
    }
}