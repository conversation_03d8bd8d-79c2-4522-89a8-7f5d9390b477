﻿using Multipay.RabbitMQExtension.DTOs;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Settings
{
    public class RabbitMqSettings : RabbitMqSettingsBase
    {
        public Config NotificarCobrancaCliente { get; set; } = null!;
        public Config NotificarCobrancaClientePublishNotification { get; set; } = null!;
        public Config Webhooks { get; set; } = null!;
        public Config NotificarSaqueCliente { get; set; } = null!;
        public Config NotificarSaqueClientePublishNotification { get; set; } = null!;
        public Config SolicitacaoSaques { get; set; } = null!;
        public Config NotificarClienteRetry { get; set; } = null!;
        public Config RefundPixNotification { get; set; } = null!;

    }
}