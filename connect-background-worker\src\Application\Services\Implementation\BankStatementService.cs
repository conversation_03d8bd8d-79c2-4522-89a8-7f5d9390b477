using Amazon.Auth.AccessControlPolicy;
using Application.Services.Interfaces;
using Application.Services.Interfaces.Api;
using Application.Services.Models.Requests;
using Application.Services.Models.Responses;
using Domain.Extensions;
using Domain.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ResultT;
using static ResultT.Results;

namespace Application.Services.Implementation;

public class BankStatementService(ILogger<BankStatementService> logger, IConnectApi connectApi) : IBankStatementService
{
    public async Task<List<BankStatement>> ExecuteBankStatementAsync(CustomerCredential credential,int idCliente, int idTipoContaBancaria, DateTime dataDe,DateTime dataAte, CancellationToken cancellationToken)
    {
        var authorization = credential.GetAuthorization();
        var request = new BankStatementRequest(idCliente, idTipoContaBancaria, dataDe, dataAte);

        var response = await connectApi.GetBankStatementAsync(authorization, request, request, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            logger.Error("Erro ao executar bank statement service: ({Status}): {Response}", response.StatusCode, $"{response.Error?.Content}");
            return [];
        }

        return response!.Content!;
    }
}
