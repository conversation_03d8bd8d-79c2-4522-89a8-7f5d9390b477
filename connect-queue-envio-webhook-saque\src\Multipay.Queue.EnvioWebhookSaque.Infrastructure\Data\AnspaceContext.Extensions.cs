﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Data
{
    public static class AnspaceContextExtensions
    {
        public static async Task CallStoredProcedureAsync(this DbContext context, string storedProcedureName, params SqlParameter[] parameters)
        {
            var commandText = $"EXEC {storedProcedureName} ";
            if (parameters.Any())
            {
                commandText += string.Join(",", parameters.Select(p => $"{p.ParameterName}={p.ParameterName}"));
            }
            await context.Database.ExecuteSqlRawAsync(commandText, parameters);
        }

        public static async Task<List<TEntity>> CallStoredProcedureAsync<TEntity>(this DbContext context, string storedProcedureName, params SqlParameter[] parameters)
            where TEntity : class
        {
            var commandText = $"EXEC {storedProcedureName} ";
            if (parameters.Any())
            {
                commandText += string.Join(",", parameters.Select(p => $"{p.ParameterName}={p.ParameterName}"));
            }

            var result = await context.Set<TEntity>().FromSqlRaw(commandText, parameters).ToListAsync();
            return result;
        }

        public static SqlParameter ToSqlParameter(this object? value, [CallerArgumentExpression("value")] string? argumentName = null)
        {
            return new SqlParameter()
            {
                ParameterName = $"@{argumentName}",
                Value = value
            };
        }
    }
}