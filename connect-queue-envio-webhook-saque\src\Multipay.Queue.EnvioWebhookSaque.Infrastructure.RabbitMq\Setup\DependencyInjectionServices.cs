﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookSaque.Core.Utils;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Bus;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Settings;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Bus;
using Multipay.RabbitMQExtension.DTOs;
using Multipay.RabbitMQExtension.Setup;
using Multipay.Service.Criptography;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Setup
{
    public static class DependencyInjectionServices
    {
        public static RabbitMqSettings AddDependencyInjectionRabbitMqSaquesServices(this IServiceCollection services, ConfigurationManager configuration, CriptoService criptoService)
        {
            var section = configuration.GetSection(key: nameof(RabbitMqSettings));

            services.AddDependencyInjectionRabbitMQServices(section);

            RegisterRabbitMqSaquesEventBus(services, configuration);

            services.Configure<RabbitMqSettings>(section);

            var settings = section.Get<RabbitMqSettings>()!;
            var sp = services.BuildServiceProvider();
            using var scope = sp.CreateScope();
            var ambienteHelp = scope.ServiceProvider.GetRequiredService<AmbienteHelp>();

            string eventBusRetryCount = ambienteHelp.RabbitMQ_RetryCount ?? $"{settings.Setup.EventBusRetryCount:0}" ?? "3";
            string port = ambienteHelp.RabbitMQ_Port ?? $"{settings.Setup.EventBusPort:0}" ?? throw new ArgumentException($"EventBusPort not found");

            settings.Setup.EnableSsl = ambienteHelp.EnableSsl;

            //settings.Setup = new RabbitMqSettingsBase.SetupConfig
            //{
            //    EventBusVirtualHost = ambienteHelp.RabbitMQ_VirtualHost ?? settings.Setup.EventBusVirtualHost ?? throw new ArgumentException($"VirtualHost not found"),
            //    EventBusConnection = ambienteHelp.RabbitMQ_Connection ?? settings.Setup.EventBusConnection ?? throw new ArgumentException($"HostName not found"),
            //    EventBusPassword = ambienteHelp.RabbitMQ_Password ?? settings.Setup.EventBusPassword ?? throw new ArgumentException($"Password not found"),
            //    EventBusUserName = ambienteHelp.RabbitMQ_Username ?? settings.Setup.EventBusUserName ?? throw new ArgumentException($"UserName not found"),
            //    EventBusPort = int.Parse(port),
            //    EnableSsl = settings.Setup.EnableSsl,
            //    EventBusRetryCount = int.Parse(eventBusRetryCount),
            //    IgnoreCertificateValidationErrors = true
            //};

            settings.Setup = new RabbitMqSettingsBase.SetupConfig
            {
                EventBusVirtualHost = settings.Setup.EventBusVirtualHost ?? configuration["RabbitMQWebhook:VirtualHost"] ?? throw new ArgumentException($"VirtualHost not found"),
                EventBusConnection = settings.Setup.EventBusConnection ?? configuration["RabbitMQWebhook:HostName"] ?? throw new ArgumentException($"HostName not found"),
                EventBusPassword = settings.Setup.EventBusPassword ?? configuration["RabbitMQWebhook:Password"] ?? throw new ArgumentException($"Password not found"),
                EventBusUserName = settings.Setup.EventBusUserName ?? configuration["RabbitMQWebhook:UserName"] ?? throw new ArgumentException($"UserName not found"),
                EventBusPort = int.Parse(port),
                EventBusRetryCount = int.Parse(eventBusRetryCount),
                EnableSsl = settings.Setup.EnableSsl,
                ConsumerDispatchConcurrency = 5,
                IgnoreCertificateValidationErrors = true
            };

            settings.Setup.EventBusPassword = criptoService.Decrypt(settings.Setup.EventBusPassword).Result;

            services.PostConfigure<RabbitMqSettings>(opt =>
            {
                opt.Setup = settings.Setup;
            });

            services.AddSingleton<IOptions<RabbitMqSettingsBase>>(service =>
            {
                using var scopeservice = service.CreateScope();
                var op = scopeservice.ServiceProvider.GetRequiredService<IOptions<RabbitMqSettings>>();

                return op;
            });

            return settings;
        }

        private static void RegisterRabbitMqSaquesEventBus(IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IBusConsumerSolicitacaoSaqueConfirmadoNotificarCliente, BusConsumerSolicitacaoSaqueConfirmadoNotificarCliente>();
            services.AddSingleton<IBusConsumerSolicitacaoSaqueConfirmadoNotificarClientePublishNotification, BusConsumerSolicitacaoSaqueConfirmadoNotificarClientePublishNotification>();
            services.AddSingleton<IBusConsumerSolicitacaoSaqueEstornado, BusConsumerSolicitacaoSaqueEstornado>();
            services.AddSingleton<IPublishSolicitacaoSaqueEstornadoRetry, BusSolicitacaoSaqueEstornadaNotificarClienteRetry>();
            services.AddSingleton<IPublishNotificarClienteRetry, PublishNotificarClienteRetry>();
        }
    }
}