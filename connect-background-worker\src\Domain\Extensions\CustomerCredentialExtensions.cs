using System.Net;
using System.Net.Http.Headers;
using Domain.Models;

namespace Domain.Extensions;

public static class CustomerCredentialExtensions
{
    public static string GetAuthorization(this CustomerCredential credential) =>
        $"{credential.UserName}:{credential.Password}:{credential.ApplicationToken}:{credential.Token}";

    public static CustomerCredential GetCredential(this string authentication)
    {
        var credential = authentication.Split(":");

        return new CustomerCredential
        (
            userName: credential[0],
            password: credential[1],
            applicationToken: credential[2],
            token: credential[3])
        ;
    }

    public static CustomerCredential? GetAuthorizationCredential(this HttpRequestHeaders headers) =>    
        headers.GetValues(nameof(Authorization)).Any() ? 
            headers.GetValues(nameof(Authorization))
                .FirstOrDefault()?.ToString()
                .GetCredential() : null;
}
