{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Api\\Multipay.Queue.EnvioWebhookSaque.Api.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Api\\Multipay.Queue.EnvioWebhookSaque.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Api\\Multipay.Queue.EnvioWebhookSaque.Api.csproj", "projectName": "Multipay.Queue.EnvioWebhookSaque.Api", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Api\\Multipay.Queue.EnvioWebhookSaque.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Application\\Multipay.Queue.EnvioWebhookSaque.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Application\\Multipay.Queue.EnvioWebhookSaque.Application.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AspNetCore.HealthChecks.MongoDb": {"target": "Package", "version": "[8.0.1, )"}, "AspNetCore.HealthChecks.Rabbitmq": {"target": "Package", "version": "[8.0.1, )"}, "AspNetCore.HealthChecks.SqlServer": {"target": "Package", "version": "[8.0.2, )"}, "AspNetCore.HealthChecks.UI.Client": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.20.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Application\\Multipay.Queue.EnvioWebhookSaque.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Application\\Multipay.Queue.EnvioWebhookSaque.Application.csproj", "projectName": "Multipay.Queue.EnvioWebhookSaque.Application", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Application\\Multipay.Queue.EnvioWebhookSaque.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Caching.SqlServer": {"target": "Package", "version": "[8.0.6, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj", "projectName": "Multipay.Queue.EnvioWebhookSaque.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Datadog.Trace": {"target": "Package", "version": "[2.51.0, )"}, "Datadog.Trace.Bundle": {"target": "Package", "version": "[2.51.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Exceptions": {"target": "Package", "version": "[8.4.0, )"}, "Serilog.Exceptions.SqlServer": {"target": "Package", "version": "[8.4.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.Datadog.Logs": {"target": "Package", "version": "[0.5.2, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.Map": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj", "projectName": "Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.6, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "PetaPoco.Compiled": {"target": "Package", "version": "[6.0.677, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj", "projectName": "Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Multipay.RabbitMQExtension": {"target": "Package", "version": "[1.0.18, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.csproj", "projectName": "Multipay.Queue.EnvioWebhookSaque.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure\\Multipay.Queue.EnvioWebhookSaque.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Queue.EnvioWebhookSaque.Core\\Multipay.Queue.EnvioWebhookSaque.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.6, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "MongoDB.Bson": {"target": "Package", "version": "[2.26.0, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.26.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj", "projectName": "Multipay.Service.Criptography", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\Multipay.Service.Criptography.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-queue-envio-webhook-saque\\src\\Multipay.Service.Criptography\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"AWSSDK.SecretsManager": {"target": "Package", "version": "[3.7.304.4, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}