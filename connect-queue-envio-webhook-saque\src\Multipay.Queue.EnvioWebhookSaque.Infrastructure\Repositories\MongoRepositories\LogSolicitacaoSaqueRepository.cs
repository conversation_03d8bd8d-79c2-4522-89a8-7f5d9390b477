﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Data;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Repositories.MongoRepositories
{
    public interface ILogSolicitacaoSaqueRepository : IBaseRepository<LogSolicitacaoSaqueModel>
    { }

    internal class LogSolicitacaoSaqueRepository : BaseRepository<LogSolicitacaoSaqueModel>, ILogSolicitacaoSaqueRepository
    {
        public LogSolicitacaoSaqueRepository(
            ILogger<LogSolicitacaoSaqueRepository> logger,
            AnspaceMongoContext context) : base(logger, context.LogSolicitacaoSaque)
        {
        }
    }
}