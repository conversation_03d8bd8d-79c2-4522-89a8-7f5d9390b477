# Connect.Integration.Bank

Adicione as seguintes injeções no projeto
AddBankConfiguration(configuration)
AddBankIntegration(configuration)
AddBankIntegrationRedis(configuration)
AddAarinAuth()

Necessita do configurar appsettigs.json


## Arquitetura das Integrações


### Diagrama de Sequência

```mermaid
%%{init: { 'sequence': {'messageAlign': 'left'} }}%%
sequenceDiagram
    Aplicação Cliente->>Serviço Comum : Request Comum
    Serviço Comum->>Classe de Extensão : Request Comum
    Classe de Extensão->>Classe de Extensão : Converte Request
    Classe de Extensão->>Serviço Comum : Request do Banco
    Serviço Comum->>Serviço do Banco : Request do Banco
    Serviço do Banco->>Api do Banco : Request do Banco
    Api do Banco->>Serviço do Banco: Response do Banco
    Serviço do Banco->>Serviço Comum : Response do Banco
    Serviço Comum->>Classe de Extensão : Response do Banco
    Classe de Extensão->>Classe de Extensão : Converte Response
    Classe de Extensão->>Serviço Comum : Response Comum
    Serviço Comum->>Aplicação Cliente : Request Comum

````