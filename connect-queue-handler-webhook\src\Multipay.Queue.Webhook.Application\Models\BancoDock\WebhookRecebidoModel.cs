﻿namespace Multipay.Queue.Webhook.Application.Models.BancoDock
{
    internal partial class WebhookRecebidoModel
    {
        public enum OriginEnum
        {
            PIX_TRANSFERS_DEBIT, PIX_TRANSFERS_CREDIT, PIX_REVERSAL_DEBIT, PIX_REVERSAL_CREDIT
        }

        public OriginEnum Origin { get; set; }
        public PayloadData Payload { get; set; } = null!;

        public partial class PayloadData
        {
            public int IdAccount { get; set; }
            public string EndToEndId { get; set; } = null!;
            public string IdempotencyKey { get; set; } = null!;
            public string? IdTx { get; set; }
            public string TransactionCode { get; set; } = null!;
            public string TransactionStatus { get; set; } = null!;
            public string TransactionType { get; set; } = null!;
            public int TransferType { get; set; }
            public int? ErrorType { get; set; }
            public DateTime TransactionDate { get; set; }
            public PartyData? CreditParty { get; set; }
            public PartyData? DebitParty { get; set; }
            public decimal FinalAmount { get; set; }
            public string? PayerAnswer { get; set; }

            public class PartyData
            {
                public string Ispb { get; set; } = null!;
                public string BankName { get; set; } = null!;
                public string Name { get; set; } = null!;
                public string NationalRegistration { get; set; } = null!;
                public string BankAccountType { get; set; } = null!;
                public string BankAccountNumber { get; set; } = null!;
                public string BankBranchNumber { get; set; } = null!;
            }
        }
    }
}