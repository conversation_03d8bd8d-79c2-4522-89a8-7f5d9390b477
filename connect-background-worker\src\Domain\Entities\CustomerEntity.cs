namespace Domain.Entities;

public class CustomerEntity
{    
    public int Id { get; }
    public Guid Guid { get; }
    public DateTime? LockDate { get; }
    public DateTime? DeletionDate { get; }

    public CustomerEntity(int id, Guid guid, DateTime? lockDate, DateTime? deletionDate)
    {
        Id = id;
        Guid = guid;
        LockDate = lockDate;
        DeletionDate = deletionDate;
    }
}
