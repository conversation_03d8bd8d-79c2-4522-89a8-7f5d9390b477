using Application.Services.Interfaces;
using Application.Services.Interfaces.Api;
using Application.Services.Models.Requests;
using Application.Services.Models.Responses;
using Domain.Extensions;
using Domain.Models;
using Microsoft.Extensions.Logging;
using ResultT;
using static ResultT.Results;

namespace Application.Services.Implementation;

public class RebalanceService(ILogger<RebalanceService> logger, IConnectApi connectApi) : IRebalanceService
{
    public async Task<Result<RebalanceResponse>> ExecuteRebalanceAsync(CustomerCredential credential, long amountBalance, CancellationToken cancellationToken)
    {
        var authorization = credential.GetAuthorization();
        var request = new RebalanceRequest(amountBalance);

        var response = await connectApi.PostRebalanceAsync(authorization, request, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            logger.Error("Erro ao executar o rebalance ({Status}): {Response}", response.StatusCode, $"{response.Error?.Content}");
            return Error();
        }
        var rebalanceResponse = response.Content;

        return Ok(rebalanceResponse);
    }
}
