﻿using Microsoft.Extensions.Logging;
using Multipay.Infrastructure.Plugin.Genial.Services;
using Multipay.Queue.Webhook.Application.Models;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Core.Models;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaGenialService : CobrancaRecebidaService<CobrancaRecebidaGenialModel>
    {
        private readonly ILogger<CobrancaRecebidaGenialService> logger;
        private readonly IEstornarQrCodeService estornarService;

        public CobrancaRecebidaGenialService(
            ILogger<CobrancaRecebidaGenialService> logger,
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente,
            IEstornarQrCodeService estornarService) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.Genial)
        {
            this.logger = logger;
            this.estornarService = estornarService;
        }

        protected override IEnumerable<CobrancaRecebidaGenialModel> ListPix => new List<CobrancaRecebidaGenialModel>()
        {
            Parameters!
        };

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);

        protected override bool VerificarSeProcessaRecebimentoPix(CobrancaRecebidaGenialModel dadosPIX)
        {
            return dadosPIX?.EventType?.Description == "Receipt";
        }

        protected override async ValueTask<ResultEstornoBanco> ProcessarDevolucaoBancoAsync(CobrancaModel cobranca)
        {
            try
            {
                var valorPago = ObterValorPago(Parameters!);
                estornarService.SetIdContaBancariaEmpresa(cobranca.TransacaoPix!.IdContaBancariaEmpresa!.Value);
                var rs = await estornarService.SolicitarDevolucao(Parameters!.InstantPaymentId, valorPago);

                if (!rs.Item1)
                {
                    CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, "Falha na devolucao -> " + rs.Item2);
                    return ResultEstornoBanco.Falha;
                }
                var txId = cobranca.TransacaoPix!.TXId;

                await CobrancaService.AtualizarEndToEndPorTxId(txId, rs.Item2);//novo endtoend
                return ResultEstornoBanco.Sucesso;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Falha ao processar devolucao de cobranca: {id}", cobranca.Id);
                CentralizadorLogsService.AdicionarLogCobranca(cobranca.Id, ex.ToString());
                return ResultEstornoBanco.Falha;
            }
        }
    }
}