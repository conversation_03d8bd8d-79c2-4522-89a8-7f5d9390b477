﻿using Multipay.Queue.Webhook.Application.Models.Celcoin;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services.Celcoin;
public interface ICelcoinPixReversalService
{
    Task<bool> VerifyPixCorrelationAsync<T>(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, CancellationToken cancellationToken);
    Task<bool> ProcessReversalPixAsync<T>(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, CancellationToken cancellationToken);
}
