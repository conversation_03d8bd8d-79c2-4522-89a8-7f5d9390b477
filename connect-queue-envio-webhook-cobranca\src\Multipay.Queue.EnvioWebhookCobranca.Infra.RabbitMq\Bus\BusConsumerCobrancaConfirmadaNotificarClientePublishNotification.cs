﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Settings;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Bus.Interfaces;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.RabbitMq.Bus
{
    public interface IBusConsumerCobrancaConfirmadaNotificarClientePublishNotification : IBusConsumer<CobrancaConfirmadaNotificarClienteEvent>
    { }

    internal class BusConsumerCobrancaConfirmadaNotificarClientePublishNotification : EventBusConsumerRabbitMQ<CobrancaConfirmadaNotificarClienteEvent>, IBusConsumerCobrancaConfirmadaNotificarClientePublishNotification
    {
        public BusConsumerCobrancaConfirmadaNotificarClientePublishNotification(
            IRabbitMQPersistentConnection persistentConnection,
            IOptions<RabbitMqSettings> options,
            ILogger<BusConsumerCobrancaConfirmadaNotificarClientePublishNotification> logger)
            : base(persistentConnection, logger, options.Value.NotificarCobrancaClientePublishNotification)
        { }
    }
}