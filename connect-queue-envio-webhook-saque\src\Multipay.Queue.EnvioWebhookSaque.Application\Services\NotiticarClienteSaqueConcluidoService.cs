﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookSaque.Core.Extensions;
using Multipay.Queue.EnvioWebhookSaque.Core.Models;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.DAPetapoco;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events;
using Serilog.Context;
using System.Diagnostics;
using System.Text;

namespace Multipay.Queue.EnvioWebhookSaque.Application.Services
{
    public interface INotiticarClienteSaqueConcluidoService
    {
        Task<bool> EnviarNotificacao(SaqueConfirmadoNotificarClienteEvent @event);
    }

    public class NotiticarClienteSaqueConcluidoService : INotiticarClienteSaqueConcluidoService
    {
        private readonly ILogger<NotiticarClienteSaqueConcluidoService> logger;
        private readonly IHttpClientFactory httpClientFactory;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICentralizadorLogsService centralizadorLogsService;
        private readonly IPublishNotificarClienteRetry publishNotificarCliente;

        public NotiticarClienteSaqueConcluidoService(
            ILogger<NotiticarClienteSaqueConcluidoService> logger,
            IHttpClientFactory httpClientFactory,
            IUnitOfWork unitOfWork,
            ICentralizadorLogsService centralizadorLogsService,
            IPublishNotificarClienteRetry publishNotificarCliente)
        {
            this.logger = logger;
            this.httpClientFactory = httpClientFactory;
            this._unitOfWork = unitOfWork;
            this.centralizadorLogsService = centralizadorLogsService;
            this.publishNotificarCliente = publishNotificarCliente;
        }

        public async Task<bool> EnviarNotificacao(SaqueConfirmadoNotificarClienteEvent @event)
        {
            var solicitacaoSaque = await this._unitOfWork.SolicitacaoSaqueRepository.ObterSolicitacaoSaqueDadosBasicosPorId(@event.IdSolicitacaoSaque);

            if (solicitacaoSaque == null)
                return false;

            var nomeCliente = await _unitOfWork.SolicitacaoSaqueRepository.ObterNomeClientePorId(solicitacaoSaque.IdCliente);
            LogContext.PushProperty("Cliente", nomeCliente);

            LogContext.PushProperty("Saque", new
            {
                solicitacaoSaque.Id,
                solicitacaoSaque.Codigo,
                E2E = solicitacaoSaque.CodigoAutenticacao
            }, true);

            try
            {
                var urlWebhook = solicitacaoSaque.UrlConfirmacao!;

                if (solicitacaoSaque.DataEstorno.HasValue)
                {
                    if (!string.IsNullOrEmpty(solicitacaoSaque.UrlAtualizacao))
                        urlWebhook = solicitacaoSaque.UrlAtualizacao!;
                    else
                        centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, "Url de atualização não informada, enviando notificação de estorno para a url de confirmação");
                }

                //Disparando Webhook Original
                await DispararWebhook(@event, solicitacaoSaque, urlWebhook, false);

                if (solicitacaoSaque.IdCliente == 13)
                {
                    //Disparando Webhook Adicional CHUMBADO para cliente SorteNaBet
                    await DispararWebhook(@event, solicitacaoSaque, "https://n8n3.sortenabetferramentas.online/webhook/0c675dad-9536-4d0c-b50f-610fbbfd78cd", false);
                }

                if (solicitacaoSaque.IdCliente == 14)
                {
                    //Disparando Webhook Adicional CHUMBADO para cliente BetFusion
                    await DispararWebhook(@event, solicitacaoSaque, "https://n8n3.sortenabetferramentas.online/webhook/e664b247-9a1c-41cc-b107-c6093e3b404d", false);
                    await DispararWebhook(@event, solicitacaoSaque, "https://serverbetfusion.blacktrack.cloud/track/", false);
                }

                return true;
            }
            catch (Exception ex)
            {
                while (ex.InnerException != null)
                    ex = ex.InnerException;

                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Erro ao enviar webhook de confirmação de saque: {ex.Message}");

                return false;
            }
        }

        private async Task<bool> DispararWebhook(SaqueConfirmadoNotificarClienteEvent @event, ViewSolicitacaoSaqueDadosBasicos? solicitacaoSaque, string UrlWebhook, bool adicional = false)
        {
            if (string.IsNullOrEmpty(UrlWebhook))
            {
                logger.LogInformation("solicitacaoSaque sem UrlConfirmacao");
                return true;
            }

            centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Enviando webhook cliente para notificar a confirmação de saque para: {UrlWebhook}");

            if (solicitacaoSaque.DataPagamento is not null)
            {
                var span = (DateTime.Now - solicitacaoSaque.DataPagamento.Value);
                var message = $"Enviando webhook ao cliente após {span.Days} days, {span.Hours} hours, {span.Minutes} minutes, {span.Seconds} seconds";

                logger.LogInformation(message);

                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, message);
            }

            if (@event.Status == null)
            {
                @event.Sucesso = true;
                @event.StatusCode = "01";
                @event.Status = "Completed";

                if (solicitacaoSaque.DataEstorno is not null)
                {
                    @event.Sucesso = false;
                    @event.StatusCode = "02";
                    @event.Status = "RETURNED TRANSACTION";
                }
            }

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, UrlWebhook)
            {
                Content = ObterBodyContent(@event, solicitacaoSaque, out string bodyJson)
            };

            if (!string.IsNullOrWhiteSpace(solicitacaoSaque.RequestIdWebhook))
                httpRequestMessage.Headers.Add("X-Request-Id", solicitacaoSaque.RequestIdWebhook);

            var httpClient = httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(15);

            HttpResponseMessage httpResponseMessage;

            LogContext.PushProperty("URL", UrlWebhook);

            Stopwatch watch = Stopwatch.StartNew();

            try
            {
                httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);

                watch.Stop();

                if (httpResponseMessage.IsSuccessStatusCode)
                {
                    logger.LogInformation("[{Evento}] Envio do webhook efetuado com sucesso (HTTP {StatusCode}) após {Duracao}ms", EventosCriticos.EV4001.Description(), (int)httpResponseMessage.StatusCode, watch.ElapsedMilliseconds);
                }
                else
                {
                    logger.LogError("[{Evento}] Falha no envio do webhook (HTTP {StatusCode}) após {Duracao}ms", EventosCriticos.EV4001.Description(), (int)httpResponseMessage.StatusCode, watch.ElapsedMilliseconds);
                }
            }
            catch (TaskCanceledException) //timeout
            {
                watch.Stop();

                logger.LogError("[{Evento}] Tempo limite de {Duracao}ms atingido, enviando para a fila de retry", EventosCriticos.EV4001.Description(), httpClient.Timeout.Seconds * 1000);

                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, $"Cliente não respondeu em tempo hábil. Limite de espera: {httpClient.Timeout.Seconds}s");

                if (!adicional)
                    publishNotificarCliente.Publicar(@event);

                return true;
            }
            var isSuccessStatusCode = httpResponseMessage.IsSuccessStatusCode;

            if (isSuccessStatusCode)
            {
                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id, "Enviado webhook via mensageria");

                await AdicionarLogEnvioWebhookSolicitacaoSaque(solicitacaoSaque, bodyJson, httpResponseMessage, UrlWebhook);

                return true;
            }
            else
            {
                var response = await httpResponseMessage.Content.ReadAsStringAsync();

                logger.LogWarning("Response: {response}", response);

                centralizadorLogsService.AdicionarLogSolicitacaoSaque(solicitacaoSaque.Id,
                    (int)httpResponseMessage.StatusCode < 500
                        ? $"Não foi possível enviar webhook ao cliente. Webhook cliente retornou StatusCode: {httpResponseMessage.StatusCode}. {response}"
                        : $"Enviado webhook ao cliente com falha[Status: {httpResponseMessage.StatusCode}]. {UrlWebhook}");

                if (!string.IsNullOrWhiteSpace(@event.IdWebhook))
                    centralizadorLogsService.AdicionarLogWebhookFornecedorSolicitacaoSaque(@event.IdWebhook, solicitacaoSaque.Id, ObterAcao(@event.Status));

                if (!adicional)
                    publishNotificarCliente.Publicar(@event);

                return true;
            }
        }

        private async Task AdicionarLogEnvioWebhookSolicitacaoSaque(ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, string bodyJson, HttpResponseMessage httpResponseMessage, string UrlWebhook)
        {
            centralizadorLogsService.AdicionarLogEnvioWebhookSolicitacaoSaque(
                solicitacaoSaque.Id,
                solicitacaoSaque.Codigo,
                "ENVIO DE PIX",
                UrlWebhook!,
                bodyJson,
                $"{httpResponseMessage.StatusCode}",
                await httpResponseMessage.Content.ReadAsStringAsync());
        }

        private static string ObterAcao(string status)
        {
            return (status ?? string.Empty) switch
            {
                "REJECTED TRANSACTION" => "PIX REJEITADO",
                "RETURNED TRANSACTION" => "PIX EXPIRADO",
                "INSTABILITY" => "PIX COM ERRO",
                _ => status ?? string.Empty
            };
        }

        private static HttpContent ObterBodyContent(SaqueConfirmadoNotificarClienteEvent @event, ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, out string bodyJson)
        {
            if (@event.Sucesso == true)
                return ObterBodyContentSucesso(@event, solicitacaoSaque, out bodyJson);

            return ObterBodyContentFalha(@event, solicitacaoSaque, out bodyJson);
        }

        private static HttpContent ObterBodyContentFalha(SaqueConfirmadoNotificarClienteEvent @event, ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, out string bodyJson)
        {
            var body = new
            {
                withdrawCode = solicitacaoSaque.Codigo,
                customId = solicitacaoSaque.CustomId,
                customUserId = solicitacaoSaque.CustomUserId,
                transactionId = solicitacaoSaque.Guid,
                transactionCode = solicitacaoSaque.CodigoTransacao,
                updateCode = "03",
                updateMessage = "REJECTED TRANSACTION",
                amount = Convert.ToInt64(solicitacaoSaque.ValorSolicitado * 100),
                reason = solicitacaoSaque.JustificativaEstorno
            };

            return new StringContent(bodyJson = body.ToJson(), Encoding.UTF8, System.Net.Mime.MediaTypeNames.Application.Json);
        }

        private static HttpContent ObterBodyContentSucesso(SaqueConfirmadoNotificarClienteEvent @event, ViewSolicitacaoSaqueDadosBasicos solicitacaoSaque, out string bodyJson)
        {
            var transacionData = new
            {
                operationType = solicitacaoSaque.TipoOperacaoBancaria,
                transactionDate = solicitacaoSaque.DataSolicitacao,
                completedDate = solicitacaoSaque.DataConclusao,
                chargebackDate = solicitacaoSaque.DataEstorno,
                paymentDate = solicitacaoSaque.DataPagamento
            };

            var recipientData = new
            {
                recipientName = solicitacaoSaque.NomeRecebedor ?? solicitacaoSaque.NomeFantasiaRecebedor,
                recipientDocumentID = solicitacaoSaque.IdCliente == 400 ? solicitacaoSaque.CPFCNPJFavorecido : solicitacaoSaque.CPFCNPJRecebedor,
                recipientBankIspb = solicitacaoSaque.ISPBRecebedor,
                recipientBankName = solicitacaoSaque.BancoRecebedor,
                recipientBankAgency = solicitacaoSaque.AgenciaRecebedor,
                recipientBankAccount = solicitacaoSaque.ContaRecebedor,
                recipientPIXKeyType = solicitacaoSaque.TipoChavePIX,
                recipientPIXKey = solicitacaoSaque.ChavePIX
            };

            var body = new
            {
                error = false,
                returnCode = "00",
                returnMessage = "Success",
                withdrawCode = solicitacaoSaque.Codigo,
                customId = solicitacaoSaque.CustomId,
                customUserId = solicitacaoSaque.CustomUserId,
                transactionId = solicitacaoSaque.Guid,
                authenticationCode = solicitacaoSaque.CodigoAutenticacao,
                transactionCode = solicitacaoSaque.CodigoTransacao,
                amount = Convert.ToInt64(solicitacaoSaque.ValorSolicitado * 100),
                transaction = transacionData,
                recipient = recipientData,
                requestDate = solicitacaoSaque.DataSolicitacao,
                paymentDate = solicitacaoSaque.DataPagamento,
                chargebackDate = solicitacaoSaque.DataEstorno,
                completionDate = solicitacaoSaque.DataConclusao,
                endToEnd = solicitacaoSaque.CodigoTransacao,
                status = @event.Status,

            };

            return new StringContent(bodyJson = body.ToJson(), Encoding.UTF8, System.Net.Mime.MediaTypeNames.Application.Json);
        }
    }
}