﻿namespace Multipay.Queue.Webhook.Core.Models
{
    public class InconsistenciaCobrancaModel
    {
        public int IdCobranca { get; set; }
        public byte IdFormaPagamento { get; set; }
        public byte IdTipoInconsistencia { get; set; }
        public decimal ValorCobranca { get; set; }
        public decimal ValorCobrado { get; set; }
        public decimal ValorPago { get; set; }
        public bool ComplementoPagamento { get; set; }
        public byte Parcela { get; set; }
        public string MoedaOrigem { get; set; } = string.Empty;
        public string Moeda { get; set; } = string.Empty;
        public DateTime DataPagamento { get; set; }
        public string Observacao { get; set; } = string.Empty;
    }
}