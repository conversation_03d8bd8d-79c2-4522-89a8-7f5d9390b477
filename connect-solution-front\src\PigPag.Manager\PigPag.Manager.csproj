﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.19.6\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props" Condition="Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.19.6\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props')" />
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{6D5FE874-0D09-4112-9860-0DD6397557BD}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>PigPag.Manager</RootNamespace>
    <AssemblyName>PigPag.Manager</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress>
    </Use64BitIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
    <DockerLaunchAction>LaunchBrowser</DockerLaunchAction>
    <DockerLaunchUrl>http://{ServiceIPAddress}</DockerLaunchUrl>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=*******, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.8.1.0\lib\net461\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.Core, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.Core.3.7.303.14\lib\net45\AWSSDK.Core.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.SecretsManager, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.SecretsManager.3.7.302.49\lib\net45\AWSSDK.SecretsManager.dll</HintPath>
    </Reference>
    <Reference Include="ClosedXML, Version=0.94.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ClosedXML.0.94.2\lib\net46\ClosedXML.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper, Version=2*******, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
      <HintPath>..\packages\CsvHelper.27.0.1\lib\net47\CsvHelper.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.2.1.35\lib\net461\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.7.2.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.7.2\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="ExcelDataReader, Version=3.7.0.0, Culture=neutral, PublicKeyToken=93517dbe6a4012fa, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelDataReader.3.7.0\lib\net462\ExcelDataReader.dll</HintPath>
    </Reference>
    <Reference Include="ExcelDataReader.DataSet, Version=3.7.0.0, Culture=neutral, PublicKeyToken=93517dbe6a4012fa, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelDataReader.DataSet.3.7.0\lib\net462\ExcelDataReader.DataSet.dll</HintPath>
    </Reference>
    <Reference Include="ExcelNumberFormat, Version=1.0.3.0, Culture=neutral, PublicKeyToken=23c6f5d73be07eca, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelNumberFormat.1.0.3\lib\net20\ExcelNumberFormat.dll</HintPath>
    </Reference>
    <Reference Include="FastMember, Version=1.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FastMember.1.5.0\lib\net461\FastMember.dll</HintPath>
    </Reference>
    <Reference Include="LinqKit, Version=1.2.5.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
      <HintPath>..\packages\LinqKit.1.2.5\lib\net45\LinqKit.dll</HintPath>
    </Reference>
    <Reference Include="LinqKit.Core, Version=1.2.5.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
      <HintPath>..\packages\LinqKit.Core.1.2.5\lib\net45\LinqKit.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Authentication.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Authentication.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authentication.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Authentication.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Authentication.Core.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authentication.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Authorization, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Authorization.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Authorization.Policy, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Authorization.Policy.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authorization.Policy.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Hosting.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Hosting.Server.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Hosting.Server.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Extensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Extensions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Features.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Mvc.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.Core, Version=2.2.5.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Mvc.Core.2.2.5\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.ResponseCaching.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.ResponseCaching.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Routing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Routing.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Routing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Routing.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Routing.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Routing.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.WebUtilities, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.WebUtilities.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.WebUtilities.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.HashCode, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.HashCode.1.1.0\lib\net461\Microsoft.Bcl.HashCode.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Build.Utilities.v4.0" />
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=4.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\lib\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.DotNet.PlatformAbstractions, Version=2.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.DotNet.PlatformAbstractions.2.1.0\lib\net45\Microsoft.DotNet.PlatformAbstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.Caching.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.8.0.0\lib\net462\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.FileExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.FileExtensions.8.0.0\lib\net462\Microsoft.Extensions.Configuration.FileExtensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Json, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Json.8.0.0\lib\net462\Microsoft.Extensions.Configuration.Json.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyModel, Version=2.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyModel.2.1.0\lib\net451\Microsoft.Extensions.DependencyModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Physical, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Physical.8.0.0\lib\net462\Microsoft.Extensions.FileProviders.Physical.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileSystemGlobbing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileSystemGlobbing.8.0.0\lib\net462\Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Hosting.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.8.0.0\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.ObjectPool, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.ObjectPool.2.2.0\lib\netstandard2.0\Microsoft.Extensions.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=8.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.8.0.2\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.8.0.0\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client, Version=4.59.0.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.4.59.0\lib\net462\Microsoft.Identity.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client.Extensions.Msal, Version=4.59.0.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.Extensions.Msal.4.59.0\lib\netstandard2.0\Microsoft.Identity.Client.Extensions.Msal.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=7.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.7.3.1\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=7.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.7.3.1\lib\net472\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=7.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.7.3.1\lib\net472\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols, Version=7.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.7.3.1\lib\net472\Microsoft.IdentityModel.Protocols.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect, Version=7.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.OpenIdConnect.7.3.1\lib\net472\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=7.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.7.3.1\lib\net472\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Net.Http.Headers, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.Headers.2.2.0\lib\netstandard2.0\Microsoft.Net.Http.Headers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.2.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="OFXParser, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\OFXParser.1.0.6\lib\net48\OFXParser.dll</HintPath>
    </Reference>
    <Reference Include="PagedList, Version=********, Culture=neutral, PublicKeyToken=abbb863e9397c5e1, processorArchitecture=MSIL">
      <HintPath>..\packages\PagedList.********\lib\net40\PagedList.dll</HintPath>
    </Reference>
    <Reference Include="Pipelines.Sockets.Unofficial, Version=*******, Culture=neutral, PublicKeyToken=42ea0a778e13fbe2, processorArchitecture=MSIL">
      <HintPath>..\packages\Pipelines.Sockets.Unofficial.2.2.8\lib\net472\Pipelines.Sockets.Unofficial.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=10*******, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.106.10.1\lib\net452\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Common, Version=*******, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\packages\ServiceStack.Common.8.4.0\lib\net472\ServiceStack.Common.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Interfaces, Version=*******, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\packages\ServiceStack.Interfaces.8.4.0\lib\net472\ServiceStack.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Redis, Version=*******, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\packages\ServiceStack.Redis.8.4.0\lib\net472\ServiceStack.Redis.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Text, Version=*******, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\packages\ServiceStack.Text.8.4.0\lib\net472\ServiceStack.Text.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=2.0.0.0, Culture=neutral, PublicKeyToken=c219ff1ca8c2ce46, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.2.8.12\lib\net472\StackExchange.Redis.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.Composition.Registration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.8.0.0\lib\net462\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Data.SqlClient, Version=4.6.1.6, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SqlClient.4.8.6\lib\net461\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.8.0.1\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=7.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.7.3.1\lib\net472\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.AccessControl.5.0.0\lib\net461\System.IO.FileSystem.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=4.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Packaging.8.0.0\lib\net462\System.IO.Packaging.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.5.0.1\lib\net461\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.Json.8.0.0\lib\net462\System.Net.Http.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.6.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.ProtectedData, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.ProtectedData.8.0.0\lib\net462\System.Security.Cryptography.ProtectedData.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates" />
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.8.0.0\lib\net462\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.8.0.5\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.7.0.0\lib\net462\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.3.0\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.3.0\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="bootstrap\css\bootstrap.css" />
    <Content Include="bootstrap\css\bootstrap.min.css" />
    <Content Include="bootstrap\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="bootstrap\js\bootstrap.js" />
    <Content Include="bootstrap\js\bootstrap.min.js" />
    <Content Include="bootstrap\js\npm.js" />
    <Content Include="css\maps\jquery-jvectormap-2.0.3.css" />
    <Content Include="css\icons.css" />
    <Content Include="css\style.css" />
    <Content Include="css\View\Dashboard\dashboard.css" />
    <Content Include="css\View\AccessDenied\acesso-negado.css" />
    <Content Include="css\View\GerenciarAcessoPerfil\gerenciar-acesso-perfil.css" />
    <Content Include="css\View\LimiteSaqueCobranca\limite-saque-cobranca.css" />
    <Content Include="dist\css\AdminLTE.css" />
    <Content Include="dist\css\AdminLTE.min.css" />
    <Content Include="dist\css\skins\skin-black-light.css" />
    <Content Include="dist\css\skins\skin-black-light.min.css" />
    <Content Include="dist\css\skins\skin-black.css" />
    <Content Include="dist\css\skins\skin-black.min.css" />
    <Content Include="dist\css\skins\skin-blue-light.css" />
    <Content Include="dist\css\skins\skin-blue-light.min.css" />
    <Content Include="dist\css\skins\skin-blue.css" />
    <Content Include="dist\css\skins\skin-blue.min.css" />
    <Content Include="dist\css\skins\skin-green-light.css" />
    <Content Include="dist\css\skins\skin-green-light.min.css" />
    <Content Include="dist\css\skins\skin-green.css" />
    <Content Include="dist\css\skins\skin-green.min.css" />
    <Content Include="dist\css\skins\skin-purple-light.css" />
    <Content Include="dist\css\skins\skin-purple-light.min.css" />
    <Content Include="dist\css\skins\skin-purple.css" />
    <Content Include="dist\css\skins\skin-purple.min.css" />
    <Content Include="dist\css\skins\skin-red-light.css" />
    <Content Include="dist\css\skins\skin-red-light.min.css" />
    <Content Include="dist\css\skins\skin-red.css" />
    <Content Include="dist\css\skins\skin-red.min.css" />
    <Content Include="dist\css\skins\skin-yellow-light.css" />
    <Content Include="dist\css\skins\skin-yellow-light.min.css" />
    <Content Include="dist\css\skins\skin-yellow.css" />
    <Content Include="dist\css\skins\skin-yellow.min.css" />
    <Content Include="dist\css\skins\_all-skins.css" />
    <Content Include="dist\css\skins\_all-skins.min.css" />
    <Content Include="dist\img\avatar.png" />
    <Content Include="dist\img\avatar04.png" />
    <Content Include="dist\img\avatar2.png" />
    <Content Include="dist\img\avatar3.png" />
    <Content Include="dist\img\avatar5.png" />
    <Content Include="dist\img\boxed-bg.jpg" />
    <Content Include="dist\img\boxed-bg.png" />
    <Content Include="dist\img\credit\american-express.png" />
    <Content Include="dist\img\credit\cirrus.png" />
    <Content Include="dist\img\credit\mastercard.png" />
    <Content Include="dist\img\credit\mestro.png" />
    <Content Include="dist\img\credit\paypal.png" />
    <Content Include="dist\img\credit\paypal2.png" />
    <Content Include="dist\img\credit\visa.png" />
    <Content Include="dist\img\default-50x50.gif" />
    <Content Include="dist\img\icons.png" />
    <Content Include="dist\img\photo1.png" />
    <Content Include="dist\img\photo2.png" />
    <Content Include="dist\img\photo3.jpg" />
    <Content Include="dist\img\photo4.jpg" />
    <Content Include="dist\img\user1-128x128.jpg" />
    <Content Include="dist\img\user2-160x160.jpg" />
    <Content Include="dist\img\user3-128x128.jpg" />
    <Content Include="dist\img\user4-128x128.jpg" />
    <Content Include="dist\img\user5-128x128.jpg" />
    <Content Include="dist\img\user6-128x128.jpg" />
    <Content Include="dist\img\user7-128x128.jpg" />
    <Content Include="dist\img\user8-128x128.jpg" />
    <Content Include="dist\js\app.js" />
    <Content Include="dist\js\app.min.js" />
    <Content Include="dist\js\demo.js" />
    <Content Include="dist\js\pages\dashboard.js" />
    <Content Include="dist\js\pages\dashboard2.js" />
    <Content Include="fontawesome6\css\all.css" />
    <Content Include="fontawesome6\css\all.min.css" />
    <Content Include="fontawesome6\css\brands.css" />
    <Content Include="fontawesome6\css\brands.min.css" />
    <Content Include="fontawesome6\css\fontawesome.css" />
    <Content Include="fontawesome6\css\fontawesome.min.css" />
    <Content Include="fontawesome6\css\regular.css" />
    <Content Include="fontawesome6\css\regular.min.css" />
    <Content Include="fontawesome6\css\solid.css" />
    <Content Include="fontawesome6\css\solid.min.css" />
    <Content Include="fontawesome6\css\svg-with-js.css" />
    <Content Include="fontawesome6\css\svg-with-js.min.css" />
    <Content Include="fontawesome6\css\v4-font-face.css" />
    <Content Include="fontawesome6\css\v4-font-face.min.css" />
    <Content Include="fontawesome6\css\v4-shims.css" />
    <Content Include="fontawesome6\css\v4-shims.min.css" />
    <Content Include="fontawesome6\css\v5-font-face.css" />
    <Content Include="fontawesome6\css\v5-font-face.min.css" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="fonts\pix.svg" />
    <Content Include="fonts\robot-11.svg" />
    <Content Include="fonts\robot-3.svg" />
    <Content Include="Global.asax" />
    <Content Include="images\acesso_negado.png" />
    <Content Include="images\american-express.png" />
    <Content Include="images\mastercard.png" />
    <Content Include="images\media.jpg" />
    <Content Include="images\paypal.png" />
    <Content Include="images\user.png" />
    <Content Include="images\visa.png" />
    <Content Include="Image\admin-logo-dark.png" />
    <Content Include="Image\admin-logo.png" />
    <Content Include="Image\alert-icon.png" />
    <Content Include="Image\background.jpg" />
    <Content Include="Image\bonecocinza.png" />
    <Content Include="Image\br.png" />
    <Content Include="Image\confirmed_icon.png" />
    <Content Include="Image\denied_icon.png" />
    <Content Include="Image\es.png" />
    <Content Include="Image\expired_icon.png" />
    <Content Include="Image\favicon.png" />
    <Content Include="Image\FavIconMSXWallet.png" />
    <Content Include="Image\FavIconMSXWallet_bk.png" />
    <Content Include="Image\icone-usuario-inativo.jpg" />
    <Content Include="Image\loader.gif" />
    <Content Include="Image\logo.png" />
    <Content Include="Image\logo_sistema.png" />
    <Content Include="Image\processing_icon.png" />
    <Content Include="Image\produto_sem_foto_800_300.png" />
    <Content Include="Image\produto_sem_foto_800_500.png" />
    <Content Include="Image\pt.png" />
    <Content Include="Image\us.png" />
    <Content Include="js\datepicker\daterangepicker.js" />
    <Content Include="js\moment\moment.min.js" />
    <Content Include="js\Views\ContaBancariaEmpresas\ContaBancariaEmpresas.js" />
    <Content Include="plugins\jQuery\jquery-3.7.1.min.js" />
    <Content Include="plugins\notify\notify.min.js" />
    <Content Include="plugins\select2\select2-bootstrap.min.css" />
    <Content Include="plugins\whirl\whirl.basic.css" />
    <Content Include="scripts\ChavePix.js" />
    <Content Include="scripts\PigPag.Common.DataTable.js" />
    <Content Include="scripts\View\LimiteSaqueCobranca\limite-saque-cobranca-cadastro.js" />
    <Content Include="scripts\View\LimiteSaqueCobranca\limite-saque-cobranca.js" />
    <Content Include="scripts\View\WebhookNotificarCliente\index.js" />
    <Content Include="scripts\View\Dashboard\Dashboard.js" />
    <Content Include="scripts\View\AccessDenied\acesso-negado.js" />
    <Content Include="scripts\View\Cliente\detalhe-cliente.js" />
    <Content Include="lib\jszip\jszip.js" />
    <Content Include="lib\jszip\jszip.min.js" />
    <Content Include="plugins\bootstrap-slider\bootstrap-slider.js" />
    <Content Include="plugins\bootstrap-slider\slider.css" />
    <Content Include="plugins\bootstrap-wysihtml5\bootstrap3-wysihtml5.all.js" />
    <Content Include="plugins\bootstrap-wysihtml5\bootstrap3-wysihtml5.all.min.js" />
    <Content Include="plugins\bootstrap-wysihtml5\bootstrap3-wysihtml5.css" />
    <Content Include="plugins\bootstrap-wysihtml5\bootstrap3-wysihtml5.min.css" />
    <Content Include="plugins\bower_components\Animatedbg\css\component.css" />
    <Content Include="plugins\bower_components\Animatedbg\css\demo.css" />
    <Content Include="plugins\bower_components\Animatedbg\css\normalize.css" />
    <Content Include="plugins\bower_components\Animatedbg\img\deco.svg" />
    <Content Include="plugins\bower_components\Animatedbg\img\demo-1-bg.jpg" />
    <Content Include="plugins\bower_components\Animatedbg\img\demo-2-bg.jpg" />
    <Content Include="plugins\bower_components\Animatedbg\js\demo-1.js" />
    <Content Include="plugins\bower_components\Animatedbg\js\demo-2.js" />
    <Content Include="plugins\bower_components\Animatedbg\js\demo-3.js" />
    <Content Include="plugins\bower_components\Animatedbg\js\demo-4.js" />
    <Content Include="plugins\bower_components\Animatedbg\js\EasePack.min.js" />
    <Content Include="plugins\bower_components\Animatedbg\js\rAF.js" />
    <Content Include="plugins\bower_components\Animatedbg\js\TweenLite.min.js" />
    <Content Include="plugins\bower_components\AnimatedSkillsDiagram\css\default.css" />
    <Content Include="plugins\bower_components\AnimatedSkillsDiagram\css\reset.css" />
    <Content Include="plugins\bower_components\AnimatedSkillsDiagram\index.html" />
    <Content Include="plugins\bower_components\AnimatedSkillsDiagram\js\animated-bar.js" />
    <Content Include="plugins\bower_components\AnimatedSkillsDiagram\js\jquery.js" />
    <Content Include="plugins\bower_components\AnimatedSkillsDiagram\js\raphael.js" />
    <Content Include="plugins\bower_components\blockUI\jquery.blockUI.js" />
    <Content Include="plugins\bower_components\bootstrap-datepicker\bootstrap-datepicker.min.css" />
    <Content Include="plugins\bower_components\bootstrap-datepicker\bootstrap-datepicker.min.js" />
    <Content Include="plugins\bower_components\bootstrap-daterangepicker\daterangepicker.css" />
    <Content Include="plugins\bower_components\bootstrap-daterangepicker\daterangepicker.js" />
    <Content Include="plugins\bower_components\bootstrap-daterangepicker\drp.png" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\css\bootstrap-theme.css" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\css\bootstrap-theme.min.css" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\css\bootstrap.css" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\css\bootstrap.min.css" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\js\bootstrap.js" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\js\bootstrap.min.js" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\js\npm.js" />
    <Content Include="plugins\bower_components\bootstrap-old\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="plugins\bower_components\bootstrap-old\Gruntfile.js" />
    <Content Include="plugins\bower_components\bootstrap-old\grunt\bs-commonjs-generator.js" />
    <Content Include="plugins\bower_components\bootstrap-old\grunt\bs-glyphicons-data-generator.js" />
    <Content Include="plugins\bower_components\bootstrap-old\grunt\bs-lessdoc-parser.js" />
    <Content Include="plugins\bower_components\bootstrap-old\grunt\bs-raw-files-generator.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\affix.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\alert.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\button.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\carousel.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\collapse.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\dropdown.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\modal.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\popover.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\scrollspy.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\tab.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\tooltip.js" />
    <Content Include="plugins\bower_components\bootstrap-old\js\transition.js" />
    <Content Include="plugins\bower_components\bootstrap-old\package.js" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\.gitignore" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\.gitmodules" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\dist\css\bootstrap-rtl.min.css" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\dist\css\bootstrap-theme-rtl.min.css" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\dist\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\dist\js\bootstrap-rtl.js" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\dist\js\bootstrap-rtl.min.js" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\src\css\bootstrap-rtl.css" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\src\css\bootstrap-theme-rtl.css" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\src\js\bootstrap-rtl.js" />
    <Content Include="plugins\bower_components\bootstrap-select\bootstrap-select.min.css" />
    <Content Include="plugins\bower_components\bootstrap-select\bootstrap-select.min.js" />
    <Content Include="plugins\bower_components\bootstrap-social\.gitignore" />
    <Content Include="plugins\bower_components\bootstrap-social\bootstrap-social.css" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\bootstrap-table-all.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\bootstrap-table-all.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\bootstrap-table-locale-all.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\bootstrap-table-locale-all.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\bootstrap-table.css" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\bootstrap-table.ints.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\bootstrap-table.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\bootstrap-table.min.css" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\bootstrap-table.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\cookie\bootstrap-table-cookie.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\cookie\bootstrap-table-cookie.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\editable\bootstrap-table-editable.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\editable\bootstrap-table-editable.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\export\bootstrap-table-export.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\export\bootstrap-table-export.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\filter-control\bootstrap-table-filter-control.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\filter-control\bootstrap-table-filter-control.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\filter\bootstrap-table-filter.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\filter\bootstrap-table-filter.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\flat-json\bootstrap-table-flat-json.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\flat-json\bootstrap-table-flat-json.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\key-events\bootstrap-table-key-events.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\key-events\bootstrap-table-key-events.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\mobile\bootstrap-table-mobile.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\mobile\bootstrap-table-mobile.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\multiple-sort\bootstrap-table-multiple-sort.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\multiple-sort\bootstrap-table-multiple-sort.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\natural-sorting\bootstrap-table-natural-sorting.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\natural-sorting\bootstrap-table-natural-sorting.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\reorder-columns\bootstrap-table-reorder-columns.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\reorder-columns\bootstrap-table-reorder-columns.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\reorder-rows\bootstrap-table-reorder-rows.css" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\reorder-rows\bootstrap-table-reorder-rows.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\reorder-rows\bootstrap-table-reorder-rows.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\resizable\bootstrap-table-resizable.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\resizable\bootstrap-table-resizable.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\toolbar\bootstrap-table-toolbar.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\extensions\toolbar\bootstrap-table-toolbar.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ar-SA.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ar-SA.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-cs-CZ.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-cs-CZ.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-da-DK.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-da-DK.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-de-DE.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-de-DE.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-el-GR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-el-GR.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-en-US.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-en-US.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-AR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-AR.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-CR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-CR.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-MX.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-MX.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-NI.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-NI.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-SP.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-es-SP.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-fr-BE.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-fr-BE.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-fr-FR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-fr-FR.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-hu-HU.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-hu-HU.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-it-IT.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-it-IT.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ja-JP.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ja-JP.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ka-GE.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ka-GE.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ko-KR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ko-KR.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ms-MY.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ms-MY.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-nb-NO.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-nb-NO.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-nl-NL.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-nl-NL.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-pl-PL.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-pl-PL.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-pt-BR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-pt-BR.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-pt-PT.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-pt-PT.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ro-RO.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ro-RO.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ru-RU.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ru-RU.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-sk-SK.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-sk-SK.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-sv-SE.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-sv-SE.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-th-TH.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-th-TH.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-tr-TR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-tr-TR.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-uk-UA.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-uk-UA.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ur-PK.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-ur-PK.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-vi-VN.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-vi-VN.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-zh-CN.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-zh-CN.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-zh-TW.js" />
    <Content Include="plugins\bower_components\bootstrap-table\dist\locale\bootstrap-table-zh-TW.min.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\bootstrap-table.css" />
    <Content Include="plugins\bower_components\bootstrap-table\src\bootstrap-table.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\cookie\bootstrap-table-cookie.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\editable\bootstrap-table-editable.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\export\bootstrap-table-export.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\filter-control\bootstrap-table-filter-control.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\filter\bootstrap-table-filter.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\flat-json\bootstrap-table-flat-json.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\key-events\bootstrap-table-key-events.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\mobile\bootstrap-table-mobile.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\multiple-sort\bootstrap-table-multiple-sort.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\natural-sorting\bootstrap-table-natural-sorting.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\reorder-columns\bootstrap-table-reorder-columns.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\reorder-rows\bootstrap-table-reorder-rows.css" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\reorder-rows\bootstrap-table-reorder-rows.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\resizable\bootstrap-table-resizable.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\toolbar\bootstrap-table-toolbar.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-ar-SA.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-cs-CZ.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-da-DK.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-de-DE.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-el-GR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-en-US.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-es-AR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-es-CR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-es-MX.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-es-NI.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-es-SP.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-fr-BE.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-fr-FR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-hu-HU.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-it-IT.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-ja-JP.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-ka-GE.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-ko-KR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-ms-MY.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-nb-NO.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-nl-NL.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-pl-PL.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-pt-BR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-pt-PT.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-ro-RO.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-ru-RU.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-sk-SK.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-sv-SE.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-th-TH.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-tr-TR.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-uk-UA.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-ur-PK.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-vi-VN.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-zh-CN.js" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-zh-TW.js" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\css\bootstrap-tagsinput.css" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\dist\bootstrap-tagsinput-angular.js" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\dist\bootstrap-tagsinput-angular.min.js" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\dist\bootstrap-tagsinput.css" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\dist\bootstrap-tagsinput.js" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\dist\bootstrap-tagsinput.min.js" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\src\bootstrap-tagsinput-angular.js" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\src\bootstrap-tagsinput.css" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\src\bootstrap-tagsinput.js" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\css\bootstrap-toggle.css" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\css\bootstrap-toggle.min.css" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\css\bootstrap2-toggle.css" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\css\bootstrap2-toggle.min.css" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\js\bootstrap-toggle.js" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\js\bootstrap-toggle.min.js" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\js\bootstrap2-toggle.js" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\js\bootstrap2-toggle.min.js" />
    <Content Include="plugins\bower_components\bootstrap-touchspin\dist\jquery.bootstrap-touchspin.css" />
    <Content Include="plugins\bower_components\bootstrap-touchspin\dist\jquery.bootstrap-touchspin.js" />
    <Content Include="plugins\bower_components\bootstrap-touchspin\dist\jquery.bootstrap-touchspin.min.css" />
    <Content Include="plugins\bower_components\bootstrap-touchspin\dist\jquery.bootstrap-touchspin.min.js" />
    <Content Include="plugins\bower_components\bootstrap-touchspin\src\jquery.bootstrap-touchspin.css" />
    <Content Include="plugins\bower_components\bootstrap-touchspin\src\jquery.bootstrap-touchspin.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\.gitattributes" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\.gitignore" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\app.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\dist\bootstrap-treeview-init.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\dist\bootstrap-treeview.min.css" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\dist\bootstrap-treeview.min.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\Gruntfile.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\public\css\bootstrap-treeview.css" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\public\example-dom.html" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\public\index.html" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\public\js\bootstrap-treeview.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\screenshot\default.PNG" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\src\css\bootstrap-treeview.css" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\src\js\bootstrap-treeview.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\tests\lib\blanket.min.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\tests\lib\bootstrap-treeview.css" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\tests\lib\bootstrap-treeview.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\tests\lib\jquery.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\tests\lib\qunit-1.12.0.css" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\tests\lib\qunit-1.12.0.js" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\tests\tests.html" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\tests\tests.js" />
    <Content Include="plugins\bower_components\calendar\dist\cal-init.js" />
    <Content Include="plugins\bower_components\calendar\dist\fullcalendar.css" />
    <Content Include="plugins\bower_components\calendar\dist\fullcalendar.js" />
    <Content Include="plugins\bower_components\calendar\dist\fullcalendar.min.css" />
    <Content Include="plugins\bower_components\calendar\dist\fullcalendar.min.js" />
    <Content Include="plugins\bower_components\calendar\dist\fullcalendar.print.css" />
    <Content Include="plugins\bower_components\calendar\dist\gcal.js" />
    <Content Include="plugins\bower_components\calendar\dist\jquery.fullcalendar.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang-all.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\ar-ma.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\ar-sa.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\ar-tn.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\ar.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\bg.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\ca.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\cs.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\da.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\de-at.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\de.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\el.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\en-au.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\en-ca.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\en-gb.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\es.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\fa.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\fi.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\fr-ca.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\fr.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\he.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\hi.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\hr.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\hu.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\id.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\is.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\it.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\ja.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\ko.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\lt.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\lv.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\nb.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\nl.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\pl.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\pt-br.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\pt.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\ro.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\ru.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\sk.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\sl.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\sr-cyrl.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\sr.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\sv.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\th.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\tr.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\uk.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\vi.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\zh-cn.js" />
    <Content Include="plugins\bower_components\calendar\dist\lang\zh-tw.js" />
    <Content Include="plugins\bower_components\calendar\jquery-ui.min.js" />
    <Content Include="plugins\bower_components\Chart.js\Chart.js" />
    <Content Include="plugins\bower_components\Chart.js\Chart.min.js" />
    <Content Include="plugins\bower_components\Chart.js\chartjs.init.js" />
    <Content Include="plugins\bower_components\Chart.js\src\Chart.Bar.js" />
    <Content Include="plugins\bower_components\Chart.js\src\Chart.Core.js" />
    <Content Include="plugins\bower_components\Chart.js\src\Chart.Doughnut.js" />
    <Content Include="plugins\bower_components\Chart.js\src\Chart.Line.js" />
    <Content Include="plugins\bower_components\Chart.js\src\Chart.PolarArea.js" />
    <Content Include="plugins\bower_components\Chart.js\src\Chart.Radar.js" />
    <Content Include="plugins\bower_components\chartist-js\dist\chartist-init.css" />
    <Content Include="plugins\bower_components\chartist-js\dist\chartist-init.js" />
    <Content Include="plugins\bower_components\chartist-js\dist\chartist.css" />
    <Content Include="plugins\bower_components\chartist-js\dist\chartist.js" />
    <Content Include="plugins\bower_components\chartist-js\dist\chartist.min.css" />
    <Content Include="plugins\bower_components\chartist-js\dist\chartist.min.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\.gitignore" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\dist\chartist-plugin-tooltip.css" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\dist\chartist-plugin-tooltip.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\dist\chartist-plugin-tooltip.min.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\Gruntfile.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\src\css\chartist-plugin-tooltip.css" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\src\scripts\chartist-plugin-tooltip.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\tasks\clean.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\tasks\copy.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\tasks\jasmine.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\tasks\jshint.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\tasks\sass.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\tasks\uglify.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\tasks\umd.js" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\test\runner.html" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\test\spec\spec-tooltip.js" />
    <Content Include="plugins\bower_components\clockpicker\dist\bootstrap-clockpicker.css" />
    <Content Include="plugins\bower_components\clockpicker\dist\bootstrap-clockpicker.js" />
    <Content Include="plugins\bower_components\clockpicker\dist\bootstrap-clockpicker.min.css" />
    <Content Include="plugins\bower_components\clockpicker\dist\bootstrap-clockpicker.min.js" />
    <Content Include="plugins\bower_components\clockpicker\dist\jquery-clockpicker.css" />
    <Content Include="plugins\bower_components\clockpicker\dist\jquery-clockpicker.js" />
    <Content Include="plugins\bower_components\clockpicker\dist\jquery-clockpicker.min.css" />
    <Content Include="plugins\bower_components\clockpicker\dist\jquery-clockpicker.min.js" />
    <Content Include="plugins\bower_components\colorpicker\bootstrap-colorpicker.js" />
    <Content Include="plugins\bower_components\colorpicker\bootstrap.colorpickersliders.min.css" />
    <Content Include="plugins\bower_components\colorpicker\bootstrap.colorpickersliders.min.js" />
    <Content Include="plugins\bower_components\colorpicker\colorpicker.css" />
    <Content Include="plugins\bower_components\colorpicker\img\alpha.png" />
    <Content Include="plugins\bower_components\colorpicker\img\hue.png" />
    <Content Include="plugins\bower_components\colorpicker\img\saturation.png" />
    <Content Include="plugins\bower_components\counterup\jquery.counterup.min.js" />
    <Content Include="plugins\bower_components\cropper\cropper-init.js" />
    <Content Include="plugins\bower_components\cropper\cropper.css" />
    <Content Include="plugins\bower_components\cropper\cropper.js" />
    <Content Include="plugins\bower_components\cropper\cropper.min.css" />
    <Content Include="plugins\bower_components\cropper\cropper.min.js" />
    <Content Include="plugins\bower_components\css-chart\css-chart.css" />
    <Content Include="plugins\bower_components\custom-select\custom-select-spinner.gif" />
    <Content Include="plugins\bower_components\custom-select\custom-select.css" />
    <Content Include="plugins\bower_components\custom-select\custom-select.js" />
    <Content Include="plugins\bower_components\custom-select\custom-select.min.js" />
    <Content Include="plugins\bower_components\custom-select\select2-spinner.gif" />
    <Content Include="plugins\bower_components\custom-select\select2.png" />
    <Content Include="plugins\bower_components\custom-select\select22x2.png" />
    <Content Include="plugins\bower_components\datatables-plugins\api\average%28%29.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\column%28%29.title%28%29.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\columns%28%29.order%28%29.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnAddDataAndDisplay.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnAddTr.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnColumnIndexToVisible.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnDataUpdate.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnDisplayRow.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnDisplayStart.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnFakeRowspan.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnFilterAll.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnFilterClear.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnFilterOnReturn.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnFindCellRowIndexes.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnFindCellRowNodes.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnGetAdjacentTr.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnGetColumnData.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnGetColumnIndex.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnGetHiddenNodes.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnGetTd.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnGetTds.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnLengthChange.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnMultiFilter.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnPagingInfo.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnProcessingIndicator.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnReloadAjax.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnSetFilteringDelay.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnSortNeutral.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnStandingRedraw.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\fnVisibleToColumnIndex.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\api\page.jumpToData%28%29.js" />
    <Content Include="plugins\bower_components\datatables-plugins\api\sum%28%29.js" />
    <Content Include="plugins\bower_components\datatables-plugins\features\alphabetSearch\dataTables.alphabetSearch.css" />
    <Content Include="plugins\bower_components\datatables-plugins\features\alphabetSearch\dataTables.alphabetSearch.js" />
    <Content Include="plugins\bower_components\datatables-plugins\features\alphabetSearch\dataTables.alphabetSearch.min.js" />
    <Content Include="plugins\bower_components\datatables-plugins\features\lengthLinks\dataTables.lengthLinks.css" />
    <Content Include="plugins\bower_components\datatables-plugins\features\lengthLinks\dataTables.lengthLinks.js" />
    <Content Include="plugins\bower_components\datatables-plugins\features\lengthLinks\dataTables.lengthLinks.min.js" />
    <Content Include="plugins\bower_components\datatables-plugins\features\searchHighlight\dataTables.searchHighlight.css" />
    <Content Include="plugins\bower_components\datatables-plugins\features\searchHighlight\dataTables.searchHighlight.js" />
    <Content Include="plugins\bower_components\datatables-plugins\features\searchHighlight\dataTables.searchHighlight.min.js" />
    <Content Include="plugins\bower_components\datatables-plugins\filtering\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\filtering\row-based\range_dates.js" />
    <Content Include="plugins\bower_components\datatables-plugins\filtering\row-based\range_numbers.js" />
    <Content Include="plugins\bower_components\datatables-plugins\filtering\row-based\TableTools.ShowSelectedOnly.js" />
    <Content Include="plugins\bower_components\datatables-plugins\filtering\type-based\accent-neutralise.js" />
    <Content Include="plugins\bower_components\datatables-plugins\filtering\type-based\html.js" />
    <Content Include="plugins\bower_components\datatables-plugins\filtering\type-based\phoneNumber.js" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\1\dataTables.bootstrap.css" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\1\dataTables.bootstrap.js" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\2\dataTables.bootstrap.css" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\2\dataTables.bootstrap.js" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\2\dataTables.bootstrap.min.js" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\2\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\3\dataTables.bootstrap.css" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\3\dataTables.bootstrap.js" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\3\dataTables.bootstrap.min.js" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\3\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\images\sort_asc.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\images\sort_asc_disabled.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\images\sort_both.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\images\sort_desc.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\bootstrap\images\sort_desc_disabled.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\font-awesome\bootstrap.html" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\font-awesome\dataTables.fontAwesome.css" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\font-awesome\datatables.html" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\font-awesome\foundation.html" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\foundation\dataTables.foundation.css" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\foundation\dataTables.foundation.js" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\foundation\dataTables.foundation.min.js" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\foundation\images\sort_asc.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\foundation\images\sort_asc_disabled.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\foundation\images\sort_both.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\foundation\images\sort_desc.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\foundation\images\sort_desc_disabled.png" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\foundation\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\jqueryui\dataTables.jqueryui.css" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\jqueryui\dataTables.jqueryui.js" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\jqueryui\dataTables.jqueryui.min.js" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\jqueryui\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\pagination\ellipses.js" />
    <Content Include="plugins\bower_components\datatables-plugins\pagination\extjs.js" />
    <Content Include="plugins\bower_components\datatables-plugins\pagination\four_button.js" />
    <Content Include="plugins\bower_components\datatables-plugins\pagination\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\pagination\input.js" />
    <Content Include="plugins\bower_components\datatables-plugins\pagination\jPaginator\dataTables.jPaginator.js" />
    <Content Include="plugins\bower_components\datatables-plugins\pagination\scrolling.js" />
    <Content Include="plugins\bower_components\datatables-plugins\pagination\select.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\alt-string.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\anti-the.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\chinese-string.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\currency.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\custom-data-source\dom-checkbox.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\custom-data-source\dom-select.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\custom-data-source\dom-text.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\date-dd-MMM-yyyy.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\date-de.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\date-eu.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\date-euro.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\date-uk.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\datetime-moment.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\datetime-us.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\enum.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\file-size.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\formatted-numbers.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\ip-address.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\monthYear.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\natural.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\num-html.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\numeric-comma.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\percent.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\persian.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\scientific.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\signed-num.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\stringMonthYear.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\time.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\title-numeric.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\title-string.js" />
    <Content Include="plugins\bower_components\datatables-plugins\sorting\turkish-string.js" />
    <Content Include="plugins\bower_components\datatables-plugins\type-detection\currency.js" />
    <Content Include="plugins\bower_components\datatables-plugins\type-detection\date-uk.js" />
    <Content Include="plugins\bower_components\datatables-plugins\type-detection\file-size.js" />
    <Content Include="plugins\bower_components\datatables-plugins\type-detection\formatted-num.js" />
    <Content Include="plugins\bower_components\datatables-plugins\type-detection\index.html" />
    <Content Include="plugins\bower_components\datatables-plugins\type-detection\ip-address.js" />
    <Content Include="plugins\bower_components\datatables-plugins\type-detection\num-html.js" />
    <Content Include="plugins\bower_components\datatables-plugins\type-detection\numeric-comma.js" />
    <Content Include="plugins\bower_components\datatables-responsive\.gitignore" />
    <Content Include="plugins\bower_components\datatables-responsive\docs\api\responsive.index%28%29.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\docs\api\responsive.rebuild%28%29.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\docs\api\responsive.recalc%28%29.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\docs\option\responsive.breakpoints.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\docs\option\responsive.details.renderer.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\docs\option\responsive.details.target.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\docs\option\responsive.details.type.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\docs\option\responsive.details.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\docs\option\responsive.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\child-rows\column-control.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\child-rows\custom-renderer.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\child-rows\disable-child-rows.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\child-rows\index.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\child-rows\right-column.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\child-rows\whole-row-control.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\display-control\auto.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\display-control\classes.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\display-control\complexHeader.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\display-control\fixedHeader.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\display-control\index.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\display-control\init-classes.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\index.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\initialisation\ajax.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\initialisation\className.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\initialisation\default.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\initialisation\index.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\initialisation\new.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\initialisation\option.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\styling\bootstrap.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\styling\compact.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\styling\foundation.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\styling\index.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\styling\jqueryui.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\examples\styling\scrolling.xml" />
    <Content Include="plugins\bower_components\datatables-responsive\js\dataTables.responsive.js" />
    <Content Include="plugins\bower_components\datatables-responsive\License.txt" />
    <Content Include="plugins\bower_components\datatables\buttons.colVis.min.js" />
    <Content Include="plugins\bower_components\datatables\buttons.html5.min.js" />
    <Content Include="plugins\bower_components\datatables\buttons.print.min.js" />
    <Content Include="plugins\bower_components\datatables\dataTables.bootstrap.js" />
    <Content Include="plugins\bower_components\datatables\dataTables.bootstrap4.min.css" />
    <Content Include="plugins\bower_components\datatables\dataTables.bootstrap4.min.js" />
    <Content Include="plugins\bower_components\datatables\dataTables.select.min.js" />
    <Content Include="plugins\bower_components\datatables\jquery.dataTables.min.css" />
    <Content Include="plugins\bower_components\datatables\jquery.dataTables.min.js" />
    <Content Include="plugins\bower_components\datatables\jszip.min.js" />
    <Content Include="plugins\bower_components\datatables\license.txt" />
    <Content Include="plugins\bower_components\datatables\media\css\dataTables.bootstrap.css" />
    <Content Include="plugins\bower_components\datatables\media\css\dataTables.bootstrap.min.css" />
    <Content Include="plugins\bower_components\datatables\media\css\dataTables.foundation.css" />
    <Content Include="plugins\bower_components\datatables\media\css\dataTables.foundation.min.css" />
    <Content Include="plugins\bower_components\datatables\media\css\dataTables.jqueryui.css" />
    <Content Include="plugins\bower_components\datatables\media\css\dataTables.jqueryui.min.css" />
    <Content Include="plugins\bower_components\datatables\media\css\jquery.dataTables.css" />
    <Content Include="plugins\bower_components\datatables\media\css\jquery.dataTables.min.css" />
    <Content Include="plugins\bower_components\datatables\media\css\jquery.dataTables_themeroller.css" />
    <Content Include="plugins\bower_components\datatables\media\images\favicon.ico" />
    <Content Include="plugins\bower_components\datatables\media\images\sort_asc.png" />
    <Content Include="plugins\bower_components\datatables\media\images\sort_asc_disabled.png" />
    <Content Include="plugins\bower_components\datatables\media\images\sort_both.png" />
    <Content Include="plugins\bower_components\datatables\media\images\sort_desc.png" />
    <Content Include="plugins\bower_components\datatables\media\images\sort_desc_disabled.png" />
    <Content Include="plugins\bower_components\datatables\media\js\dataTables.bootstrap.js" />
    <Content Include="plugins\bower_components\datatables\media\js\dataTables.bootstrap.min.js" />
    <Content Include="plugins\bower_components\datatables\media\js\dataTables.foundation.js" />
    <Content Include="plugins\bower_components\datatables\media\js\dataTables.foundation.min.js" />
    <Content Include="plugins\bower_components\datatables\media\js\dataTables.jqueryui.js" />
    <Content Include="plugins\bower_components\datatables\media\js\dataTables.jqueryui.min.js" />
    <Content Include="plugins\bower_components\datatables\media\js\jquery.dataTables.js" />
    <Content Include="plugins\bower_components\datatables\media\js\jquery.dataTables.min.js" />
    <Content Include="plugins\bower_components\datatables\media\js\jquery.js" />
    <Content Include="plugins\bower_components\datatables\pdfmake.min.js" />
    <Content Include="plugins\bower_components\datatables\select.bootstrap.min.css" />
    <Content Include="plugins\bower_components\datatables\sort_asc.png" />
    <Content Include="plugins\bower_components\datatables\sort_asc_disabled.png" />
    <Content Include="plugins\bower_components\datatables\sort_both.png" />
    <Content Include="plugins\bower_components\datatables\sort_desc.png" />
    <Content Include="plugins\bower_components\datatables\sort_desc_disabled.png" />
    <Content Include="plugins\bower_components\datatables\vfs_fonts.js" />
    <Content Include="plugins\bower_components\daterangepicker\daterangepicker.css" />
    <Content Include="plugins\bower_components\daterangepicker\daterangepicker.min.js" />
    <Content Include="plugins\bower_components\dropify\.gitignore" />
    <Content Include="plugins\bower_components\dropify\dist\css\demo.css" />
    <Content Include="plugins\bower_components\dropify\dist\css\dropify.css" />
    <Content Include="plugins\bower_components\dropify\dist\css\dropify.min.css" />
    <Content Include="plugins\bower_components\dropify\dist\fonts\dropify.svg" />
    <Content Include="plugins\bower_components\dropify\dist\js\dropify.js" />
    <Content Include="plugins\bower_components\dropify\dist\js\dropify.min.js" />
    <Content Include="plugins\bower_components\dropify\gulpfile.js" />
    <Content Include="plugins\bower_components\dropify\index.html" />
    <Content Include="plugins\bower_components\dropify\src\fonts\dropify.svg" />
    <Content Include="plugins\bower_components\dropify\src\images\cover.jpg" />
    <Content Include="plugins\bower_components\dropify\src\images\test-image-1.jpg" />
    <Content Include="plugins\bower_components\dropify\src\images\test-image-2.jpg" />
    <Content Include="plugins\bower_components\dropify\src\js\dropify.js" />
    <Content Include="plugins\bower_components\dropzone-master\.gitignore" />
    <Content Include="plugins\bower_components\dropzone-master\dist\basic.css" />
    <Content Include="plugins\bower_components\dropzone-master\dist\dropzone-amd-module.js" />
    <Content Include="plugins\bower_components\dropzone-master\dist\dropzone.css" />
    <Content Include="plugins\bower_components\dropzone-master\dist\dropzone.js" />
    <Content Include="plugins\bower_components\dropzone-master\dist\min\basic.min.css" />
    <Content Include="plugins\bower_components\dropzone-master\dist\min\dropzone-amd-module.min.js" />
    <Content Include="plugins\bower_components\dropzone-master\dist\min\dropzone.min.css" />
    <Content Include="plugins\bower_components\dropzone-master\dist\min\dropzone.min.js" />
    <Content Include="plugins\bower_components\dropzone-master\index.js" />
    <Content Include="plugins\bower_components\dropzone-master\test\test.html" />
    <Content Include="plugins\bower_components\dropzone-master\test\test.js" />
    <Content Include="plugins\bower_components\fancybox\ekko-lightbox.min.css" />
    <Content Include="plugins\bower_components\fancybox\ekko-lightbox.min.js" />
    <Content Include="plugins\bower_components\flot.tooltip\js\excanvas.min.js" />
    <Content Include="plugins\bower_components\flot.tooltip\js\jquery.flot.js" />
    <Content Include="plugins\bower_components\flot.tooltip\js\jquery.flot.tooltip.js" />
    <Content Include="plugins\bower_components\flot.tooltip\js\jquery.flot.tooltip.min.js" />
    <Content Include="plugins\bower_components\flot.tooltip\js\jquery.flot.tooltip.source.js" />
    <Content Include="plugins\bower_components\flot\.gitignore" />
    <Content Include="plugins\bower_components\flot\examples\ajax\index.html" />
    <Content Include="plugins\bower_components\flot\examples\annotating\index.html" />
    <Content Include="plugins\bower_components\flot\examples\axes-interacting\index.html" />
    <Content Include="plugins\bower_components\flot\examples\axes-multiple\index.html" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\date.js" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\index.html" />
    <Content Include="plugins\bower_components\flot\examples\axes-time\index.html" />
    <Content Include="plugins\bower_components\flot\examples\background.png" />
    <Content Include="plugins\bower_components\flot\examples\basic-options\index.html" />
    <Content Include="plugins\bower_components\flot\examples\basic-usage\index.html" />
    <Content Include="plugins\bower_components\flot\examples\canvas\index.html" />
    <Content Include="plugins\bower_components\flot\examples\categories\index.html" />
    <Content Include="plugins\bower_components\flot\examples\examples.css" />
    <Content Include="plugins\bower_components\flot\examples\image\hs-2004-27-a-large-web.jpg" />
    <Content Include="plugins\bower_components\flot\examples\image\index.html" />
    <Content Include="plugins\bower_components\flot\examples\index.html" />
    <Content Include="plugins\bower_components\flot\examples\interacting\index.html" />
    <Content Include="plugins\bower_components\flot\examples\navigate\arrow-down.gif" />
    <Content Include="plugins\bower_components\flot\examples\navigate\arrow-left.gif" />
    <Content Include="plugins\bower_components\flot\examples\navigate\arrow-right.gif" />
    <Content Include="plugins\bower_components\flot\examples\navigate\arrow-up.gif" />
    <Content Include="plugins\bower_components\flot\examples\navigate\index.html" />
    <Content Include="plugins\bower_components\flot\examples\percentiles\index.html" />
    <Content Include="plugins\bower_components\flot\examples\realtime\index.html" />
    <Content Include="plugins\bower_components\flot\examples\resize\index.html" />
    <Content Include="plugins\bower_components\flot\examples\selection\index.html" />
    <Content Include="plugins\bower_components\flot\examples\series-errorbars\index.html" />
    <Content Include="plugins\bower_components\flot\examples\series-pie\index.html" />
    <Content Include="plugins\bower_components\flot\examples\series-toggle\index.html" />
    <Content Include="plugins\bower_components\flot\examples\series-types\index.html" />
    <Content Include="plugins\bower_components\flot\examples\shared\jquery-ui\jquery-ui.min.css" />
    <Content Include="plugins\bower_components\flot\examples\stacking\index.html" />
    <Content Include="plugins\bower_components\flot\examples\symbols\index.html" />
    <Content Include="plugins\bower_components\flot\examples\threshold\index.html" />
    <Content Include="plugins\bower_components\flot\examples\tracking\index.html" />
    <Content Include="plugins\bower_components\flot\examples\visitors\index.html" />
    <Content Include="plugins\bower_components\flot\examples\zooming\index.html" />
    <Content Include="plugins\bower_components\flot\excanvas.js" />
    <Content Include="plugins\bower_components\flot\excanvas.min.js" />
    <Content Include="plugins\bower_components\flot\jquery.colorhelpers.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.canvas.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.categories.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.crosshair.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.errorbars.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.fillbetween.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.image.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.navigate.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.pie.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.resize.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.selection.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.stack.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.symbol.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.threshold.js" />
    <Content Include="plugins\bower_components\flot\jquery.flot.time.js" />
    <Content Include="plugins\bower_components\flot\jquery.js" />
    <Content Include="plugins\bower_components\flot\LICENSE.txt" />
    <Content Include="plugins\bower_components\footable\css\fonts\footable.svg" />
    <Content Include="plugins\bower_components\footable\css\footable.core.css" />
    <Content Include="plugins\bower_components\footable\js\footable.all.min.js" />
    <Content Include="plugins\bower_components\gallery\css\animated-masonry-gallery.css" />
    <Content Include="plugins\bower_components\gallery\js\animated-masonry-gallery.js" />
    <Content Include="plugins\bower_components\gallery\js\jquery.isotope.min.js" />
    <Content Include="plugins\bower_components\gmaps\gmaps.js" />
    <Content Include="plugins\bower_components\gmaps\gmaps.min.js" />
    <Content Include="plugins\bower_components\gmaps\jquery.gmaps.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.controls.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.core.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.events.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.geofences.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.geometry.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.layers.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.map_types.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.markers.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.native_extensions.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.overlays.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.routes.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.static.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.streetview.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.styles.js" />
    <Content Include="plugins\bower_components\gmaps\lib\gmaps.utils.js" />
    <Content Include="plugins\bower_components\holderjs\.gitattributes" />
    <Content Include="plugins\bower_components\holderjs\.gitignore" />
    <Content Include="plugins\bower_components\holderjs\gulpfile.js" />
    <Content Include="plugins\bower_components\holderjs\holder.js" />
    <Content Include="plugins\bower_components\holderjs\src\augment.js" />
    <Content Include="plugins\bower_components\holderjs\src\holder.js" />
    <Content Include="plugins\bower_components\holderjs\src\ondomready.js" />
    <Content Include="plugins\bower_components\holderjs\src\polyfills.js" />
    <Content Include="plugins\bower_components\holderjs\test\.gitignore" />
    <Content Include="plugins\bower_components\holderjs\test\image.jpg" />
    <Content Include="plugins\bower_components\holderjs\test\index.html" />
    <Content Include="plugins\bower_components\horizontal-timeline\css\horizontal-timeline.css" />
    <Content Include="plugins\bower_components\horizontal-timeline\css\reset.css" />
    <Content Include="plugins\bower_components\horizontal-timeline\img\cd-arrow.svg" />
    <Content Include="plugins\bower_components\horizontal-timeline\js\horizontal-timeline.js" />
    <Content Include="plugins\bower_components\horizontal-timeline\js\jquery-2.1.4.js" />
    <Content Include="plugins\bower_components\horizontal-timeline\js\jquery.mobile.custom.min.js" />
    <Content Include="plugins\bower_components\horizontal-timeline\js\modernizr.js" />
    <Content Include="plugins\bower_components\html5-editor\bootstrap-wysihtml5.css" />
    <Content Include="plugins\bower_components\html5-editor\bootstrap-wysihtml5.js" />
    <Content Include="plugins\bower_components\html5-editor\wysihtml5-0.3.0.js" />
    <Content Include="plugins\bower_components\intlTelInput\css\demo.css" />
    <Content Include="plugins\bower_components\intlTelInput\css\intlTelInput.css" />
    <Content Include="plugins\bower_components\intlTelInput\img\flags%402x.png" />
    <Content Include="plugins\bower_components\intlTelInput\img\flags.png" />
    <Content Include="plugins\bower_components\intlTelInput\js\intlTelInput.min.js" />
    <Content Include="plugins\bower_components\intlTelInput\js\utils.js" />
    <Content Include="plugins\bower_components\ion-rangeslider\css\ion.rangeSlider.css" />
    <Content Include="plugins\bower_components\ion-rangeslider\css\ion.rangeSlider.skinFlat.css" />
    <Content Include="plugins\bower_components\ion-rangeslider\css\ion.rangeSlider.skinHTML5.css" />
    <Content Include="plugins\bower_components\ion-rangeslider\css\ion.rangeSlider.skinModern.css" />
    <Content Include="plugins\bower_components\ion-rangeslider\css\ion.rangeSlider.skinNice.css" />
    <Content Include="plugins\bower_components\ion-rangeslider\css\ion.rangeSlider.skinSimple.css" />
    <Content Include="plugins\bower_components\ion-rangeslider\css\normalize.css" />
    <Content Include="plugins\bower_components\ion-rangeslider\img\sprite-skin-flat.png" />
    <Content Include="plugins\bower_components\ion-rangeslider\img\sprite-skin-modern.png" />
    <Content Include="plugins\bower_components\ion-rangeslider\img\sprite-skin-nice.png" />
    <Content Include="plugins\bower_components\ion-rangeslider\img\sprite-skin-simple.png" />
    <Content Include="plugins\bower_components\ion-rangeslider\js\ion-rangeSlider\ion.rangeSlider-init.js" />
    <Content Include="plugins\bower_components\ion-rangeslider\js\ion-rangeSlider\ion.rangeSlider.js" />
    <Content Include="plugins\bower_components\ion-rangeslider\js\ion-rangeSlider\ion.rangeSlider.min.js" />
    <Content Include="plugins\bower_components\ion-rangeslider\js\vendor\jquery-1.12.3.min.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\.gitattributes" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\.gitignore" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\css\asColorPicker.css" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\demo\css\main.css" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\demo\css\prism.css" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\demo\demo.gif" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\demo\index.html" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\demo\js\jquery.toc.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\demo\js\prism.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\dist\jquery-asColorPicker.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\dist\jquery-asColorPicker.min.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\Gruntfile.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-cn.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-de.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-dk.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-es.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-fi.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-fr.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-it.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-ja.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-ru.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-sv.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\i18n\jquery-asColorInput-tr.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\images\alpha.png" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\images\hue.png" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\images\saturation.png" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\images\transparent.png" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\libs\jquery-asColor.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\libs\jquery-asGradient.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\libs\jquery.min.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\resource.txt" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\alpha.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\buttons.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\clear.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\core.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\gradient.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\hex.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\hue.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\info.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\keyboard.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\palettes.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\preview.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\saturation.js" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\trigger.js" />
    <Content Include="plugins\bower_components\jquery-datatables-editable\dataTables.bootstrap.js" />
    <Content Include="plugins\bower_components\jquery-datatables-editable\datatables.css" />
    <Content Include="plugins\bower_components\jquery-datatables-editable\jquery.dataTables.js" />
    <Content Include="plugins\bower_components\jquery-sparkline\jquery.charts-sparkline.js" />
    <Content Include="plugins\bower_components\jquery-sparkline\jquery.sparkline.min.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\.gitattributes" />
    <Content Include="plugins\bower_components\jquery-steps-master\.gitignore" />
    <Content Include="plugins\bower_components\jquery-steps-master\build\jquery.steps.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\build\jquery.steps.min.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\demo\css\jquery.steps.css" />
    <Content Include="plugins\bower_components\jquery-steps-master\demo\css\main.css" />
    <Content Include="plugins\bower_components\jquery-steps-master\demo\css\normalize.css" />
    <Content Include="plugins\bower_components\jquery-steps-master\demo\index.html" />
    <Content Include="plugins\bower_components\jquery-steps-master\demo\tabs.html" />
    <Content Include="plugins\bower_components\jquery-steps-master\demo\vertical.html" />
    <Content Include="plugins\bower_components\jquery-steps-master\Gruntfile.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\lib\jquery-1.10.2.min.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\lib\jquery-1.11.1.min.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\lib\jquery-1.9.1.min.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\lib\jquery.cookie-1.3.1.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\lib\modernizr-2.6.2.min.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\LICENSE.txt" />
    <Content Include="plugins\bower_components\jquery-steps-master\nuget\NuGet.exe" />
    <Content Include="plugins\bower_components\jquery-steps-master\src\defaults.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\src\enums.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\src\helper.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\src\model.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\src\privates.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\src\publics.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\src\_banner.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\test\index.html" />
    <Content Include="plugins\bower_components\jquery-steps-master\test\jquery.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\test\qunit\qunit-1.11.0.css" />
    <Content Include="plugins\bower_components\jquery-steps-master\test\qunit\qunit-1.11.0.js" />
    <Content Include="plugins\bower_components\jquery-steps-master\test\tests.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\.gitattributes" />
    <Content Include="plugins\bower_components\jquery-wizard-master\.gitignore" />
    <Content Include="plugins\bower_components\jquery-wizard-master\css\wizard.css" />
    <Content Include="plugins\bower_components\jquery-wizard-master\demo\ajax\1.html" />
    <Content Include="plugins\bower_components\jquery-wizard-master\demo\ajax\2.html" />
    <Content Include="plugins\bower_components\jquery-wizard-master\demo\ajax\3.html" />
    <Content Include="plugins\bower_components\jquery-wizard-master\demo\collapse.html" />
    <Content Include="plugins\bower_components\jquery-wizard-master\demo\css\main.css" />
    <Content Include="plugins\bower_components\jquery-wizard-master\demo\index.html" />
    <Content Include="plugins\bower_components\jquery-wizard-master\demo\tabs.html" />
    <Content Include="plugins\bower_components\jquery-wizard-master\dist\jquery-wizard.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\dist\jquery-wizard.min.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\Gruntfile.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\libs\bootstrap\bootstrap.min.css" />
    <Content Include="plugins\bower_components\jquery-wizard-master\libs\bootstrap\bootstrap.min.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\libs\formvalidation\bootstrap.min.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\libs\formvalidation\formValidation.min.css" />
    <Content Include="plugins\bower_components\jquery-wizard-master\libs\formvalidation\formValidation.min.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\libs\jquery-loader.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\libs\jquery\jquery.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\bind.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\defaults.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\intro.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\outro.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\public.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\setup.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\step.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\support.js" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\util.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\dist\angular.easypiechart.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\dist\angular.easypiechart.min.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\dist\easypiechart.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\dist\easypiechart.min.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\dist\jquery.easypiechart.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\dist\jquery.easypiechart.min.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\easy-pie-chart.init.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\src\angular.directive.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\src\easypiechart.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\src\jquery.plugin.js" />
    <Content Include="plugins\bower_components\jquery.easy-pie-chart\src\renderer\canvas.js" />
    <Content Include="plugins\bower_components\jquery\dist\jquery.js" />
    <Content Include="plugins\bower_components\jquery\dist\jquery.min.js" />
    <Content Include="plugins\bower_components\jquery\src\ajax.js" />
    <Content Include="plugins\bower_components\jquery\src\ajax\jsonp.js" />
    <Content Include="plugins\bower_components\jquery\src\ajax\load.js" />
    <Content Include="plugins\bower_components\jquery\src\ajax\parseJSON.js" />
    <Content Include="plugins\bower_components\jquery\src\ajax\parseXML.js" />
    <Content Include="plugins\bower_components\jquery\src\ajax\script.js" />
    <Content Include="plugins\bower_components\jquery\src\ajax\var\nonce.js" />
    <Content Include="plugins\bower_components\jquery\src\ajax\var\rquery.js" />
    <Content Include="plugins\bower_components\jquery\src\ajax\xhr.js" />
    <Content Include="plugins\bower_components\jquery\src\attributes.js" />
    <Content Include="plugins\bower_components\jquery\src\attributes\attr.js" />
    <Content Include="plugins\bower_components\jquery\src\attributes\classes.js" />
    <Content Include="plugins\bower_components\jquery\src\attributes\prop.js" />
    <Content Include="plugins\bower_components\jquery\src\attributes\support.js" />
    <Content Include="plugins\bower_components\jquery\src\attributes\val.js" />
    <Content Include="plugins\bower_components\jquery\src\callbacks.js" />
    <Content Include="plugins\bower_components\jquery\src\core.js" />
    <Content Include="plugins\bower_components\jquery\src\core\access.js" />
    <Content Include="plugins\bower_components\jquery\src\core\init.js" />
    <Content Include="plugins\bower_components\jquery\src\core\parseHTML.js" />
    <Content Include="plugins\bower_components\jquery\src\core\ready.js" />
    <Content Include="plugins\bower_components\jquery\src\core\var\rsingleTag.js" />
    <Content Include="plugins\bower_components\jquery\src\css.js" />
    <Content Include="plugins\bower_components\jquery\src\css\addGetHookIf.js" />
    <Content Include="plugins\bower_components\jquery\src\css\curCSS.js" />
    <Content Include="plugins\bower_components\jquery\src\css\defaultDisplay.js" />
    <Content Include="plugins\bower_components\jquery\src\css\hiddenVisibleSelectors.js" />
    <Content Include="plugins\bower_components\jquery\src\css\support.js" />
    <Content Include="plugins\bower_components\jquery\src\css\swap.js" />
    <Content Include="plugins\bower_components\jquery\src\css\var\cssExpand.js" />
    <Content Include="plugins\bower_components\jquery\src\css\var\getStyles.js" />
    <Content Include="plugins\bower_components\jquery\src\css\var\isHidden.js" />
    <Content Include="plugins\bower_components\jquery\src\css\var\rmargin.js" />
    <Content Include="plugins\bower_components\jquery\src\css\var\rnumnonpx.js" />
    <Content Include="plugins\bower_components\jquery\src\data.js" />
    <Content Include="plugins\bower_components\jquery\src\data\accepts.js" />
    <Content Include="plugins\bower_components\jquery\src\data\Data.js" />
    <Content Include="plugins\bower_components\jquery\src\data\var\data_priv.js" />
    <Content Include="plugins\bower_components\jquery\src\data\var\data_user.js" />
    <Content Include="plugins\bower_components\jquery\src\deferred.js" />
    <Content Include="plugins\bower_components\jquery\src\deprecated.js" />
    <Content Include="plugins\bower_components\jquery\src\dimensions.js" />
    <Content Include="plugins\bower_components\jquery\src\effects.js" />
    <Content Include="plugins\bower_components\jquery\src\effects\animatedSelector.js" />
    <Content Include="plugins\bower_components\jquery\src\effects\Tween.js" />
    <Content Include="plugins\bower_components\jquery\src\event.js" />
    <Content Include="plugins\bower_components\jquery\src\event\ajax.js" />
    <Content Include="plugins\bower_components\jquery\src\event\alias.js" />
    <Content Include="plugins\bower_components\jquery\src\event\support.js" />
    <Content Include="plugins\bower_components\jquery\src\exports\amd.js" />
    <Content Include="plugins\bower_components\jquery\src\exports\global.js" />
    <Content Include="plugins\bower_components\jquery\src\intro.js" />
    <Content Include="plugins\bower_components\jquery\src\jquery.js" />
    <Content Include="plugins\bower_components\jquery\src\manipulation.js" />
    <Content Include="plugins\bower_components\jquery\src\manipulation\support.js" />
    <Content Include="plugins\bower_components\jquery\src\manipulation\var\rcheckableType.js" />
    <Content Include="plugins\bower_components\jquery\src\manipulation\_evalUrl.js" />
    <Content Include="plugins\bower_components\jquery\src\offset.js" />
    <Content Include="plugins\bower_components\jquery\src\outro.js" />
    <Content Include="plugins\bower_components\jquery\src\queue.js" />
    <Content Include="plugins\bower_components\jquery\src\queue\delay.js" />
    <Content Include="plugins\bower_components\jquery\src\selector-native.js" />
    <Content Include="plugins\bower_components\jquery\src\selector-sizzle.js" />
    <Content Include="plugins\bower_components\jquery\src\selector.js" />
    <Content Include="plugins\bower_components\jquery\src\serialize.js" />
    <Content Include="plugins\bower_components\jquery\src\sizzle\dist\sizzle.js" />
    <Content Include="plugins\bower_components\jquery\src\sizzle\dist\sizzle.min.js" />
    <Content Include="plugins\bower_components\jquery\src\traversing.js" />
    <Content Include="plugins\bower_components\jquery\src\traversing\findFilter.js" />
    <Content Include="plugins\bower_components\jquery\src\traversing\var\rneedsContext.js" />
    <Content Include="plugins\bower_components\jquery\src\var\arr.js" />
    <Content Include="plugins\bower_components\jquery\src\var\class2type.js" />
    <Content Include="plugins\bower_components\jquery\src\var\concat.js" />
    <Content Include="plugins\bower_components\jquery\src\var\hasOwn.js" />
    <Content Include="plugins\bower_components\jquery\src\var\indexOf.js" />
    <Content Include="plugins\bower_components\jquery\src\var\pnum.js" />
    <Content Include="plugins\bower_components\jquery\src\var\push.js" />
    <Content Include="plugins\bower_components\jquery\src\var\rnotwhite.js" />
    <Content Include="plugins\bower_components\jquery\src\var\slice.js" />
    <Content Include="plugins\bower_components\jquery\src\var\strundefined.js" />
    <Content Include="plugins\bower_components\jquery\src\var\support.js" />
    <Content Include="plugins\bower_components\jquery\src\var\toString.js" />
    <Content Include="plugins\bower_components\jquery\src\wrap.js" />
    <Content Include="plugins\bower_components\jsgrid\.gitignore" />
    <Content Include="plugins\bower_components\jsgrid\css\icons-2x.png" />
    <Content Include="plugins\bower_components\jsgrid\css\icons.png" />
    <Content Include="plugins\bower_components\jsgrid\css\jsgrid.css" />
    <Content Include="plugins\bower_components\jsgrid\css\theme.css" />
    <Content Include="plugins\bower_components\jsgrid\db.js" />
    <Content Include="plugins\bower_components\jsgrid\external\jquery\jquery-1.8.3.js" />
    <Content Include="plugins\bower_components\jsgrid\external\qunit\qunit-1.10.0.css" />
    <Content Include="plugins\bower_components\jsgrid\external\qunit\qunit-1.10.0.js" />
    <Content Include="plugins\bower_components\jsgrid\Gruntfile.js" />
    <Content Include="plugins\bower_components\jsgrid\src\fields\jsgrid.field.checkbox.js" />
    <Content Include="plugins\bower_components\jsgrid\src\fields\jsgrid.field.control.js" />
    <Content Include="plugins\bower_components\jsgrid\src\fields\jsgrid.field.number.js" />
    <Content Include="plugins\bower_components\jsgrid\src\fields\jsgrid.field.select.js" />
    <Content Include="plugins\bower_components\jsgrid\src\fields\jsgrid.field.text.js" />
    <Content Include="plugins\bower_components\jsgrid\src\fields\jsgrid.field.textarea.js" />
    <Content Include="plugins\bower_components\jsgrid\src\i18n\es.js" />
    <Content Include="plugins\bower_components\jsgrid\src\i18n\fr.js" />
    <Content Include="plugins\bower_components\jsgrid\src\i18n\pl.js" />
    <Content Include="plugins\bower_components\jsgrid\src\i18n\pt-br.js" />
    <Content Include="plugins\bower_components\jsgrid\src\i18n\pt.js" />
    <Content Include="plugins\bower_components\jsgrid\src\i18n\ru.js" />
    <Content Include="plugins\bower_components\jsgrid\src\jsgrid.core.js" />
    <Content Include="plugins\bower_components\jsgrid\src\jsgrid.field.js" />
    <Content Include="plugins\bower_components\jsgrid\src\jsgrid.load-indicator.js" />
    <Content Include="plugins\bower_components\jsgrid\src\jsgrid.load-strategies.js" />
    <Content Include="plugins\bower_components\jsgrid\src\jsgrid.sort-strategies.js" />
    <Content Include="plugins\bower_components\jsgrid\src\jsgrid.validation.js" />
    <Content Include="plugins\bower_components\jsgrid\tests\index.html" />
    <Content Include="plugins\bower_components\jsgrid\tests\jsgrid.field.tests.js" />
    <Content Include="plugins\bower_components\jsgrid\tests\jsgrid.sort-strategies.tests.js" />
    <Content Include="plugins\bower_components\jsgrid\tests\jsgrid.tests.js" />
    <Content Include="plugins\bower_components\jsgrid\tests\jsgrid.validation.tests.js" />
    <Content Include="plugins\bower_components\knob\jquery.knob.js" />
    <Content Include="plugins\bower_components\knob\jquery.knob.min.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\.gitignore" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\dist\jquery.magnific-popup-init.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\dist\jquery.magnific-popup.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\dist\jquery.magnific-popup.min.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\dist\magnific-popup.css" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\Gruntfile.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\libs\jquery-loader.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\libs\jquery\jquery.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\libs\qunit\qunit.css" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\libs\qunit\qunit.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\js\ajax.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\js\core.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\js\gallery.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\js\iframe.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\js\image.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\js\inline.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\js\retina.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\js\zoom.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\index.html" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\site-assets\ajax\test-ajax-2.html" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\site-assets\ajax\test-ajax.html" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\site-assets\site.css" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\third-party-libs\jquery.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\third-party-libs\jquery.min.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\third-party-libs\uglify.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\third-party-libs\zepto.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\third-party-libs\zepto.min.js" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\wordpress.html" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\_includes\analytics.html" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\_includes\build-tool.html" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\_includes\examples.html" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\_includes\logo.html" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\_includes\signup.html" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\_layouts\default.html" />
    <Content Include="plugins\bower_components\magnific-popup\dist\jquery.magnific-popup.js" />
    <Content Include="plugins\bower_components\magnific-popup\dist\jquery.magnific-popup.min.js" />
    <Content Include="plugins\bower_components\magnific-popup\dist\magnific-popup.css" />
    <Content Include="plugins\bower_components\magnific-popup\src\js\ajax.js" />
    <Content Include="plugins\bower_components\magnific-popup\src\js\core.js" />
    <Content Include="plugins\bower_components\magnific-popup\src\js\fastclick.js" />
    <Content Include="plugins\bower_components\magnific-popup\src\js\gallery.js" />
    <Content Include="plugins\bower_components\magnific-popup\src\js\iframe.js" />
    <Content Include="plugins\bower_components\magnific-popup\src\js\image.js" />
    <Content Include="plugins\bower_components\magnific-popup\src\js\inline.js" />
    <Content Include="plugins\bower_components\magnific-popup\src\js\retina.js" />
    <Content Include="plugins\bower_components\magnific-popup\src\js\zoom.js" />
    <Content Include="plugins\bower_components\Minimal-Gauge-chart\.gitattributes" />
    <Content Include="plugins\bower_components\Minimal-Gauge-chart\css\cmGauge.css" />
    <Content Include="plugins\bower_components\Minimal-Gauge-chart\index.html" />
    <Content Include="plugins\bower_components\Minimal-Gauge-chart\js\cmGauge.js" />
    <Content Include="plugins\bower_components\mjolnic-bootstrap-colorpicker\dist\css\bootstrap-colorpicker.css" />
    <Content Include="plugins\bower_components\mjolnic-bootstrap-colorpicker\dist\css\bootstrap-colorpicker.min.css" />
    <Content Include="plugins\bower_components\mjolnic-bootstrap-colorpicker\dist\img\bootstrap-colorpicker\alpha-horizontal.png" />
    <Content Include="plugins\bower_components\mjolnic-bootstrap-colorpicker\dist\img\bootstrap-colorpicker\alpha.png" />
    <Content Include="plugins\bower_components\mjolnic-bootstrap-colorpicker\dist\img\bootstrap-colorpicker\hue-horizontal.png" />
    <Content Include="plugins\bower_components\mjolnic-bootstrap-colorpicker\dist\img\bootstrap-colorpicker\hue.png" />
    <Content Include="plugins\bower_components\mjolnic-bootstrap-colorpicker\dist\img\bootstrap-colorpicker\saturation.png" />
    <Content Include="plugins\bower_components\mjolnic-bootstrap-colorpicker\dist\js\bootstrap-colorpicker.js" />
    <Content Include="plugins\bower_components\mjolnic-bootstrap-colorpicker\dist\js\bootstrap-colorpicker.min.js" />
    <Content Include="plugins\bower_components\mocha\media\logo.svg" />
    <Content Include="plugins\bower_components\mocha\mocha.css" />
    <Content Include="plugins\bower_components\mocha\mocha.js" />
    <Content Include="plugins\bower_components\moment\min\locales.js" />
    <Content Include="plugins\bower_components\moment\min\locales.min.js" />
    <Content Include="plugins\bower_components\moment\min\moment-with-locales.js" />
    <Content Include="plugins\bower_components\moment\min\moment-with-locales.min.js" />
    <Content Include="plugins\bower_components\moment\min\moment.min.js" />
    <Content Include="plugins\bower_components\moment\min\tests.js" />
    <Content Include="plugins\bower_components\moment\moment.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\check-overflow.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\date-from-array.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\from-anything.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\from-array.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\from-object.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\from-string-and-array.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\from-string-and-format.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\from-string.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\local.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\parsing-flags.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\utc.js" />
    <Content Include="plugins\bower_components\moment\src\lib\create\valid.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\abs.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\add-subtract.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\as.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\bubble.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\constructor.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\create.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\duration.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\get.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\humanize.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\iso-string.js" />
    <Content Include="plugins\bower_components\moment\src\lib\duration\prototype.js" />
    <Content Include="plugins\bower_components\moment\src\lib\format\format.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\calendar.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\constructor.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\en.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\formats.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\invalid.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\lists.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\locale.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\locales.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\ordinal.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\pre-post-format.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\prototype.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\relative.js" />
    <Content Include="plugins\bower_components\moment\src\lib\locale\set.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\add-subtract.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\calendar.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\clone.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\compare.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\constructor.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\diff.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\format.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\from.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\get-set.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\locale.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\min-max.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\moment.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\prototype.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\start-end-of.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\to-type.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\to.js" />
    <Content Include="plugins\bower_components\moment\src\lib\moment\valid.js" />
    <Content Include="plugins\bower_components\moment\src\lib\parse\regex.js" />
    <Content Include="plugins\bower_components\moment\src\lib\parse\token.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\aliases.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\constants.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\day-of-month.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\day-of-week.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\day-of-year.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\hour.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\millisecond.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\minute.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\month.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\offset.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\quarter.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\second.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\timestamp.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\timezone.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\units.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\week-year.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\week.js" />
    <Content Include="plugins\bower_components\moment\src\lib\units\year.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\abs-ceil.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\abs-floor.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\compare-arrays.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\defaults.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\deprecate.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\extend.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\has-own-prop.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\hooks.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\is-array.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\is-date.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\map.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\to-int.js" />
    <Content Include="plugins\bower_components\moment\src\lib\utils\zero-fill.js" />
    <Content Include="plugins\bower_components\moment\src\locale\af.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ar-ma.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ar-sa.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ar-tn.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ar.js" />
    <Content Include="plugins\bower_components\moment\src\locale\az.js" />
    <Content Include="plugins\bower_components\moment\src\locale\be.js" />
    <Content Include="plugins\bower_components\moment\src\locale\bg.js" />
    <Content Include="plugins\bower_components\moment\src\locale\bn.js" />
    <Content Include="plugins\bower_components\moment\src\locale\bo.js" />
    <Content Include="plugins\bower_components\moment\src\locale\br.js" />
    <Content Include="plugins\bower_components\moment\src\locale\bs.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ca.js" />
    <Content Include="plugins\bower_components\moment\src\locale\cs.js" />
    <Content Include="plugins\bower_components\moment\src\locale\cv.js" />
    <Content Include="plugins\bower_components\moment\src\locale\cy.js" />
    <Content Include="plugins\bower_components\moment\src\locale\da.js" />
    <Content Include="plugins\bower_components\moment\src\locale\de-at.js" />
    <Content Include="plugins\bower_components\moment\src\locale\de.js" />
    <Content Include="plugins\bower_components\moment\src\locale\el.js" />
    <Content Include="plugins\bower_components\moment\src\locale\en-au.js" />
    <Content Include="plugins\bower_components\moment\src\locale\en-ca.js" />
    <Content Include="plugins\bower_components\moment\src\locale\en-gb.js" />
    <Content Include="plugins\bower_components\moment\src\locale\eo.js" />
    <Content Include="plugins\bower_components\moment\src\locale\es.js" />
    <Content Include="plugins\bower_components\moment\src\locale\et.js" />
    <Content Include="plugins\bower_components\moment\src\locale\eu.js" />
    <Content Include="plugins\bower_components\moment\src\locale\fa.js" />
    <Content Include="plugins\bower_components\moment\src\locale\fi.js" />
    <Content Include="plugins\bower_components\moment\src\locale\fo.js" />
    <Content Include="plugins\bower_components\moment\src\locale\fr-ca.js" />
    <Content Include="plugins\bower_components\moment\src\locale\fr.js" />
    <Content Include="plugins\bower_components\moment\src\locale\fy.js" />
    <Content Include="plugins\bower_components\moment\src\locale\gl.js" />
    <Content Include="plugins\bower_components\moment\src\locale\he.js" />
    <Content Include="plugins\bower_components\moment\src\locale\hi.js" />
    <Content Include="plugins\bower_components\moment\src\locale\hr.js" />
    <Content Include="plugins\bower_components\moment\src\locale\hu.js" />
    <Content Include="plugins\bower_components\moment\src\locale\hy-am.js" />
    <Content Include="plugins\bower_components\moment\src\locale\id.js" />
    <Content Include="plugins\bower_components\moment\src\locale\is.js" />
    <Content Include="plugins\bower_components\moment\src\locale\it.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ja.js" />
    <Content Include="plugins\bower_components\moment\src\locale\jv.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ka.js" />
    <Content Include="plugins\bower_components\moment\src\locale\km.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ko.js" />
    <Content Include="plugins\bower_components\moment\src\locale\lb.js" />
    <Content Include="plugins\bower_components\moment\src\locale\lt.js" />
    <Content Include="plugins\bower_components\moment\src\locale\lv.js" />
    <Content Include="plugins\bower_components\moment\src\locale\me.js" />
    <Content Include="plugins\bower_components\moment\src\locale\mk.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ml.js" />
    <Content Include="plugins\bower_components\moment\src\locale\mr.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ms-my.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ms.js" />
    <Content Include="plugins\bower_components\moment\src\locale\my.js" />
    <Content Include="plugins\bower_components\moment\src\locale\nb.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ne.js" />
    <Content Include="plugins\bower_components\moment\src\locale\nl.js" />
    <Content Include="plugins\bower_components\moment\src\locale\nn.js" />
    <Content Include="plugins\bower_components\moment\src\locale\pl.js" />
    <Content Include="plugins\bower_components\moment\src\locale\pt-br.js" />
    <Content Include="plugins\bower_components\moment\src\locale\pt.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ro.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ru.js" />
    <Content Include="plugins\bower_components\moment\src\locale\si.js" />
    <Content Include="plugins\bower_components\moment\src\locale\sk.js" />
    <Content Include="plugins\bower_components\moment\src\locale\sl.js" />
    <Content Include="plugins\bower_components\moment\src\locale\sq.js" />
    <Content Include="plugins\bower_components\moment\src\locale\sr-cyrl.js" />
    <Content Include="plugins\bower_components\moment\src\locale\sr.js" />
    <Content Include="plugins\bower_components\moment\src\locale\sv.js" />
    <Content Include="plugins\bower_components\moment\src\locale\ta.js" />
    <Content Include="plugins\bower_components\moment\src\locale\th.js" />
    <Content Include="plugins\bower_components\moment\src\locale\tl-ph.js" />
    <Content Include="plugins\bower_components\moment\src\locale\tr.js" />
    <Content Include="plugins\bower_components\moment\src\locale\tzl.js" />
    <Content Include="plugins\bower_components\moment\src\locale\tzm-latn.js" />
    <Content Include="plugins\bower_components\moment\src\locale\tzm.js" />
    <Content Include="plugins\bower_components\moment\src\locale\uk.js" />
    <Content Include="plugins\bower_components\moment\src\locale\uz.js" />
    <Content Include="plugins\bower_components\moment\src\locale\vi.js" />
    <Content Include="plugins\bower_components\moment\src\locale\zh-cn.js" />
    <Content Include="plugins\bower_components\moment\src\locale\zh-tw.js" />
    <Content Include="plugins\bower_components\moment\src\moment.js" />
    <Content Include="plugins\bower_components\morrisjs\.gitignore" />
    <Content Include="plugins\bower_components\morrisjs\examples\area-as-line.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\area.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\bar-colors.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\bar-no-axes.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\bar.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\days.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\decimal-custom-hover.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\diagonal-xlabels-bar.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\diagonal-xlabels.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\donut-colors.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\donut-formatter.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\donut.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\dst.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\events.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\goals.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\lib\example.css" />
    <Content Include="plugins\bower_components\morrisjs\examples\lib\example.js" />
    <Content Include="plugins\bower_components\morrisjs\examples\months-no-smooth.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\negative.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\no-grid.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\non-continuous.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\non-date.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\quarters.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\resize.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\stacked_bars.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\timestamps.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\updating.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\weeks.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\years.html" />
    <Content Include="plugins\bower_components\morrisjs\examples\_template.html" />
    <Content Include="plugins\bower_components\morrisjs\Gruntfile.js" />
    <Content Include="plugins\bower_components\morrisjs\morris.css" />
    <Content Include="plugins\bower_components\morrisjs\morris.js" />
    <Content Include="plugins\bower_components\morrisjs\morris.min.js" />
    <Content Include="plugins\bower_components\morrisjs\spec\specs.html" />
    <Content Include="plugins\bower_components\morrisjs\spec\viz\examples.js" />
    <Content Include="plugins\bower_components\morrisjs\spec\viz\exemplary\area0.png" />
    <Content Include="plugins\bower_components\morrisjs\spec\viz\exemplary\bar0.png" />
    <Content Include="plugins\bower_components\morrisjs\spec\viz\exemplary\line0.png" />
    <Content Include="plugins\bower_components\morrisjs\spec\viz\exemplary\stacked_bar0.png" />
    <Content Include="plugins\bower_components\morrisjs\spec\viz\test.html" />
    <Content Include="plugins\bower_components\morrisjs\spec\viz\visual_specs.js" />
    <Content Include="plugins\bower_components\multiselect\css\multi-select.css" />
    <Content Include="plugins\bower_components\multiselect\img\switch.png" />
    <Content Include="plugins\bower_components\multiselect\js\jquery.multi-select.js" />
    <Content Include="plugins\bower_components\nestable\jquery.nestable.js" />
    <Content Include="plugins\bower_components\nestable\nestable.css" />
    <Content Include="plugins\bower_components\owl.carousel\owl.carousel.js" />
    <Content Include="plugins\bower_components\owl.carousel\owl.carousel.min.css" />
    <Content Include="plugins\bower_components\owl.carousel\owl.carousel.min.js" />
    <Content Include="plugins\bower_components\owl.carousel\owl.custom.js" />
    <Content Include="plugins\bower_components\owl.carousel\owl.theme.default.css" />
    <Content Include="plugins\bower_components\peity\jquery.peity.init.js" />
    <Content Include="plugins\bower_components\peity\jquery.peity.min.js" />
    <Content Include="plugins\bower_components\raphael\dev\amdDev.js" />
    <Content Include="plugins\bower_components\raphael\dev\index.js" />
    <Content Include="plugins\bower_components\raphael\dev\require.js" />
    <Content Include="plugins\bower_components\raphael\license.txt" />
    <Content Include="plugins\bower_components\raphael\raphael-min.js" />
    <Content Include="plugins\bower_components\raphael\raphael.js" />
    <Content Include="plugins\bower_components\register-steps\jquery.easing.min.js" />
    <Content Include="plugins\bower_components\register-steps\register-init.js" />
    <Content Include="plugins\bower_components\register-steps\steps.css" />
    <Content Include="plugins\bower_components\sidebar-nav\dist\sidebar-nav.css" />
    <Content Include="plugins\bower_components\sidebar-nav\dist\sidebar-nav.js" />
    <Content Include="plugins\bower_components\sidebar-nav\dist\sidebar-nav.min.css" />
    <Content Include="plugins\bower_components\sidebar-nav\dist\sidebar-nav.min.js" />
    <Content Include="plugins\bower_components\sidebar-nav\Gruntfile.js" />
    <Content Include="plugins\bower_components\sidebar-nav\src\metisMenu.css" />
    <Content Include="plugins\bower_components\sidebar-nav\src\metisMenu.js" />
    <Content Include="plugins\bower_components\skycons\skycons.js" />
    <Content Include="plugins\bower_components\styleswitcher\jQuery.style.switcher.js" />
    <Content Include="plugins\bower_components\summernote\dist\summernote-bs3.css" />
    <Content Include="plugins\bower_components\summernote\dist\summernote.css" />
    <Content Include="plugins\bower_components\summernote\dist\summernote.js" />
    <Content Include="plugins\bower_components\summernote\dist\summernote.min.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-ar-AR.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-bg-BG.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-ca-ES.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-cs-CZ.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-da-DK.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-de-DE.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-es-ES.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-es-EU.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-fa-IR.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-fi-FI.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-fr-FR.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-he-IL.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-hu-HU.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-id-ID.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-it-IT.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-ja-JP.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-ko-KR.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-lt-LT.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-nb-NO.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-nl-NL.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-pl-PL.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-pt-BR.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-pt-PT.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-ro-RO.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-ru-RU.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-sk-SK.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-sl-SI.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-sr-RS-Latin.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-sr-RS.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-sv-SE.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-th-TH.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-tr-TR.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-uk-UA.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-vi-VN.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-zh-CN.js" />
    <Content Include="plugins\bower_components\summernote\lang\summernote-zh-TW.js" />
    <Content Include="plugins\bower_components\summernote\meteor\package-standalone.js" />
    <Content Include="plugins\bower_components\summernote\meteor\package.js" />
    <Content Include="plugins\bower_components\summernote\meteor\test.js" />
    <Content Include="plugins\bower_components\summernote\plugin\summernote-ext-hello.js" />
    <Content Include="plugins\bower_components\summernote\plugin\summernote-ext-hint.js" />
    <Content Include="plugins\bower_components\summernote\plugin\summernote-ext-video.js" />
    <Content Include="plugins\bower_components\summernote\src\css\elements.css" />
    <Content Include="plugins\bower_components\summernote\src\css\summernote.css" />
    <Content Include="plugins\bower_components\summernote\src\js\app.js" />
    <Content Include="plugins\bower_components\summernote\src\js\core\agent.js" />
    <Content Include="plugins\bower_components\summernote\src\js\core\async.js" />
    <Content Include="plugins\bower_components\summernote\src\js\core\dom.js" />
    <Content Include="plugins\bower_components\summernote\src\js\core\func.js" />
    <Content Include="plugins\bower_components\summernote\src\js\core\key.js" />
    <Content Include="plugins\bower_components\summernote\src\js\core\list.js" />
    <Content Include="plugins\bower_components\summernote\src\js\core\range.js" />
    <Content Include="plugins\bower_components\summernote\src\js\defaults.js" />
    <Content Include="plugins\bower_components\summernote\src\js\editing\Bullet.js" />
    <Content Include="plugins\bower_components\summernote\src\js\editing\History.js" />
    <Content Include="plugins\bower_components\summernote\src\js\editing\Style.js" />
    <Content Include="plugins\bower_components\summernote\src\js\editing\Table.js" />
    <Content Include="plugins\bower_components\summernote\src\js\editing\Typing.js" />
    <Content Include="plugins\bower_components\summernote\src\js\EventHandler.js" />
    <Content Include="plugins\bower_components\summernote\src\js\intro.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\Button.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\Clipboard.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\Codeview.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\DragAndDrop.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\Editor.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\Fullscreen.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\Handle.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\HelpDialog.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\ImageDialog.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\LinkDialog.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\Popover.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\Statusbar.js" />
    <Content Include="plugins\bower_components\summernote\src\js\module\Toolbar.js" />
    <Content Include="plugins\bower_components\summernote\src\js\outro.js" />
    <Content Include="plugins\bower_components\summernote\src\js\Renderer.js" />
    <Content Include="plugins\bower_components\summernote\src\js\summernote.js" />
    <Content Include="plugins\bower_components\sweetalert\jquery.sweet-alert.custom.js" />
    <Content Include="plugins\bower_components\sweetalert\sweetalert-dev.js" />
    <Content Include="plugins\bower_components\sweetalert\sweetalert.css" />
    <Content Include="plugins\bower_components\sweetalert\sweetalert.min.js" />
    <Content Include="plugins\bower_components\switchery\dist\switchery.css" />
    <Content Include="plugins\bower_components\switchery\dist\switchery.js" />
    <Content Include="plugins\bower_components\switchery\dist\switchery.min.css" />
    <Content Include="plugins\bower_components\switchery\dist\switchery.min.js" />
    <Content Include="plugins\bower_components\switchery\meteor\export.js" />
    <Content Include="plugins\bower_components\switchery\meteor\tests.js" />
    <Content Include="plugins\bower_components\switchery\switchery.css" />
    <Content Include="plugins\bower_components\switchery\switchery.js" />
    <Content Include="plugins\bower_components\tablesaw-master\.gitignore" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\ajax-init-stack.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\ajax-init.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\bare.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\caption.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\demo.css" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\109.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\110.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\ajax-init-tbody.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\charset-test-issue-106.php" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\hideempty.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\issue-31.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\issue-32.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\issue-35.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\issue-37.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\issue-48-49.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\issue-65.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\issues\issue-74.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\kitchensink-nomodeswitch.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\kitchensink.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\modeswitch.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\sort-custom.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\sort.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\stack.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\stackonly.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\swipe-config.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\swipe-container.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\swipe-fixcols-media.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\swipe.html" />
    <Content Include="plugins\bower_components\tablesaw-master\demo\toggle.html" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\bare\tablesaw.bare.css" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\dependencies\jquery.js" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\dependencies\qunit.css" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\dependencies\qunit.js" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\dependencies\respond.js" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\stackonly\tablesaw.stackonly.css" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\stackonly\tablesaw.stackonly.js" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\tablesaw-init.js" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\tablesaw.css" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\tablesaw.js" />
    <Content Include="plugins\bower_components\tablesaw-master\docs\columntoggle-minimap.gif" />
    <Content Include="plugins\bower_components\tablesaw-master\docs\mode-switch.gif" />
    <Content Include="plugins\bower_components\tablesaw-master\docs\sortable.png" />
    <Content Include="plugins\bower_components\tablesaw-master\docs\stack.gif" />
    <Content Include="plugins\bower_components\tablesaw-master\docs\swipe-minimap.gif" />
    <Content Include="plugins\bower_components\tablesaw-master\Gruntfile.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables-init.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.btnmarkup.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.columntoggle.css" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.columntoggle.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.css" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.minimap.css" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.minimap.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.modeswitch.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.skin.css" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.sortable.css" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.sortable.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.stack-default-breakpoint.css" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.stack.css" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.stack.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.swipetoggle.css" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.swipetoggle.js" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.toolbar.css" />
    <Content Include="plugins\bower_components\tablesaw-master\test\tablesaw.html" />
    <Content Include="plugins\bower_components\tablesaw-master\test\tablesaw_bare.html" />
    <Content Include="plugins\bower_components\tablesaw-master\test\tablesaw_test.js" />
    <Content Include="plugins\bower_components\timepicker\bootstrap-timepicker.min.css" />
    <Content Include="plugins\bower_components\timepicker\bootstrap-timepicker.min.js" />
    <Content Include="plugins\bower_components\tiny-editable\mindmup-editabletable.js" />
    <Content Include="plugins\bower_components\tiny-editable\numeric-input-example.js" />
    <Content Include="plugins\bower_components\tinymce\jquery.tinymce.min.js" />
    <Content Include="plugins\bower_components\tinymce\license.txt" />
    <Content Include="plugins\bower_components\tinymce\plugins\advlist\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\anchor\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\autolink\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\autoresize\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\autosave\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\bbcode\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\charmap\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\code\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\colorpicker\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\contextmenu\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\directionality\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-cool.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-cry.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-embarassed.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-foot-in-mouth.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-frown.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-innocent.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-kiss.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-laughing.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-money-mouth.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-sealed.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-smile.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-surprised.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-tongue-out.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-undecided.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-wink.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\img\smiley-yell.gif" />
    <Content Include="plugins\bower_components\tinymce\plugins\emoticons\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\example\dialog.html" />
    <Content Include="plugins\bower_components\tinymce\plugins\example\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\example_dependency\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\fullpage\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\fullscreen\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\hr\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\imagetools\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\image\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\importcss\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\insertdatetime\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\layer\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\legacyoutput\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\link\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\lists\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\media\moxieplayer.swf" />
    <Content Include="plugins\bower_components\tinymce\plugins\media\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\nonbreaking\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\noneditable\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\pagebreak\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\paste\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\preview\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\print\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\save\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\searchreplace\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\spellchecker\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\tabfocus\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\table\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\template\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\textcolor\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\textpattern\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\visualblocks\css\visualblocks.css" />
    <Content Include="plugins\bower_components\tinymce\plugins\visualblocks\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\visualchars\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\plugins\wordcount\plugin.min.js" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\content.inline.min.css" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\content.min.css" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\fonts\tinymce-small.svg" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\fonts\tinymce.svg" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\img\anchor.gif" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\img\loader.gif" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\img\object.gif" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\img\trans.gif" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\skin.ie7.min.css" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\skin.min.css" />
    <Content Include="plugins\bower_components\tinymce\themes\modern\theme.min.js" />
    <Content Include="plugins\bower_components\tinymce\tinymce.min.js" />
    <Content Include="plugins\bower_components\toast-master\css\jquery.toast.css" />
    <Content Include="plugins\bower_components\toast-master\js\jquery.toast.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\.gitignore" />
    <Content Include="plugins\bower_components\typeahead.js-master\dist\bloodhound.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\dist\bloodhound.min.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\dist\typeahead-init.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\dist\typeahead.bundle.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\dist\typeahead.bundle.min.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\dist\typeahead.jquery.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\dist\typeahead.jquery.min.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\dist\typehead-min.css" />
    <Content Include="plugins\bower_components\typeahead.js-master\Gruntfile.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\karma.conf.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\bloodhound.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\lru_cache.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\options_parser.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\persistent_storage.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\prefetch.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\remote.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\search_index.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\tokenizers.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\transport.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\bloodhound\version.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\common\utils.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\dataset.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\default_menu.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\event_bus.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\event_emitter.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\highlight.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\input.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\menu.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\plugin.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\typeahead.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\src\typeahead\www.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\bloodhound\bloodhound_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\bloodhound\lru_cache_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\bloodhound\options_parser_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\bloodhound\persistent_storage_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\bloodhound\prefetch_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\bloodhound\remote_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\bloodhound\search_index_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\bloodhound\tokenizers_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\bloodhound\transport_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\fixtures\ajax_responses.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\fixtures\data.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\fixtures\html.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\helpers\typeahead_mocks.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\integration\test.html" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\integration\test.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\playground.html" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\typeahead\dataset_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\typeahead\default_results_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\typeahead\event_bus_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\typeahead\event_emitter_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\typeahead\highlight_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\typeahead\input_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\typeahead\plugin_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\typeahead\results_spec.js" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\typeahead\typeahead_spec.js" />
    <Content Include="plugins\bower_components\typed.js-master\dist\typed.min.js" />
    <Content Include="plugins\bower_components\typed.js-master\js\typed.js" />
    <Content Include="plugins\bower_components\vectormap\gdp-data.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-2.0.2.css" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-2.0.2.min.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-asia-mill.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-au-mill.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-ca-lcc.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-de-mill.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-europe-mill-en.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-in-mill.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-uk-mill-en.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-us-aea-en.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-us-il-chicago-mill-en.js" />
    <Content Include="plugins\bower_components\vectormap\jquery-jvectormap-world-mill-en.js" />
    <Content Include="plugins\bower_components\vectormap\jvectormap.custom.js" />
    <Content Include="plugins\bower_components\waypoints\lib\jquery.waypoints.js" />
    <Content Include="plugins\bower_components\waypoints\lib\jquery.waypoints.min.js" />
    <Content Include="plugins\bower_components\waypoints\lib\noframework.waypoints.js" />
    <Content Include="plugins\bower_components\waypoints\lib\noframework.waypoints.min.js" />
    <Content Include="plugins\bower_components\waypoints\lib\shortcuts\infinite.js" />
    <Content Include="plugins\bower_components\waypoints\lib\shortcuts\infinite.min.js" />
    <Content Include="plugins\bower_components\waypoints\lib\shortcuts\inview.js" />
    <Content Include="plugins\bower_components\waypoints\lib\shortcuts\inview.min.js" />
    <Content Include="plugins\bower_components\waypoints\lib\shortcuts\sticky.js" />
    <Content Include="plugins\bower_components\waypoints\lib\shortcuts\sticky.min.js" />
    <Content Include="plugins\bower_components\waypoints\lib\waypoints.debug.js" />
    <Content Include="plugins\bower_components\waypoints\lib\zepto.waypoints.js" />
    <Content Include="plugins\bower_components\waypoints\lib\zepto.waypoints.min.js" />
    <Content Include="plugins\chartjs\Chart.js" />
    <Content Include="plugins\chartjs\Chart.min.js" />
    <Content Include="plugins\ckeditor\adapters\jquery.js" />
    <Content Include="plugins\ckeditor\build-config.js" />
    <Content Include="plugins\ckeditor\ckeditor.js" />
    <Content Include="plugins\ckeditor\config.js" />
    <Content Include="plugins\ckeditor\contents.css" />
    <Content Include="plugins\ckeditor\lang\af.js" />
    <Content Include="plugins\ckeditor\lang\ar.js" />
    <Content Include="plugins\ckeditor\lang\bg.js" />
    <Content Include="plugins\ckeditor\lang\bn.js" />
    <Content Include="plugins\ckeditor\lang\bs.js" />
    <Content Include="plugins\ckeditor\lang\ca.js" />
    <Content Include="plugins\ckeditor\lang\cs.js" />
    <Content Include="plugins\ckeditor\lang\cy.js" />
    <Content Include="plugins\ckeditor\lang\da.js" />
    <Content Include="plugins\ckeditor\lang\de.js" />
    <Content Include="plugins\ckeditor\lang\el.js" />
    <Content Include="plugins\ckeditor\lang\en-au.js" />
    <Content Include="plugins\ckeditor\lang\en-ca.js" />
    <Content Include="plugins\ckeditor\lang\en-gb.js" />
    <Content Include="plugins\ckeditor\lang\en.js" />
    <Content Include="plugins\ckeditor\lang\eo.js" />
    <Content Include="plugins\ckeditor\lang\es.js" />
    <Content Include="plugins\ckeditor\lang\et.js" />
    <Content Include="plugins\ckeditor\lang\eu.js" />
    <Content Include="plugins\ckeditor\lang\fa.js" />
    <Content Include="plugins\ckeditor\lang\fi.js" />
    <Content Include="plugins\ckeditor\lang\fo.js" />
    <Content Include="plugins\ckeditor\lang\fr-ca.js" />
    <Content Include="plugins\ckeditor\lang\fr.js" />
    <Content Include="plugins\ckeditor\lang\gl.js" />
    <Content Include="plugins\ckeditor\lang\gu.js" />
    <Content Include="plugins\ckeditor\lang\he.js" />
    <Content Include="plugins\ckeditor\lang\hi.js" />
    <Content Include="plugins\ckeditor\lang\hr.js" />
    <Content Include="plugins\ckeditor\lang\hu.js" />
    <Content Include="plugins\ckeditor\lang\id.js" />
    <Content Include="plugins\ckeditor\lang\is.js" />
    <Content Include="plugins\ckeditor\lang\it.js" />
    <Content Include="plugins\ckeditor\lang\ja.js" />
    <Content Include="plugins\ckeditor\lang\ka.js" />
    <Content Include="plugins\ckeditor\lang\km.js" />
    <Content Include="plugins\ckeditor\lang\ko.js" />
    <Content Include="plugins\ckeditor\lang\ku.js" />
    <Content Include="plugins\ckeditor\lang\lt.js" />
    <Content Include="plugins\ckeditor\lang\lv.js" />
    <Content Include="plugins\ckeditor\lang\mk.js" />
    <Content Include="plugins\ckeditor\lang\mn.js" />
    <Content Include="plugins\ckeditor\lang\ms.js" />
    <Content Include="plugins\ckeditor\lang\nb.js" />
    <Content Include="plugins\ckeditor\lang\nl.js" />
    <Content Include="plugins\ckeditor\lang\no.js" />
    <Content Include="plugins\ckeditor\lang\pl.js" />
    <Content Include="plugins\ckeditor\lang\pt-br.js" />
    <Content Include="plugins\ckeditor\lang\pt.js" />
    <Content Include="plugins\ckeditor\lang\ro.js" />
    <Content Include="plugins\ckeditor\lang\ru.js" />
    <Content Include="plugins\ckeditor\lang\si.js" />
    <Content Include="plugins\ckeditor\lang\sk.js" />
    <Content Include="plugins\ckeditor\lang\sl.js" />
    <Content Include="plugins\ckeditor\lang\sq.js" />
    <Content Include="plugins\ckeditor\lang\sr-latn.js" />
    <Content Include="plugins\ckeditor\lang\sr.js" />
    <Content Include="plugins\ckeditor\lang\sv.js" />
    <Content Include="plugins\ckeditor\lang\th.js" />
    <Content Include="plugins\ckeditor\lang\tr.js" />
    <Content Include="plugins\ckeditor\lang\ug.js" />
    <Content Include="plugins\ckeditor\lang\uk.js" />
    <Content Include="plugins\ckeditor\lang\vi.js" />
    <Content Include="plugins\ckeditor\lang\zh-cn.js" />
    <Content Include="plugins\ckeditor\lang\zh.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\a11yhelp.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\ar.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\bg.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\ca.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\cs.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\cy.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\da.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\de.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\el.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\en.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\eo.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\es.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\et.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\fa.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\fi.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\fr-ca.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\fr.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\gl.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\gu.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\he.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\hi.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\hr.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\hu.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\id.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\it.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\ja.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\km.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\ko.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\ku.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\lt.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\lv.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\mk.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\mn.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\nb.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\nl.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\no.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\pl.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\pt-br.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\pt.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\ro.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\ru.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\si.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\sk.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\sl.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\sq.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\sr-latn.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\sr.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\sv.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\th.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\tr.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\ug.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\uk.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\vi.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\zh-cn.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\zh.js" />
    <Content Include="plugins\ckeditor\plugins\a11yhelp\dialogs\lang\_translationstatus.txt" />
    <Content Include="plugins\ckeditor\plugins\about\dialogs\about.js" />
    <Content Include="plugins\ckeditor\plugins\about\dialogs\hidpi\logo_ckeditor.png" />
    <Content Include="plugins\ckeditor\plugins\about\dialogs\logo_ckeditor.png" />
    <Content Include="plugins\ckeditor\plugins\clipboard\dialogs\paste.js" />
    <Content Include="plugins\ckeditor\plugins\dialog\dialogDefinition.js" />
    <Content Include="plugins\ckeditor\plugins\fakeobjects\images\spacer.gif" />
    <Content Include="plugins\ckeditor\plugins\icons.png" />
    <Content Include="plugins\ckeditor\plugins\icons_hidpi.png" />
    <Content Include="plugins\ckeditor\plugins\image\dialogs\image.js" />
    <Content Include="plugins\ckeditor\plugins\image\images\noimage.png" />
    <Content Include="plugins\ckeditor\plugins\link\dialogs\anchor.js" />
    <Content Include="plugins\ckeditor\plugins\link\dialogs\link.js" />
    <Content Include="plugins\ckeditor\plugins\link\images\anchor.png" />
    <Content Include="plugins\ckeditor\plugins\link\images\hidpi\anchor.png" />
    <Content Include="plugins\ckeditor\plugins\magicline\images\hidpi\icon.png" />
    <Content Include="plugins\ckeditor\plugins\magicline\images\icon.png" />
    <Content Include="plugins\ckeditor\plugins\pastefromword\filter\default.js" />
    <Content Include="plugins\ckeditor\plugins\scayt\dialogs\options.js" />
    <Content Include="plugins\ckeditor\plugins\scayt\dialogs\toolbar.css" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\ar.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\bg.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\ca.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\cs.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\cy.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\de.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\el.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\en.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\eo.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\es.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\et.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\fa.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\fi.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\fr-ca.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\fr.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\gl.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\he.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\hr.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\hu.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\id.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\it.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\ja.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\km.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\ku.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\lv.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\nb.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\nl.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\no.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\pl.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\pt-br.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\pt.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\ru.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\si.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\sk.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\sl.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\sq.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\sv.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\th.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\tr.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\ug.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\uk.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\vi.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\zh-cn.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\zh.js" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\lang\_translationstatus.txt" />
    <Content Include="plugins\ckeditor\plugins\specialchar\dialogs\specialchar.js" />
    <Content Include="plugins\ckeditor\plugins\tabletools\dialogs\tableCell.js" />
    <Content Include="plugins\ckeditor\plugins\table\dialogs\table.js" />
    <Content Include="plugins\ckeditor\plugins\wsc\dialogs\ciframe.html" />
    <Content Include="plugins\ckeditor\plugins\wsc\dialogs\tmp.html" />
    <Content Include="plugins\ckeditor\plugins\wsc\dialogs\tmpFrameset.html" />
    <Content Include="plugins\ckeditor\plugins\wsc\dialogs\wsc.css" />
    <Content Include="plugins\ckeditor\plugins\wsc\dialogs\wsc.js" />
    <Content Include="plugins\ckeditor\plugins\wsc\dialogs\wsc_ie.js" />
    <Content Include="plugins\ckeditor\skins\moono\dialog.css" />
    <Content Include="plugins\ckeditor\skins\moono\dialog_ie.css" />
    <Content Include="plugins\ckeditor\skins\moono\dialog_ie7.css" />
    <Content Include="plugins\ckeditor\skins\moono\dialog_ie8.css" />
    <Content Include="plugins\ckeditor\skins\moono\dialog_iequirks.css" />
    <Content Include="plugins\ckeditor\skins\moono\dialog_opera.css" />
    <Content Include="plugins\ckeditor\skins\moono\editor.css" />
    <Content Include="plugins\ckeditor\skins\moono\editor_gecko.css" />
    <Content Include="plugins\ckeditor\skins\moono\editor_ie.css" />
    <Content Include="plugins\ckeditor\skins\moono\editor_ie7.css" />
    <Content Include="plugins\ckeditor\skins\moono\editor_ie8.css" />
    <Content Include="plugins\ckeditor\skins\moono\editor_iequirks.css" />
    <Content Include="plugins\ckeditor\skins\moono\icons.png" />
    <Content Include="plugins\ckeditor\skins\moono\icons_hidpi.png" />
    <Content Include="plugins\ckeditor\skins\moono\images\arrow.png" />
    <Content Include="plugins\ckeditor\skins\moono\images\close.png" />
    <Content Include="plugins\ckeditor\skins\moono\images\hidpi\close.png" />
    <Content Include="plugins\ckeditor\skins\moono\images\hidpi\lock-open.png" />
    <Content Include="plugins\ckeditor\skins\moono\images\hidpi\lock.png" />
    <Content Include="plugins\ckeditor\skins\moono\images\hidpi\refresh.png" />
    <Content Include="plugins\ckeditor\skins\moono\images\lock-open.png" />
    <Content Include="plugins\ckeditor\skins\moono\images\lock.png" />
    <Content Include="plugins\ckeditor\skins\moono\images\refresh.png" />
    <Content Include="plugins\ckeditor\styles.js" />
    <Content Include="plugins\datatables-buttons\js\buttons.colVis.js" />
    <Content Include="plugins\datatables-buttons\js\buttons.colVis.min.js" />
    <Content Include="plugins\datatables-buttons\js\buttons.flash.js" />
    <Content Include="plugins\datatables-buttons\js\buttons.flash.min.js" />
    <Content Include="plugins\datatables-buttons\js\buttons.html5.js" />
    <Content Include="plugins\datatables-buttons\js\buttons.html5.min.js" />
    <Content Include="plugins\datatables-buttons\js\buttons.print.js" />
    <Content Include="plugins\datatables-buttons\js\buttons.print.min.js" />
    <Content Include="plugins\datatables-buttons\js\dataTables.buttons.js" />
    <Content Include="plugins\datatables-buttons\js\dataTables.buttons.min.js" />
    <Content Include="plugins\datatables\css\dataTables.bootstrap.css" />
    <Content Include="plugins\datatables\css\dataTables.bootstrap.min.css" />
    <Content Include="plugins\datatables\css\dataTables.bootstrap4.css" />
    <Content Include="plugins\datatables\css\dataTables.bootstrap4.min.css" />
    <Content Include="plugins\datatables\css\dataTables.foundation.css" />
    <Content Include="plugins\datatables\css\dataTables.foundation.min.css" />
    <Content Include="plugins\datatables\css\dataTables.jqueryui.css" />
    <Content Include="plugins\datatables\css\dataTables.jqueryui.min.css" />
    <Content Include="plugins\datatables\css\dataTables.material.css" />
    <Content Include="plugins\datatables\css\dataTables.material.min.css" />
    <Content Include="plugins\datatables\css\dataTables.semanticui.css" />
    <Content Include="plugins\datatables\css\dataTables.semanticui.min.css" />
    <Content Include="plugins\datatables\css\dataTables.uikit.css" />
    <Content Include="plugins\datatables\css\dataTables.uikit.min.css" />
    <Content Include="plugins\datatables\css\jquery.dataTables.css" />
    <Content Include="plugins\datatables\css\jquery.dataTables.min.css" />
    <Content Include="plugins\datatables\css\jquery.dataTables_themeroller.css" />
    <Content Include="plugins\datatables\css\jquery.dataTables_themeroller.min.css" />
    <Content Include="plugins\datatables\dataTables.bootstrap4.min.css" />
    <Content Include="plugins\datatables\dataTables.bootstrap4.min.js" />
    <Content Include="plugins\datatables\dataTables.select.min.js" />
    <Content Include="plugins\datatables\js\dataTables.bootstrap.js" />
    <Content Include="plugins\datatables\js\dataTables.bootstrap.min.js" />
    <Content Include="plugins\datatables\js\dataTables.bootstrap4.js" />
    <Content Include="plugins\datatables\js\dataTables.bootstrap4.min.js" />
    <Content Include="plugins\datatables\js\dataTables.dataTables.js" />
    <Content Include="plugins\datatables\js\dataTables.dataTables.min.js" />
    <Content Include="plugins\datatables\js\dataTables.foundation.js" />
    <Content Include="plugins\datatables\js\dataTables.foundation.min.js" />
    <Content Include="plugins\datatables\js\dataTables.jqueryui.js" />
    <Content Include="plugins\datatables\js\dataTables.jqueryui.min.js" />
    <Content Include="plugins\datatables\js\dataTables.material.js" />
    <Content Include="plugins\datatables\js\dataTables.material.min.js" />
    <Content Include="plugins\datatables\js\dataTables.semanticui.js" />
    <Content Include="plugins\datatables\js\dataTables.semanticui.min.js" />
    <Content Include="plugins\datatables\js\dataTables.uikit.js" />
    <Content Include="plugins\datatables\js\dataTables.uikit.min.js" />
    <Content Include="plugins\datatables\js\jquery.dataTables.js" />
    <Content Include="plugins\datatables\js\jquery.dataTables.min.js" />
    <Content Include="plugins\datatables\license.txt" />
    <Content Include="plugins\datatables\PigShop\buttons.bootstrap.min.css" />
    <Content Include="plugins\datatables\PigShop\buttons.bootstrap.min.js" />
    <Content Include="plugins\datatables\PigShop\buttons.dataTables.min.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.bootstrap.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.bootstrap.min.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.bootstrap4.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.bootstrap4.min.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.foundation.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.foundation.min.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.jqueryui.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.jqueryui.min.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.semanticui.css" />
    <Content Include="plugins\datatables\PigShop\css\dataTables.semanticui.min.css" />
    <Content Include="plugins\datatables\PigShop\css\jquery.dataTables.css" />
    <Content Include="plugins\datatables\PigShop\css\jquery.dataTables.min.css" />
    <Content Include="plugins\datatables\PigShop\dataTables.buttons.min.js" />
    <Content Include="plugins\datatables\PigShop\datatables.css" />
    <Content Include="plugins\datatables\PigShop\datatables.js" />
    <Content Include="plugins\datatables\PigShop\datatables.min.css" />
    <Content Include="plugins\datatables\PigShop\datatables.min.js" />
    <Content Include="plugins\datatables\PigShop\images\sort_asc.png" />
    <Content Include="plugins\datatables\PigShop\images\sort_asc_disabled.png" />
    <Content Include="plugins\datatables\PigShop\images\sort_both.png" />
    <Content Include="plugins\datatables\PigShop\images\sort_desc.png" />
    <Content Include="plugins\datatables\PigShop\images\sort_desc_disabled.png" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.bootstrap.js" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.bootstrap.min.js" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.bootstrap4.js" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.bootstrap4.min.js" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.foundation.js" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.foundation.min.js" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.jqueryui.js" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.jqueryui.min.js" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.semanticui.js" />
    <Content Include="plugins\datatables\PigShop\js\dataTables.semanticui.min.js" />
    <Content Include="plugins\datatables\PigShop\js\jquery.dataTables.js" />
    <Content Include="plugins\datatables\PigShop\js\jquery.dataTables.min.js" />
    <Content Include="plugins\datatables\PigShop\moment-with-locales-2.22.2.min.js" />
    <Content Include="plugins\datatables\Bk\dataTables.bootstrap.css" />
    <Content Include="plugins\datatables\Bk\dataTables.bootstrap.js" />
    <Content Include="plugins\datatables\Bk\dataTables.bootstrap.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\css\dataTables.autoFill.css" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\css\dataTables.autoFill.min.css" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\examples\columns.html" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\examples\complete-callback.html" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\examples\fill-both.html" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\examples\fill-horizontal.html" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\examples\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\examples\scrolling.html" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\examples\simple.html" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\examples\step-callback.html" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\images\filler.png" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\js\dataTables.autoFill.js" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\js\dataTables.autoFill.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\AutoFill\Readme.txt" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\css\dataTables.colReorder.css" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\css\dataTables.colReorder.min.css" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\alt_insert.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\colvis.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\col_filter.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\fixedcolumns.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\fixedheader.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\jqueryui.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\new_init.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\predefined.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\realtime.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\reset.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\scrolling.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\server_side.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\simple.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\examples\state_save.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\images\insert.png" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\js\dataTables.colReorder.js" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\js\dataTables.colReorder.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\License.txt" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\css\dataTables.colVis.css" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\css\dataTables.colvis.jqueryui.css" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\css\dataTables.colVis.min.css" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\button_order.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\exclude_columns.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\group_columns.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\jqueryui.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\mouseover.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\new_init.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\restore.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\simple.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\text.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\title_callback.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\two_tables.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\examples\two_tables_identical.html" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\js\dataTables.colVis.js" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\js\dataTables.colVis.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\License.txt" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\css\dataTables.fixedColumns.css" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\css\dataTables.fixedColumns.min.css" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\bootstrap.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\colvis.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\col_filter.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\css_size.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\index_column.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\left_right_columns.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\right_column.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\rowspan.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\server-side-processing.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\simple.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\size_fixed.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\size_fluid.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\examples\two_columns.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\js\dataTables.fixedColumns.js" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\js\dataTables.fixedColumns.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\License.txt" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\css\dataTables.fixedHeader.css" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\css\dataTables.fixedHeader.min.css" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\examples\header_footer.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\examples\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\examples\simple.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\examples\top_left_right.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\examples\two_tables.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\examples\zIndexes.html" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\js\dataTables.fixedHeader.js" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\js\dataTables.fixedHeader.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\FixedHeader\Readme.txt" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\css\dataTables.keyTable.css" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\css\dataTables.keyTable.min.css" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\examples\events.html" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\examples\html.html" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\examples\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\examples\scrolling.html" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\examples\simple.html" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\js\dataTables.keyTable.js" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\js\dataTables.keyTable.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\KeyTable\Readme.txt" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\css\dataTables.responsive.css" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\child-rows\column-control.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\child-rows\custom-renderer.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\child-rows\disable-child-rows.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\child-rows\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\child-rows\right-column.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\child-rows\whole-row-control.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\display-control\auto.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\display-control\classes.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\display-control\complexHeader.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\display-control\fixedHeader.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\display-control\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\display-control\init-classes.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\initialisation\ajax.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\initialisation\className.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\initialisation\default.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\initialisation\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\initialisation\new.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\initialisation\option.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\styling\bootstrap.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\styling\compact.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\styling\foundation.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\styling\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\examples\styling\scrolling.html" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\js\dataTables.responsive.js" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\js\dataTables.responsive.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\License.txt" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\css\dataTables.scroller.css" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\css\dataTables.scroller.min.css" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\examples\api_scrolling.html" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\examples\data\2500.txt" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\examples\data\ssp.php" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\examples\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\examples\large_js_source.html" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\examples\server-side_processing.html" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\examples\simple.html" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\examples\state_saving.html" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\images\loading-background.png" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\js\dataTables.scroller.js" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\js\dataTables.scroller.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\Scroller\Readme.txt" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\css\dataTables.tableTools.css" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\css\dataTables.tableTools.min.css" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\ajax.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\alter_buttons.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\bootstrap.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\button_text.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\collection.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\defaults.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\index.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\jqueryui.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\multiple_tables.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\multi_instance.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\new_init.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\pdf_message.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\plug-in.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\select_column.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\select_multi.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\select_os.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\select_single.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\simple.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\examples\swf_path.html" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\collection.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\collection_hover.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\copy.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\copy_hover.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\csv.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\csv_hover.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\pdf.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\pdf_hover.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\print.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\print_hover.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\xls.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\xls_hover.png" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\js\dataTables.tableTools.js" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\js\dataTables.tableTools.min.js" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\swf\copy_csv_xls.swf" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\swf\copy_csv_xls_pdf.swf" />
    <Content Include="plugins\datatables\Bk\images\sort_asc.png" />
    <Content Include="plugins\datatables\Bk\images\sort_asc_disabled.png" />
    <Content Include="plugins\datatables\Bk\images\sort_both.png" />
    <Content Include="plugins\datatables\Bk\images\sort_desc.png" />
    <Content Include="plugins\datatables\Bk\images\sort_desc_disabled.png" />
    <Content Include="plugins\datatables\Bk\jquery.dataTables.css" />
    <Content Include="plugins\datatables\Bk\jquery.dataTables.js" />
    <Content Include="plugins\datatables\Bk\jquery.dataTables.min.css" />
    <Content Include="plugins\datatables\Bk\jquery.dataTables.min.js" />
    <Content Include="plugins\datatables\Bk\jquery.dataTables_themeroller.css" />
    <Content Include="plugins\datatables\buttons.colVis.min.js" />
    <Content Include="plugins\datatables\buttons.html5.min.js" />
    <Content Include="plugins\datatables\buttons.print.min.js" />
    <Content Include="plugins\datatables\dataTables.bootstrap.css" />
    <Content Include="plugins\datatables\dataTables.bootstrap.js" />
    <Content Include="plugins\datatables\dataTables.bootstrap.min.js" />
    <Content Include="plugins\datatables\extensions\AutoFill\css\dataTables.autoFill.css" />
    <Content Include="plugins\datatables\extensions\AutoFill\css\dataTables.autoFill.min.css" />
    <Content Include="plugins\datatables\extensions\AutoFill\examples\columns.html" />
    <Content Include="plugins\datatables\extensions\AutoFill\examples\complete-callback.html" />
    <Content Include="plugins\datatables\extensions\AutoFill\examples\fill-both.html" />
    <Content Include="plugins\datatables\extensions\AutoFill\examples\fill-horizontal.html" />
    <Content Include="plugins\datatables\extensions\AutoFill\examples\index.html" />
    <Content Include="plugins\datatables\extensions\AutoFill\examples\scrolling.html" />
    <Content Include="plugins\datatables\extensions\AutoFill\examples\simple.html" />
    <Content Include="plugins\datatables\extensions\AutoFill\examples\step-callback.html" />
    <Content Include="plugins\datatables\extensions\AutoFill\images\filler.png" />
    <Content Include="plugins\datatables\extensions\AutoFill\js\dataTables.autoFill.js" />
    <Content Include="plugins\datatables\extensions\AutoFill\js\dataTables.autoFill.min.js" />
    <Content Include="plugins\datatables\extensions\AutoFill\Readme.txt" />
    <Content Include="plugins\datatables\extensions\ColReorder\css\dataTables.colReorder.css" />
    <Content Include="plugins\datatables\extensions\ColReorder\css\dataTables.colReorder.min.css" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\alt_insert.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\colvis.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\col_filter.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\fixedcolumns.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\fixedheader.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\index.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\jqueryui.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\new_init.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\predefined.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\realtime.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\reset.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\scrolling.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\server_side.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\simple.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\examples\state_save.html" />
    <Content Include="plugins\datatables\extensions\ColReorder\images\insert.png" />
    <Content Include="plugins\datatables\extensions\ColReorder\js\dataTables.colReorder.js" />
    <Content Include="plugins\datatables\extensions\ColReorder\js\dataTables.colReorder.min.js" />
    <Content Include="plugins\datatables\extensions\ColReorder\License.txt" />
    <Content Include="plugins\datatables\extensions\ColVis\css\dataTables.colVis.css" />
    <Content Include="plugins\datatables\extensions\ColVis\css\dataTables.colvis.jqueryui.css" />
    <Content Include="plugins\datatables\extensions\ColVis\css\dataTables.colVis.min.css" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\button_order.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\exclude_columns.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\group_columns.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\index.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\jqueryui.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\mouseover.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\new_init.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\restore.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\simple.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\text.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\title_callback.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\two_tables.html" />
    <Content Include="plugins\datatables\extensions\ColVis\examples\two_tables_identical.html" />
    <Content Include="plugins\datatables\extensions\ColVis\js\dataTables.colVis.js" />
    <Content Include="plugins\datatables\extensions\ColVis\js\dataTables.colVis.min.js" />
    <Content Include="plugins\datatables\extensions\ColVis\License.txt" />
    <Content Include="plugins\datatables\extensions\FixedColumns\css\dataTables.fixedColumns.css" />
    <Content Include="plugins\datatables\extensions\FixedColumns\css\dataTables.fixedColumns.min.css" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\bootstrap.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\colvis.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\col_filter.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\css_size.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\index.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\index_column.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\left_right_columns.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\right_column.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\rowspan.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\server-side-processing.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\simple.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\size_fixed.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\size_fluid.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\examples\two_columns.html" />
    <Content Include="plugins\datatables\extensions\FixedColumns\js\dataTables.fixedColumns.js" />
    <Content Include="plugins\datatables\extensions\FixedColumns\js\dataTables.fixedColumns.min.js" />
    <Content Include="plugins\datatables\extensions\FixedColumns\License.txt" />
    <Content Include="plugins\datatables\extensions\FixedHeader\css\dataTables.fixedHeader.css" />
    <Content Include="plugins\datatables\extensions\FixedHeader\css\dataTables.fixedHeader.min.css" />
    <Content Include="plugins\datatables\extensions\FixedHeader\examples\header_footer.html" />
    <Content Include="plugins\datatables\extensions\FixedHeader\examples\index.html" />
    <Content Include="plugins\datatables\extensions\FixedHeader\examples\simple.html" />
    <Content Include="plugins\datatables\extensions\FixedHeader\examples\top_left_right.html" />
    <Content Include="plugins\datatables\extensions\FixedHeader\examples\two_tables.html" />
    <Content Include="plugins\datatables\extensions\FixedHeader\examples\zIndexes.html" />
    <Content Include="plugins\datatables\extensions\FixedHeader\js\dataTables.fixedHeader.js" />
    <Content Include="plugins\datatables\extensions\FixedHeader\js\dataTables.fixedHeader.min.js" />
    <Content Include="plugins\datatables\extensions\FixedHeader\Readme.txt" />
    <Content Include="plugins\datatables\extensions\KeyTable\css\dataTables.keyTable.css" />
    <Content Include="plugins\datatables\extensions\KeyTable\css\dataTables.keyTable.min.css" />
    <Content Include="plugins\datatables\extensions\KeyTable\examples\events.html" />
    <Content Include="plugins\datatables\extensions\KeyTable\examples\html.html" />
    <Content Include="plugins\datatables\extensions\KeyTable\examples\index.html" />
    <Content Include="plugins\datatables\extensions\KeyTable\examples\scrolling.html" />
    <Content Include="plugins\datatables\extensions\KeyTable\examples\simple.html" />
    <Content Include="plugins\datatables\extensions\KeyTable\js\dataTables.keyTable.js" />
    <Content Include="plugins\datatables\extensions\KeyTable\js\dataTables.keyTable.min.js" />
    <Content Include="plugins\datatables\extensions\KeyTable\Readme.txt" />
    <Content Include="plugins\datatables\extensions\Responsive\css\dataTables.responsive.css" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\child-rows\column-control.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\child-rows\custom-renderer.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\child-rows\disable-child-rows.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\child-rows\index.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\child-rows\right-column.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\child-rows\whole-row-control.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\display-control\auto.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\display-control\classes.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\display-control\complexHeader.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\display-control\fixedHeader.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\display-control\index.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\display-control\init-classes.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\index.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\initialisation\ajax.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\initialisation\className.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\initialisation\default.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\initialisation\index.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\initialisation\new.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\initialisation\option.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\styling\bootstrap.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\styling\compact.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\styling\foundation.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\styling\index.html" />
    <Content Include="plugins\datatables\extensions\Responsive\examples\styling\scrolling.html" />
    <Content Include="plugins\datatables\extensions\Responsive\js\dataTables.responsive.js" />
    <Content Include="plugins\datatables\extensions\Responsive\js\dataTables.responsive.min.js" />
    <Content Include="plugins\datatables\extensions\Responsive\License.txt" />
    <Content Include="plugins\datatables\extensions\Scroller\css\dataTables.scroller.css" />
    <Content Include="plugins\datatables\extensions\Scroller\css\dataTables.scroller.min.css" />
    <Content Include="plugins\datatables\extensions\Scroller\examples\api_scrolling.html" />
    <Content Include="plugins\datatables\extensions\Scroller\examples\data\2500.txt" />
    <Content Include="plugins\datatables\extensions\Scroller\examples\data\ssp.php" />
    <Content Include="plugins\datatables\extensions\Scroller\examples\index.html" />
    <Content Include="plugins\datatables\extensions\Scroller\examples\large_js_source.html" />
    <Content Include="plugins\datatables\extensions\Scroller\examples\server-side_processing.html" />
    <Content Include="plugins\datatables\extensions\Scroller\examples\simple.html" />
    <Content Include="plugins\datatables\extensions\Scroller\examples\state_saving.html" />
    <Content Include="plugins\datatables\extensions\Scroller\images\loading-background.png" />
    <Content Include="plugins\datatables\extensions\Scroller\js\dataTables.scroller.js" />
    <Content Include="plugins\datatables\extensions\Scroller\js\dataTables.scroller.min.js" />
    <Content Include="plugins\datatables\extensions\Scroller\Readme.txt" />
    <Content Include="plugins\datatables\extensions\TableTools\css\dataTables.tableTools.css" />
    <Content Include="plugins\datatables\extensions\TableTools\css\dataTables.tableTools.min.css" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\ajax.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\alter_buttons.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\bootstrap.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\button_text.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\collection.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\defaults.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\index.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\jqueryui.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\multiple_tables.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\multi_instance.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\new_init.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\pdf_message.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\plug-in.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\select_column.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\select_multi.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\select_os.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\select_single.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\simple.html" />
    <Content Include="plugins\datatables\extensions\TableTools\examples\swf_path.html" />
    <Content Include="plugins\datatables\extensions\TableTools\images\collection.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\collection_hover.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\copy.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\copy_hover.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\csv.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\csv_hover.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\pdf.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\pdf_hover.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\print.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\print_hover.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\xls.png" />
    <Content Include="plugins\datatables\extensions\TableTools\images\xls_hover.png" />
    <Content Include="plugins\datatables\extensions\TableTools\js\dataTables.tableTools.js" />
    <Content Include="plugins\datatables\extensions\TableTools\js\dataTables.tableTools.min.js" />
    <Content Include="plugins\datatables\extensions\TableTools\swf\copy_csv_xls.swf" />
    <Content Include="plugins\datatables\extensions\TableTools\swf\copy_csv_xls_pdf.swf" />
    <Content Include="plugins\datatables\images\sort_asc.png" />
    <Content Include="plugins\datatables\images\sort_asc_disabled.png" />
    <Content Include="plugins\datatables\images\sort_both.png" />
    <Content Include="plugins\datatables\images\sort_desc.png" />
    <Content Include="plugins\datatables\images\sort_desc_disabled.png" />
    <Content Include="plugins\datatables\jquery.dataTables.css" />
    <Content Include="plugins\datatables\jquery.dataTables.js" />
    <Content Include="plugins\datatables\jquery.dataTables.min.css" />
    <Content Include="plugins\datatables\jquery.dataTables.min.js" />
    <Content Include="plugins\datatables\jquery.dataTables_themeroller.css" />
    <Content Include="plugins\datatables\jszip.min.js" />
    <Content Include="plugins\datatables\pdfmake.min.js" />
    <Content Include="plugins\datatables\select.bootstrap.min.css" />
    <Content Include="plugins\datatables\sort_asc.png" />
    <Content Include="plugins\datatables\sort_asc_disabled.png" />
    <Content Include="plugins\datatables\sort_both.png" />
    <Content Include="plugins\datatables\sort_desc.png" />
    <Content Include="plugins\datatables\sort_desc_disabled.png" />
    <Content Include="plugins\datatables\vfs_fonts.js" />
    <Content Include="plugins\datepicker\bootstrap-datepicker.js" />
    <Content Include="plugins\datepicker\datepicker3.css" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.ar.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.az.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.bg.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.ca.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.cs.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.cy.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.da.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.de.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.el.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.es.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.et.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.fa.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.fi.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.fr.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.gl.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.he.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.hr.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.hu.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.id.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.is.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.it.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.ja.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.ka.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.kk.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.kr.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.lt.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.lv.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.mk.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.ms.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.nb.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.nl-BE.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.nl.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.no.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.pl.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.pt-BR.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.pt.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.ro.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.rs-latin.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.rs.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.ru.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.sk.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.sl.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.sq.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.sv.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.sw.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.th.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.tr.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.ua.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.vi.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.zh-CN.js" />
    <Content Include="plugins\datepicker\locales\bootstrap-datepicker.zh-TW.js" />
    <Content Include="plugins\fastclick\fastclick.js" />
    <Content Include="plugins\fastclick\fastclick.min.js" />
    <Content Include="plugins\images\admin-logo-dark.png" />
    <Content Include="plugins\images\admin-logo.png" />
    <Content Include="plugins\images\admin-text-dark.png" />
    <Content Include="plugins\images\admin-text.png" />
    <Content Include="plugins\images\admin-text_new.png" />
    <Content Include="plugins\images\alert.png" />
    <Content Include="plugins\images\alert2.png" />
    <Content Include="plugins\images\alert3.png" />
    <Content Include="plugins\images\alert4.png" />
    <Content Include="plugins\images\alert5.png" />
    <Content Include="plugins\images\alert6.png" />
    <Content Include="plugins\images\alert7.png" />
    <Content Include="plugins\images\assets\landscape1.jpg" />
    <Content Include="plugins\images\assets\landscape10.jpg" />
    <Content Include="plugins\images\assets\landscape11.jpg" />
    <Content Include="plugins\images\assets\landscape12.jpg" />
    <Content Include="plugins\images\assets\landscape13.jpg" />
    <Content Include="plugins\images\assets\landscape14.jpg" />
    <Content Include="plugins\images\assets\landscape15.jpg" />
    <Content Include="plugins\images\assets\landscape16.jpg" />
    <Content Include="plugins\images\assets\landscape17.jpg" />
    <Content Include="plugins\images\assets\landscape18.jpg" />
    <Content Include="plugins\images\assets\landscape2.jpg" />
    <Content Include="plugins\images\assets\landscape3.jpg" />
    <Content Include="plugins\images\assets\landscape4.jpg" />
    <Content Include="plugins\images\assets\landscape5.jpg" />
    <Content Include="plugins\images\assets\landscape6.jpg" />
    <Content Include="plugins\images\assets\landscape7.jpg" />
    <Content Include="plugins\images\assets\landscape8.jpg" />
    <Content Include="plugins\images\assets\landscape9.jpg" />
    <Content Include="plugins\images\assets\studio1.jpg" />
    <Content Include="plugins\images\assets\studio10.jpg" />
    <Content Include="plugins\images\assets\studio11.jpg" />
    <Content Include="plugins\images\assets\studio12.jpg" />
    <Content Include="plugins\images\assets\studio13.jpg" />
    <Content Include="plugins\images\assets\studio14.jpg" />
    <Content Include="plugins\images\assets\studio15.jpg" />
    <Content Include="plugins\images\assets\studio16.jpg" />
    <Content Include="plugins\images\assets\studio17.jpg" />
    <Content Include="plugins\images\assets\studio18.jpg" />
    <Content Include="plugins\images\assets\studio19.jpg" />
    <Content Include="plugins\images\assets\studio2.jpg" />
    <Content Include="plugins\images\assets\studio20.jpg" />
    <Content Include="plugins\images\assets\studio21.jpg" />
    <Content Include="plugins\images\assets\studio22.jpg" />
    <Content Include="plugins\images\assets\studio23.jpg" />
    <Content Include="plugins\images\assets\studio24.jpg" />
    <Content Include="plugins\images\assets\studio25.jpg" />
    <Content Include="plugins\images\assets\studio26.jpg" />
    <Content Include="plugins\images\assets\studio27.jpg" />
    <Content Include="plugins\images\assets\studio3.jpg" />
    <Content Include="plugins\images\assets\studio4.jpg" />
    <Content Include="plugins\images\assets\studio5.jpg" />
    <Content Include="plugins\images\assets\studio6.jpg" />
    <Content Include="plugins\images\assets\studio7.jpg" />
    <Content Include="plugins\images\assets\studio8.jpg" />
    <Content Include="plugins\images\assets\studio9.jpg" />
    <Content Include="plugins\images\big\c1.jpg" />
    <Content Include="plugins\images\big\c2.jpg" />
    <Content Include="plugins\images\big\c3.jpg" />
    <Content Include="plugins\images\big\c4.jpg" />
    <Content Include="plugins\images\big\circle.jpg" />
    <Content Include="plugins\images\big\d2.jpg" />
    <Content Include="plugins\images\big\img1.jpg" />
    <Content Include="plugins\images\big\img2.jpg" />
    <Content Include="plugins\images\big\img3.jpg" />
    <Content Include="plugins\images\big\img4.jpg" />
    <Content Include="plugins\images\big\img5.jpg" />
    <Content Include="plugins\images\big\img6.jpg" />
    <Content Include="plugins\images\busy.gif" />
    <Content Include="plugins\images\chair.jpg" />
    <Content Include="plugins\images\chair2.jpg" />
    <Content Include="plugins\images\chair3.jpg" />
    <Content Include="plugins\images\chair4.jpg" />
    <Content Include="plugins\images\confirmed_icon.png" />
    <Content Include="plugins\images\debitoitau.gif" />
    <Content Include="plugins\images\debitoitau1.png" />
    <Content Include="plugins\images\denied_icon.png" />
    <Content Include="plugins\images\docs\horizontal-menu.png" />
    <Content Include="plugins\images\docs\resizemenu.jpg" />
    <Content Include="plugins\images\docs\sidebar.jpeg" />
    <Content Include="plugins\images\docs\sidebar.jpg" />
    <Content Include="plugins\images\docs\spinner.jpg" />
    <Content Include="plugins\images\docs\starter-kit.jpg" />
    <Content Include="plugins\images\docs\template-structure.jpg" />
    <Content Include="plugins\images\error-bg.jpg" />
    <Content Include="plugins\images\expired_icon.png" />
    <Content Include="plugins\images\favicon.png" />
    <Content Include="plugins\images\features-bg.jpg" />
    <Content Include="plugins\images\heading-bg\banner-1.jpg" />
    <Content Include="plugins\images\heading-bg\slide1.jpg" />
    <Content Include="plugins\images\heading-bg\slide2.jpg" />
    <Content Include="plugins\images\heading-bg\slide3.jpg" />
    <Content Include="plugins\images\heading-bg\slide4.jpg" />
    <Content Include="plugins\images\heading-bg\slide6.jpg" />
    <Content Include="plugins\images\heading-title-bg.jpg" />
    <Content Include="plugins\images\headline.png" />
    <Content Include="plugins\images\icone_american_express.jpg" />
    <Content Include="plugins\images\icone_aura.jpg" />
    <Content Include="plugins\images\icone_diners_club_internacional.jpg" />
    <Content Include="plugins\images\icone_discover.jpg" />
    <Content Include="plugins\images\icone_elo.jpg" />
    <Content Include="plugins\images\icone_jcb.jpg" />
    <Content Include="plugins\images\icone_master_card.jpg" />
    <Content Include="plugins\images\icone_visa.jpg" />
    <Content Include="plugins\images\imac.png" />
    <Content Include="plugins\images\img1.jpg" />
    <Content Include="plugins\images\img2.jpg" />
    <Content Include="plugins\images\img3.jpg" />
    <Content Include="plugins\images\img4.jpg" />
    <Content Include="plugins\images\large\1.jpg" />
    <Content Include="plugins\images\large\3.jpg" />
    <Content Include="plugins\images\large\5.jpg" />
    <Content Include="plugins\images\large\6.jpg" />
    <Content Include="plugins\images\large\7.jpg" />
    <Content Include="plugins\images\large\img1.jpg" />
    <Content Include="plugins\images\large\img2.jpg" />
    <Content Include="plugins\images\large\img3.jpg" />
    <Content Include="plugins\images\large\img4.jpg" />
    <Content Include="plugins\images\large\img5.jpg" />
    <Content Include="plugins\images\large\img6.jpg" />
    <Content Include="plugins\images\login-register.jpg" />
    <Content Include="plugins\images\login-register.png" />
    <Content Include="plugins\images\login-registerBK.jpg" />
    <Content Include="plugins\images\login-register_bk.jpg" />
    <Content Include="plugins\images\logo-itau.png" />
    <Content Include="plugins\images\logo.png" />
    <Content Include="plugins\images\model.png" />
    <Content Include="plugins\images\model2.png" />
    <Content Include="plugins\images\model3.png" />
    <Content Include="plugins\images\more.jpg" />
    <Content Include="plugins\images\multiple-arrow.png" />
    <Content Include="plugins\images\music\alb1.jpg" />
    <Content Include="plugins\images\music\alb2.jpg" />
    <Content Include="plugins\images\music\alb3.jpg" />
    <Content Include="plugins\images\music\alb4.jpg" />
    <Content Include="plugins\images\music\alb5.jpg" />
    <Content Include="plugins\images\music\alb6.jpg" />
    <Content Include="plugins\images\music\alb7.jpg" />
    <Content Include="plugins\images\music\alb8.jpg" />
    <Content Include="plugins\images\music\alb9.jpg" />
    <Content Include="plugins\images\music\vd1.jpg" />
    <Content Include="plugins\images\music\vd10.jpg" />
    <Content Include="plugins\images\music\vd11.jpg" />
    <Content Include="plugins\images\music\vd12.jpg" />
    <Content Include="plugins\images\music\vd13.jpg" />
    <Content Include="plugins\images\music\vd14.jpg" />
    <Content Include="plugins\images\music\vd2.jpg" />
    <Content Include="plugins\images\music\vd3.jpg" />
    <Content Include="plugins\images\music\vd4.jpg" />
    <Content Include="plugins\images\music\vd5.jpg" />
    <Content Include="plugins\images\music\vd6.jpg" />
    <Content Include="plugins\images\music\vd7.jpg" />
    <Content Include="plugins\images\music\vd8.jpg" />
    <Content Include="plugins\images\music\vd9.jpg" />
    <Content Include="plugins\images\news\gradiant-bg.png" />
    <Content Include="plugins\images\news\slide1.jpg" />
    <Content Include="plugins\images\news\slide6.jpg" />
    <Content Include="plugins\images\pigpag-logo-branca.png" />
    <Content Include="plugins\images\pigpag-logo-clara.png" />
    <Content Include="plugins\images\pigpag-logo-vertical.png" />
    <Content Include="plugins\images\profile-menu.png" />
    <Content Include="plugins\images\property\heart.png" />
    <Content Include="plugins\images\property\pro-bath.png" />
    <Content Include="plugins\images\property\pro-bed.png" />
    <Content Include="plugins\images\property\pro-garage.png" />
    <Content Include="plugins\images\property\prop1.jpeg" />
    <Content Include="plugins\images\property\prop2.jpeg" />
    <Content Include="plugins\images\property\prop3.jpeg" />
    <Content Include="plugins\images\property\prop4.jpeg" />
    <Content Include="plugins\images\property\prop5.jpg" />
    <Content Include="plugins\images\property\prop6.jpg" />
    <Content Include="plugins\images\property\prop7.jpg" />
    <Content Include="plugins\images\property\prop8.jpg" />
    <Content Include="plugins\images\report-logo.png" />
    <Content Include="plugins\images\screen1.jpg" />
    <Content Include="plugins\images\screen2.jpg" />
    <Content Include="plugins\images\screen3.jpg" />
    <Content Include="plugins\images\screen4.jpg" />
    <Content Include="plugins\images\screen5.jpg" />
    <Content Include="plugins\images\secure-label-s.svg" />
    <Content Include="plugins\images\secure-label.svg" />
    <Content Include="plugins\images\small\vd1.jpg" />
    <Content Include="plugins\images\small\vd10.jpg" />
    <Content Include="plugins\images\small\vd11.jpg" />
    <Content Include="plugins\images\small\vd12.jpg" />
    <Content Include="plugins\images\small\vd13.jpg" />
    <Content Include="plugins\images\small\vd14.jpg" />
    <Content Include="plugins\images\small\vd2.jpg" />
    <Content Include="plugins\images\small\vd3.jpg" />
    <Content Include="plugins\images\small\vd4.jpg" />
    <Content Include="plugins\images\small\vd5.jpg" />
    <Content Include="plugins\images\small\vd6.jpg" />
    <Content Include="plugins\images\small\vd7.jpg" />
    <Content Include="plugins\images\small\vd8.jpg" />
    <Content Include="plugins\images\small\vd9.jpg" />
    <Content Include="plugins\images\sort_asc.png" />
    <Content Include="plugins\images\sort_asc_disabled.png" />
    <Content Include="plugins\images\sort_both.png" />
    <Content Include="plugins\images\sort_desc.png" />
    <Content Include="plugins\images\sort_desc_disabled.png" />
    <Content Include="plugins\images\tooltip\Euclid.png" />
    <Content Include="plugins\images\tooltip\shape1.svg" />
    <Content Include="plugins\images\tooltip\shape2.svg" />
    <Content Include="plugins\images\tooltip\shape3.svg" />
    <Content Include="plugins\images\tooltip\tooltip1.svg" />
    <Content Include="plugins\images\tooltip\tooltip2.svg" />
    <Content Include="plugins\images\tooltip\tooltip3.svg" />
    <Content Include="plugins\images\users\1.jpg" />
    <Content Include="plugins\images\users\2.jpg" />
    <Content Include="plugins\images\users\3.jpg" />
    <Content Include="plugins\images\users\4.jpg" />
    <Content Include="plugins\images\users\5.jpg" />
    <Content Include="plugins\images\users\6.jpg" />
    <Content Include="plugins\images\users\7.jpg" />
    <Content Include="plugins\images\users\8.jpg" />
    <Content Include="plugins\images\users\agent.jpg" />
    <Content Include="plugins\images\users\agent2.jpg" />
    <Content Include="plugins\images\users\arijit.jpg" />
    <Content Include="plugins\images\users\d1.jpg" />
    <Content Include="plugins\images\users\d2.jpg" />
    <Content Include="plugins\images\users\d3.jpg" />
    <Content Include="plugins\images\users\d4.jpg" />
    <Content Include="plugins\images\users\d5.jpg" />
    <Content Include="plugins\images\users\genu.jpg" />
    <Content Include="plugins\images\users\govinda.jpg" />
    <Content Include="plugins\images\users\hritik.jpg" />
    <Content Include="plugins\images\users\john.jpg" />
    <Content Include="plugins\images\users\pawandeep.jpg" />
    <Content Include="plugins\images\users\ritesh.jpg" />
    <Content Include="plugins\images\users\salman.jpg" />
    <Content Include="plugins\images\users\sonu.jpg" />
    <Content Include="plugins\images\users\varun.jpg" />
    <Content Include="plugins\images\weather-bg.jpg" />
    <Content Include="plugins\input-mask\jquery.inputmask.date.extensions.js" />
    <Content Include="plugins\input-mask\jquery.inputmask.extensions.js" />
    <Content Include="plugins\input-mask\jquery.inputmask.js" />
    <Content Include="plugins\input-mask\jquery.inputmask.numeric.extensions.js" />
    <Content Include="plugins\input-mask\jquery.inputmask.phone.extensions.js" />
    <Content Include="plugins\input-mask\jquery.inputmask.regex.extensions.js" />
    <Content Include="plugins\input-mask\phone-codes\readme.txt" />
    <Content Include="plugins\ionslider\img\sprite-skin-flat.png" />
    <Content Include="plugins\ionslider\img\sprite-skin-nice.png" />
    <Content Include="plugins\ionslider\ion.rangeSlider.css" />
    <Content Include="plugins\ionslider\ion.rangeSlider.min.js" />
    <Content Include="plugins\ionslider\ion.rangeSlider.skinFlat.css" />
    <Content Include="plugins\ionslider\ion.rangeSlider.skinNice.css" />
    <Content Include="plugins\jQueryUI\jquery-ui.js" />
    <Content Include="plugins\jQueryUI\jquery-ui.min.js" />
    <Content Include="plugins\jQuery\jQuery-2.1.4.min.js" />
    <Content Include="plugins\jQuery\jquery.min.js" />
    <Content Include="plugins\morris\morris.css" />
    <Content Include="plugins\morris\morris.js" />
    <Content Include="plugins\morris\morris.min.js" />
    <Content Include="plugins\pdfmake\pdfmake.js" />
    <Content Include="plugins\pdfmake\pdfmake.min.js" />
    <Content Include="plugins\pdfmake\vfs_fonts.js" />
    <Content Include="plugins\pdfmake\vfs_fonts.min.js" />
    <Content Include="plugins\select2\i18n\az.js" />
    <Content Include="plugins\select2\i18n\bg.js" />
    <Content Include="plugins\select2\i18n\ca.js" />
    <Content Include="plugins\select2\i18n\cs.js" />
    <Content Include="plugins\select2\i18n\da.js" />
    <Content Include="plugins\select2\i18n\de.js" />
    <Content Include="plugins\select2\i18n\en.js" />
    <Content Include="plugins\select2\i18n\es.js" />
    <Content Include="plugins\select2\i18n\et.js" />
    <Content Include="plugins\select2\i18n\eu.js" />
    <Content Include="plugins\select2\i18n\fa.js" />
    <Content Include="plugins\select2\i18n\fi.js" />
    <Content Include="plugins\select2\i18n\fr.js" />
    <Content Include="plugins\select2\i18n\gl.js" />
    <Content Include="plugins\select2\i18n\he.js" />
    <Content Include="plugins\select2\i18n\hi.js" />
    <Content Include="plugins\select2\i18n\hr.js" />
    <Content Include="plugins\select2\i18n\hu.js" />
    <Content Include="plugins\select2\i18n\id.js" />
    <Content Include="plugins\select2\i18n\is.js" />
    <Content Include="plugins\select2\i18n\it.js" />
    <Content Include="plugins\select2\i18n\ko.js" />
    <Content Include="plugins\select2\i18n\lt.js" />
    <Content Include="plugins\select2\i18n\lv.js" />
    <Content Include="plugins\select2\i18n\mk.js" />
    <Content Include="plugins\select2\i18n\nb.js" />
    <Content Include="plugins\select2\i18n\nl.js" />
    <Content Include="plugins\select2\i18n\pl.js" />
    <Content Include="plugins\select2\i18n\pt-BR.js" />
    <Content Include="plugins\select2\i18n\pt.js" />
    <Content Include="plugins\select2\i18n\ro.js" />
    <Content Include="plugins\select2\i18n\ru.js" />
    <Content Include="plugins\select2\i18n\sk.js" />
    <Content Include="plugins\select2\i18n\sr.js" />
    <Content Include="plugins\select2\i18n\sv.js" />
    <Content Include="plugins\select2\i18n\th.js" />
    <Content Include="plugins\select2\i18n\tr.js" />
    <Content Include="plugins\select2\i18n\uk.js" />
    <Content Include="plugins\select2\i18n\vi.js" />
    <Content Include="plugins\select2\i18n\zh-CN.js" />
    <Content Include="plugins\select2\i18n\zh-TW.js" />
    <Content Include="plugins\select2\select2-bootstrap.min.css" />
    <Content Include="plugins\select2\select2.css" />
    <Content Include="plugins\select2\select2.full.js" />
    <Content Include="plugins\select2\select2.full.min.js" />
    <Content Include="plugins\select2\select2.js" />
    <Content Include="plugins\select2\select2.min.css" />
    <Content Include="plugins\select2\select2.min.js" />
    <Content Include="scripts\ai.0.22.19-build00125.js" />
    <Content Include="scripts\ai.0.22.19-build00125.min.js" />
    <Content Include="scripts\bootstrap.js" />
    <Content Include="scripts\bootstrap.min.js" />
    <Content Include="bootstrap\css\bootstrap.css.map" />
    <Content Include="bootstrap\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="bootstrap\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="bootstrap\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="bootstrap\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="libman.json" />
    <Content Include="appsettings.sicoob.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="appsettings.genial.json" />
    <Content Include="appsettings.delbank.json" />
    <Content Include="fontawesome6\webfonts\fa-brands-400.ttf" />
    <Content Include="fontawesome6\webfonts\fa-brands-400.woff2" />
    <Content Include="fontawesome6\webfonts\fa-regular-400.ttf" />
    <Content Include="fontawesome6\webfonts\fa-regular-400.woff2" />
    <Content Include="fontawesome6\webfonts\fa-solid-900.ttf" />
    <Content Include="fontawesome6\webfonts\fa-solid-900.woff2" />
    <Content Include="fontawesome6\webfonts\fa-v4compatibility.ttf" />
    <Content Include="fontawesome6\webfonts\fa-v4compatibility.woff2" />
    <Content Include="appsettings.bancodock.json" />
    <Content Include="appsettings.mkbank.json" />
    <None Include="packages.config" />
    <Content Include="plugins\datatables\extensions\ColReorder\Readme.md" />
    <Content Include="plugins\datatables\extensions\ColVis\Readme.md" />
    <Content Include="plugins\datatables\extensions\FixedColumns\Readme.md" />
    <Content Include="plugins\datatables\extensions\Responsive\css\dataTables.responsive.scss" />
    <Content Include="plugins\datatables\extensions\Responsive\Readme.md" />
    <Content Include="plugins\datatables\extensions\TableTools\images\psd\collection.psd" />
    <Content Include="plugins\datatables\extensions\TableTools\images\psd\copy document.psd" />
    <Content Include="plugins\datatables\extensions\TableTools\images\psd\file_types.psd" />
    <Content Include="plugins\datatables\extensions\TableTools\images\psd\printer.psd" />
    <Content Include="plugins\datatables\extensions\TableTools\Readme.md" />
    <Content Include="plugins\datatables\Bk\extensions\ColReorder\Readme.md" />
    <Content Include="plugins\datatables\Bk\extensions\ColVis\Readme.md" />
    <Content Include="plugins\datatables\Bk\extensions\FixedColumns\Readme.md" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\css\dataTables.responsive.scss" />
    <Content Include="plugins\datatables\Bk\extensions\Responsive\Readme.md" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\psd\collection.psd" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\psd\copy document.psd" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\psd\file_types.psd" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\images\psd\printer.psd" />
    <Content Include="plugins\datatables\Bk\extensions\TableTools\Readme.md" />
    <Content Include="plugins\bower_components\bootstrap-old\.bower.json" />
    <Content Include="plugins\bower_components\bootstrap-old\bower.json" />
    <Content Include="plugins\bower_components\bootstrap-old\CHANGELOG.md" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\css\bootstrap-theme.css.map" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\css\bootstrap-theme.min.css.map" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\css\bootstrap.css.map" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\css\bootstrap.min.css.map" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="plugins\bower_components\bootstrap-old\dist\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="plugins\bower_components\bootstrap-old\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="plugins\bower_components\bootstrap-old\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="plugins\bower_components\bootstrap-old\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="plugins\bower_components\bootstrap-old\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="plugins\bower_components\bootstrap-old\grunt\.jshintrc" />
    <Content Include="plugins\bower_components\bootstrap-old\grunt\configBridge.json" />
    <Content Include="plugins\bower_components\bootstrap-old\grunt\sauce_browsers.yml" />
    <Content Include="plugins\bower_components\bootstrap-old\js\.jscsrc" />
    <Content Include="plugins\bower_components\bootstrap-old\js\.jshintrc" />
    <Content Include="plugins\bower_components\bootstrap-old\less\.csscomb.json" />
    <Content Include="plugins\bower_components\bootstrap-old\less\.csslintrc" />
    <Content Include="plugins\bower_components\bootstrap-old\less\alerts.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\badges.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\bootstrap.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\breadcrumbs.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\button-groups.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\buttons.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\carousel.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\close.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\code.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\component-animations.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\dropdowns.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\forms.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\glyphicons.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\grid.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\input-groups.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\jumbotron.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\labels.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\list-group.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\media.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\alerts.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\background-variant.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\border-radius.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\buttons.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\center-block.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\clearfix.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\forms.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\gradients.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\grid-framework.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\grid.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\hide-text.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\image.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\labels.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\list-group.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\nav-divider.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\nav-vertical-align.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\opacity.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\pagination.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\panels.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\progress-bar.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\reset-filter.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\reset-text.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\resize.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\responsive-visibility.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\size.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\tab-focus.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\table-row.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\text-emphasis.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\text-overflow.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\mixins\vendor-prefixes.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\modals.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\navbar.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\navs.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\normalize.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\pager.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\pagination.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\panels.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\popovers.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\print.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\progress-bars.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\responsive-embed.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\responsive-utilities.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\scaffolding.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\tables.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\theme.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\thumbnails.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\tooltip.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\type.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\utilities.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\variables.less" />
    <Content Include="plugins\bower_components\bootstrap-old\less\wells.less" />
    <Content Include="plugins\bower_components\bootstrap-old\LICENSE" />
    <Content Include="plugins\bower_components\bootstrap-old\nuget\bootstrap.less.nuspec" />
    <Content Include="plugins\bower_components\bootstrap-old\nuget\bootstrap.nuspec" />
    <Content Include="plugins\bower_components\bootstrap-old\nuget\MyGet.ps1" />
    <Content Include="plugins\bower_components\bootstrap-old\package.json" />
    <Content Include="plugins\bower_components\bootstrap-old\README.md" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\bower.json" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\dist\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\dist\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\dist\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\dist\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="plugins\bower_components\bootstrap-rtl-master\README.md" />
    <Content Include="plugins\bower_components\bootstrap-social\.bower.json" />
    <Content Include="plugins\bower_components\bootstrap-social\.editorconfig" />
    <Content Include="plugins\bower_components\bootstrap-social\bootstrap-social.less" />
    <Content Include="plugins\bower_components\bootstrap-social\bootstrap-social.scss" />
    <Content Include="plugins\bower_components\bootstrap-social\bower.json" />
    <Content Include="plugins\bower_components\bootstrap-social\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\bootstrap-test.json" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\cookie\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\editable\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\export\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\filter-control\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\filter\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\flat-json\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\key-events\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\mobile\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\multiple-sort\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\natural-sorting\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\reorder-columns\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\reorder-rows\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\resizable\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\extensions\toolbar\README.md" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\bootstrap-table-en-US.js.template" />
    <Content Include="plugins\bower_components\bootstrap-table\src\locale\README.md" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\dist\bootstrap-tagsinput-angular.min.js.map" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\dist\bootstrap-tagsinput.less" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\dist\bootstrap-tagsinput.min.js.map" />
    <Content Include="plugins\bower_components\bootstrap-tagsinput\dist\bootstrap-tagsinput.zip" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\js\bootstrap-toggle.min.js.map" />
    <Content Include="plugins\bower_components\bootstrap-toggle-master\js\bootstrap2-toggle.min.js.map" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\.bowerrc" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\.jshintrc" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\.travis.yml" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\bower.json" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\CHANGELOG.md" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\LICENSE" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\package.json" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\README.md" />
    <Content Include="plugins\bower_components\bootstrap-treeview-master\tests\README.md" />
    <Content Include="plugins\bower_components\chartist-js\dist\chartist.css.map" />
    <Content Include="plugins\bower_components\chartist-js\dist\chartist.min.js.map" />
    <Content Include="plugins\bower_components\chartist-js\dist\scss\chartist.scss" />
    <Content Include="plugins\bower_components\chartist-js\dist\scss\settings\_chartist-settings.scss" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\bower.json" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\CHANGELOG.md" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\dist\chartist-plugin-tooltip.css.map" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\dist\chartist-plugin-tooltip.min.js.map" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\dist\LICENSE" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\LICENSE" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\package.json" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\README.md" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\src\css\chartist-plugin-tooltip.css.map" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\src\scss\chartist-plugin-tooltip.scss" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\tasks\aliases.yml" />
    <Content Include="plugins\bower_components\chartist-plugin-tooltip-master\test\.jshintrc" />
    <Content Include="plugins\bower_components\datatables-plugins\.bower.json" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Afrikaans.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Albanian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Arabic.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Azerbaijan.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Bangla.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Belarusian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Bulgarian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Catalan.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Chinese-traditional.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Chinese.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Croatian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Czech.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Danish.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Dutch.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\English.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Estonian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Filipino.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Finnish.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\French.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Galician.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Georgian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\German.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Greek.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Gujarati.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Hebrew.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Hindi.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Hungarian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Icelandic.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Indonesian-Alternative.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Indonesian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Irish.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Italian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Japanese.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Korean.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Latvian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Lithuanian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Macedonian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Malay.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Norwegian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Persian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Polish.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Portuguese-Brasil.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Portuguese.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Romanian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Russian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Serbian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Slovak.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Slovenian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Spanish.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Swahili.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Swedish.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Tamil.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Thai.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Turkish.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Ukranian.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Urdu.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Uzbek.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\i18n\Vietnamese.lang" />
    <Content Include="plugins\bower_components\datatables-plugins\integration\jqueryui\dataTables.jqueryui.scss" />
    <Content Include="plugins\bower_components\datatables-plugins\make.sh" />
    <Content Include="plugins\bower_components\datatables-plugins\README.md" />
    <Content Include="plugins\bower_components\datatables-responsive\.bower.json" />
    <Content Include="plugins\bower_components\datatables-responsive\bower.json" />
    <Content Include="plugins\bower_components\datatables-responsive\css\responsive.bootstrap.scss" />
    <Content Include="plugins\bower_components\datatables-responsive\css\responsive.dataTables.scss" />
    <Content Include="plugins\bower_components\datatables-responsive\css\responsive.foundation.scss" />
    <Content Include="plugins\bower_components\datatables-responsive\css\responsive.jqueryui.scss" />
    <Content Include="plugins\bower_components\datatables-responsive\make.sh" />
    <Content Include="plugins\bower_components\datatables-responsive\Readme.md" />
    <Content Include="plugins\bower_components\datatables\.bower.json" />
    <Content Include="plugins\bower_components\datatables\bower.json" />
    <Content Include="plugins\bower_components\datatables\Contributing.md" />
    <Content Include="plugins\bower_components\datatables\media\images\Sorting icons.psd" />
    <Content Include="plugins\bower_components\datatables\Readme.md" />
    <Content Include="plugins\bower_components\dropify\dist\fonts\dropify.eot" />
    <Content Include="plugins\bower_components\dropify\dist\fonts\dropify.ttf" />
    <Content Include="plugins\bower_components\dropify\dist\fonts\dropify.woff" />
    <Content Include="plugins\bower_components\dropify\package.json" />
    <Content Include="plugins\bower_components\dropify\README.md" />
    <Content Include="plugins\bower_components\dropify\src\fonts\dropify.eot" />
    <Content Include="plugins\bower_components\dropify\src\fonts\dropify.ttf" />
    <Content Include="plugins\bower_components\dropify\src\fonts\dropify.woff" />
    <Content Include="plugins\bower_components\dropify\src\sass\demo.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\demo\helpers\_grid.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\demo\helpers\_mixins.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\demo\helpers\_shortcuts.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\demo\_grid.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\demo\_normalize.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\demo\_reset.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\demo\_typo.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\demo\_variables.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\dropify.scss" />
    <Content Include="plugins\bower_components\dropify\src\sass\_dropify-font.scss" />
    <Content Include="plugins\bower_components\dropzone-master\.tagconfig" />
    <Content Include="plugins\bower_components\dropzone-master\.travis.yml" />
    <Content Include="plugins\bower_components\dropzone-master\AMD_footer" />
    <Content Include="plugins\bower_components\dropzone-master\AMD_header" />
    <Content Include="plugins\bower_components\dropzone-master\bower.json" />
    <Content Include="plugins\bower_components\dropzone-master\component.json" />
    <Content Include="plugins\bower_components\dropzone-master\composer.json" />
    <Content Include="plugins\bower_components\dropzone-master\CONTRIBUTING.md" />
    <Content Include="plugins\bower_components\dropzone-master\dist\readme.md" />
    <Content Include="plugins\bower_components\dropzone-master\Gruntfile.coffee" />
    <Content Include="plugins\bower_components\dropzone-master\LICENSE" />
    <Content Include="plugins\bower_components\dropzone-master\package.json" />
    <Content Include="plugins\bower_components\dropzone-master\README.md" />
    <Content Include="plugins\bower_components\dropzone-master\src\basic.scss" />
    <Content Include="plugins\bower_components\dropzone-master\src\dropzone.coffee" />
    <Content Include="plugins\bower_components\dropzone-master\src\dropzone.scss" />
    <Content Include="plugins\bower_components\dropzone-master\test.sh" />
    <Content Include="plugins\bower_components\dropzone-master\test\test.coffee" />
    <Content Include="plugins\bower_components\flot.tooltip\.bower.json" />
    <Content Include="plugins\bower_components\flot.tooltip\bower.json" />
    <Content Include="plugins\bower_components\flot\.bower.json" />
    <Content Include="plugins\bower_components\flot\.travis.yml" />
    <Content Include="plugins\bower_components\flot\API.md" />
    <Content Include="plugins\bower_components\flot\component.json" />
    <Content Include="plugins\bower_components\flot\CONTRIBUTING.md" />
    <Content Include="plugins\bower_components\flot\examples\ajax\data-eu-gdp-growth-1.json" />
    <Content Include="plugins\bower_components\flot\examples\ajax\data-eu-gdp-growth-2.json" />
    <Content Include="plugins\bower_components\flot\examples\ajax\data-eu-gdp-growth-3.json" />
    <Content Include="plugins\bower_components\flot\examples\ajax\data-eu-gdp-growth-4.json" />
    <Content Include="plugins\bower_components\flot\examples\ajax\data-eu-gdp-growth-5.json" />
    <Content Include="plugins\bower_components\flot\examples\ajax\data-eu-gdp-growth.json" />
    <Content Include="plugins\bower_components\flot\examples\ajax\data-japan-gdp-growth.json" />
    <Content Include="plugins\bower_components\flot\examples\ajax\data-usa-gdp-growth.json" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\africa" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\antarctica" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\asia" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\australasia" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\backward" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\etcetera" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\europe" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\factory" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\iso3166.tab" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\leapseconds" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\northamerica" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\pacificnew" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\solar87" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\solar88" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\solar89" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\southamerica" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\systemv" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\yearistype.sh" />
    <Content Include="plugins\bower_components\flot\examples\axes-time-zones\tz\zone.tab" />
    <Content Include="plugins\bower_components\flot\FAQ.md" />
    <Content Include="plugins\bower_components\flot\flot.jquery.json" />
    <Content Include="plugins\bower_components\flot\Makefile" />
    <Content Include="plugins\bower_components\flot\NEWS.md" />
    <Content Include="plugins\bower_components\flot\package.json" />
    <Content Include="plugins\bower_components\flot\PLUGINS.md" />
    <Content Include="plugins\bower_components\flot\README.md" />
    <Content Include="plugins\bower_components\footable\css\fonts\footable.eot" />
    <Content Include="plugins\bower_components\footable\css\fonts\footable.ttf" />
    <Content Include="plugins\bower_components\footable\css\fonts\footable.woff" />
    <Content Include="plugins\bower_components\gmaps\gmaps.min.js.map" />
    <Content Include="plugins\bower_components\holderjs\.bower.json" />
    <Content Include="plugins\bower_components\holderjs\.jshintrc" />
    <Content Include="plugins\bower_components\holderjs\bower.json" />
    <Content Include="plugins\bower_components\holderjs\composer.json" />
    <Content Include="plugins\bower_components\holderjs\package.json" />
    <Content Include="plugins\bower_components\holderjs\README.md" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\.editorconfig" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\.jscsrc" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\.jshintrc" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\bower.json" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\less\jquery-asColorPicker.less" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\LICENSE-GPL" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\package.json" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\README.md" />
    <Content Include="plugins\bower_components\jquery-asColorPicker-master\src\.jshintrc" />
    <Content Include="plugins\bower_components\jquery-steps-master\.travis.yml" />
    <Content Include="plugins\bower_components\jquery-steps-master\bower.json" />
    <Content Include="plugins\bower_components\jquery-steps-master\CHANGELOG.md" />
    <Content Include="plugins\bower_components\jquery-steps-master\nuget\jQuery.Steps.nuspec" />
    <Content Include="plugins\bower_components\jquery-steps-master\package.json" />
    <Content Include="plugins\bower_components\jquery-steps-master\README.md" />
    <Content Include="plugins\bower_components\jquery-steps-master\steps.jquery.json" />
    <Content Include="plugins\bower_components\jquery-wizard-master\.csscomb.json" />
    <Content Include="plugins\bower_components\jquery-wizard-master\.editorconfig" />
    <Content Include="plugins\bower_components\jquery-wizard-master\.jscsrc" />
    <Content Include="plugins\bower_components\jquery-wizard-master\.jshintrc" />
    <Content Include="plugins\bower_components\jquery-wizard-master\bower.json" />
    <Content Include="plugins\bower_components\jquery-wizard-master\CONTRIBUTING.md" />
    <Content Include="plugins\bower_components\jquery-wizard-master\less\wizard.less" />
    <Content Include="plugins\bower_components\jquery-wizard-master\LICENSE-GPL" />
    <Content Include="plugins\bower_components\jquery-wizard-master\package.json" />
    <Content Include="plugins\bower_components\jquery-wizard-master\README.md" />
    <Content Include="plugins\bower_components\jquery-wizard-master\src\.jshintrc" />
    <Content Include="plugins\bower_components\jquery\.bower.json" />
    <Content Include="plugins\bower_components\jquery\bower.json" />
    <Content Include="plugins\bower_components\jquery\dist\jquery.min.map" />
    <Content Include="plugins\bower_components\jquery\src\sizzle\dist\sizzle.min.map" />
    <Content Include="plugins\bower_components\jsgrid\.editorconfig" />
    <Content Include="plugins\bower_components\jsgrid\.npmignore" />
    <Content Include="plugins\bower_components\jsgrid\.travis.yml" />
    <Content Include="plugins\bower_components\jsgrid\bower.json" />
    <Content Include="plugins\bower_components\jsgrid\LICENSE" />
    <Content Include="plugins\bower_components\jsgrid\package.json" />
    <Content Include="plugins\bower_components\jsgrid\README.md" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\.jshintrc" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\.travis.yml" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\bower.json" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\composer.json" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\LICENSE" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\magnific-popup.jquery.json" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\package.json" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\README.md" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\css\main.scss" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\src\css\_settings.scss" />
    <Content Include="plugins\bower_components\Magnific-Popup-master\website\documentation.md" />
    <Content Include="plugins\bower_components\magnific-popup\src\css\main.scss" />
    <Content Include="plugins\bower_components\magnific-popup\src\css\_settings.scss" />
    <Content Include="plugins\bower_components\Minimal-Gauge-chart\README.md" />
    <Content Include="plugins\bower_components\mocha\.bower.json" />
    <Content Include="plugins\bower_components\mocha\bower.json" />
    <Content Include="plugins\bower_components\mocha\History.md" />
    <Content Include="plugins\bower_components\mocha\LICENSE" />
    <Content Include="plugins\bower_components\mocha\Readme.md" />
    <Content Include="plugins\bower_components\morrisjs\.bower.json" />
    <Content Include="plugins\bower_components\morrisjs\.travis.yml" />
    <Content Include="plugins\bower_components\morrisjs\bower.json" />
    <Content Include="plugins\bower_components\morrisjs\bower.travis.json" />
    <Content Include="plugins\bower_components\morrisjs\less\morris.core.less" />
    <Content Include="plugins\bower_components\morrisjs\lib\morris.area.coffee" />
    <Content Include="plugins\bower_components\morrisjs\lib\morris.bar.coffee" />
    <Content Include="plugins\bower_components\morrisjs\lib\morris.coffee" />
    <Content Include="plugins\bower_components\morrisjs\lib\morris.donut.coffee" />
    <Content Include="plugins\bower_components\morrisjs\lib\morris.grid.coffee" />
    <Content Include="plugins\bower_components\morrisjs\lib\morris.hover.coffee" />
    <Content Include="plugins\bower_components\morrisjs\lib\morris.line.coffee" />
    <Content Include="plugins\bower_components\morrisjs\package.json" />
    <Content Include="plugins\bower_components\morrisjs\README.md" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\area\area_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\bar\bar_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\bar\colours.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\commas_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\donut\donut_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\grid\auto_grid_lines_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\grid\set_data_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\grid\y_label_format_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\hover_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\label_series_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\line\line_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\pad_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\lib\parse_time_spec.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\support\placeholder.coffee" />
    <Content Include="plugins\bower_components\morrisjs\spec\viz\run.sh" />
    <Content Include="plugins\bower_components\owl.carousel\owl.carousel - Shortcut.lnk" />
    <Content Include="plugins\bower_components\raphael\.bower.json" />
    <Content Include="plugins\bower_components\raphael\bower.json" />
    <Content Include="plugins\bower_components\sidebar-nav\.bower.json" />
    <Content Include="plugins\bower_components\sidebar-nav\bower.json" />
    <Content Include="plugins\bower_components\sidebar-nav\LICENSE" />
    <Content Include="plugins\bower_components\sidebar-nav\package.json" />
    <Content Include="plugins\bower_components\sidebar-nav\README.md" />
    <Content Include="plugins\bower_components\summernote\meteor\publish.sh" />
    <Content Include="plugins\bower_components\summernote\meteor\README.md" />
    <Content Include="plugins\bower_components\summernote\meteor\runtests.sh" />
    <Content Include="plugins\bower_components\summernote\src\less\elements.less" />
    <Content Include="plugins\bower_components\summernote\src\less\summernote.less" />
    <Content Include="plugins\bower_components\summernote\src\sass\summernote.scss" />
    <Content Include="plugins\bower_components\tablesaw-master\.jshintrc" />
    <Content Include="plugins\bower_components\tablesaw-master\.travis.yml" />
    <Content Include="plugins\bower_components\tablesaw-master\bower.json" />
    <Content Include="plugins\bower_components\tablesaw-master\CONTRIBUTING.md" />
    <Content Include="plugins\bower_components\tablesaw-master\dist\stackonly\tablesaw.stackonly.scss" />
    <Content Include="plugins\bower_components\tablesaw-master\LICENSE" />
    <Content Include="plugins\bower_components\tablesaw-master\package.json" />
    <Content Include="plugins\bower_components\tablesaw-master\README.md" />
    <Content Include="plugins\bower_components\tablesaw-master\src\.jshintrc" />
    <Content Include="plugins\bower_components\tablesaw-master\src\tables.stack-mixin.scss" />
    <Content Include="plugins\bower_components\tablesaw-master\test\.jshintrc" />
    <Content Include="plugins\bower_components\tinymce\langs\readme.md" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\fonts\tinymce-small.eot" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\fonts\tinymce-small.ttf" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\fonts\tinymce-small.woff" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\fonts\tinymce.eot" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\fonts\tinymce.ttf" />
    <Content Include="plugins\bower_components\tinymce\skins\lightgray\fonts\tinymce.woff" />
    <Content Include="plugins\bower_components\typeahead.js-master\.jshintrc" />
    <Content Include="plugins\bower_components\typeahead.js-master\.travis.yml" />
    <Content Include="plugins\bower_components\typeahead.js-master\bower.json" />
    <Content Include="plugins\bower_components\typeahead.js-master\CHANGELOG.md" />
    <Content Include="plugins\bower_components\typeahead.js-master\composer.json" />
    <Content Include="plugins\bower_components\typeahead.js-master\CONTRIBUTING.md" />
    <Content Include="plugins\bower_components\typeahead.js-master\countries.json" />
    <Content Include="plugins\bower_components\typeahead.js-master\doc\bloodhound.md" />
    <Content Include="plugins\bower_components\typeahead.js-master\doc\jquery_typeahead.md" />
    <Content Include="plugins\bower_components\typeahead.js-master\doc\migration\0.10.0.md" />
    <Content Include="plugins\bower_components\typeahead.js-master\LICENSE" />
    <Content Include="plugins\bower_components\typeahead.js-master\nba.json" />
    <Content Include="plugins\bower_components\typeahead.js-master\nfl.json" />
    <Content Include="plugins\bower_components\typeahead.js-master\nhl.json" />
    <Content Include="plugins\bower_components\typeahead.js-master\package.json" />
    <Content Include="plugins\bower_components\typeahead.js-master\README.md" />
    <Content Include="plugins\bower_components\typeahead.js-master\test\ci" />
    <Content Include="plugins\bower_components\typeahead.js-master\typeahead.js.jquery.json" />
    <Content Include="plugins\datatables\Contributing.md" />
    <Content Include="plugins\datatables\Readme.md" />
    <Content Include="plugins\images\assets\photothumb.db" />
    <Content Include="plugins\datatables-buttons\js\buttons.colVis.min.mjs" />
    <Content Include="plugins\datatables-buttons\js\buttons.colVis.mjs" />
    <Content Include="plugins\datatables-buttons\js\buttons.html5.min.mjs" />
    <Content Include="plugins\datatables-buttons\js\buttons.html5.mjs" />
    <Content Include="plugins\datatables-buttons\js\buttons.print.min.mjs" />
    <Content Include="plugins\datatables-buttons\js\buttons.print.mjs" />
    <Content Include="plugins\datatables-buttons\js\dataTables.buttons.min.mjs" />
    <Content Include="plugins\datatables-buttons\js\dataTables.buttons.mjs" />
    <Content Include="plugins\pdfmake\fonts\Roboto\Roboto-Italic.ttf" />
    <Content Include="plugins\pdfmake\fonts\Roboto\Roboto-Medium.ttf" />
    <Content Include="plugins\pdfmake\fonts\Roboto\Roboto-MediumItalic.ttf" />
    <Content Include="plugins\pdfmake\fonts\Roboto\Roboto-Regular.ttf" />
    <Content Include="plugins\pdfmake\pdfmake.js.map" />
    <Content Include="plugins\pdfmake\pdfmake.min.js.map" />
    <None Include="Properties\PublishProfiles\BETBANK.pubxml" />
    <None Include="scripts\jquery-1.10.2.intellisense.js" />
    <Content Include="scripts\general.js" />
    <Content Include="scripts\jquery-1.10.2.js" />
    <Content Include="scripts\jquery-1.10.2.min.js" />
    <None Include="scripts\jquery-1.4.4-vsdoc.js" />
    <Content Include="scripts\jquery-1.4.4.js" />
    <Content Include="scripts\jquery-1.4.4.min.js" />
    <None Include="scripts\jquery-3.0.0.intellisense.js" />
    <Content Include="scripts\jquery-3.0.0.js" />
    <Content Include="scripts\jquery-3.0.0.min.js" />
    <Content Include="scripts\jquery-3.0.0.slim.js" />
    <Content Include="scripts\jquery-3.0.0.slim.min.js" />
    <Content Include="scripts\jquery-ui-1.10.4.custom.min.js" />
    <Content Include="scripts\jquery.maskedinput.min.js" />
    <Content Include="scripts\modernizr-2.6.2.js" />
    <Content Include="scripts\PigPag.Common.js" />
    <Content Include="scripts\View\Blacklist\blacklistConsulta.js" />
    <Content Include="scripts\View\Blacklist\blacklist.js" />
    <Content Include="scripts\View\ChavePIXFavorecidosTodasChaves\chavePIXFavorecidosTodasChaves.js" />
    <Content Include="scripts\View\Cliente\Alterar.js" />
    <Content Include="scripts\View\Cliente\Cadastrar.js" />
    <Content Include="scripts\View\Cobranca\Cobranca.js" />
    <Content Include="scripts\View\Empresa\Empresa.js" />
    <Content Include="scripts\View\Financeiro\BuscarCobranca.js" />
    <Content Include="scripts\View\Financeiro\Financeiro.js" />
    <Content Include="scripts\jquery.mask.min.js" />
    <Content Include="scripts\View\GerenciarAcessoPerfil\gerenciar-acesso-perfil.js" />
    <Content Include="scripts\View\GerenciarAcessoPerfil\selecionar-operador.js" />
    <Content Include="scripts\View\Tabela\Categoria.js" />
    <Content Include="scripts\View\VitMarketing\Campanhas.js" />
    <Content Include="scripts\View\VitMarketing\Visualizar.js" />
    <Content Include="Web.config" />
    <Content Include="Views\AntecipacaoRecebiveis\AntecipacaoComDataRetroativa.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\AntecipacoesEmAnalise.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\AnteciparRecebiveis.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\AnteciparRecebiveisCliente.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\DetalheAntecipacaoDiaCliente.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\DetalheAntecipacaoPeriodoCliente.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\DetalheAntecipacoesEmAnalise.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\PartialPesquisarClienteAnteciparRecebiveis.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\_PartialAcessoNegado.cshtml" />
    <Content Include="Views\Anuncio\Cadastrar.cshtml" />
    <Content Include="Views\Aplicacao\Alterar.cshtml" />
    <Content Include="Views\Aplicacao\Cadastrar.cshtml" />
    <Content Include="Views\Aplicacao\Index.cshtml" />
    <Content Include="Views\Arquivos\ArquivosRemessa.cshtml" />
    <Content Include="Views\Arquivos\ArquivosRetorno.cshtml" />
    <Content Include="Views\Arquivos\BradescoGerarArquivoRemessaTransferencias.cshtml" />
    <Content Include="Views\Arquivos\CaixaProcessarArquivoRetorno.cshtml" />
    <Content Include="Views\Arquivos\ItauGerarArquivoRemessa.cshtml" />
    <Content Include="Views\Arquivos\ItauProcessarArquivoRetorno.cshtml" />
    <Content Include="Views\Arquivos\SantanderGerarArquivoRemessa.cshtml" />
    <Content Include="Views\Arquivos\SantanderProcessarArquivoRetorno.cshtml" />
    <Content Include="Views\Arquivos\SantanderProcessarArquivoRetornoCNAB240.cshtml" />
    <Content Include="Views\Associado\DiretosAssociado.cshtml" />
    <Content Include="Views\Associado\SaldoAssociado.cshtml" />
    <Content Include="Views\Bitcoin\ConsultaTransacoesBitcoin.cshtml" />
    <Content Include="Views\Bitcoin\FazerTransferencia.cshtml" />
    <Content Include="Views\Bitcoin\GetSaldoMSCoin.cshtml" />
    <Content Include="Views\Bitcoin\HashsPerdidas.cshtml" />
    <Content Include="Views\Bitcoin\ListasChavesTransacoesMSCoinBCH.cshtml" />
    <Content Include="Views\Bitcoin\ListasContasMSCoin.cshtml" />
    <Content Include="Views\Bitcoin\ListasTransacoesMSCoin.cshtml" />
    <Content Include="Views\Bitcoin\ListasTransacoesMSCoinBCH.cshtml" />
    <Content Include="Views\Boleto\BoletosGeradosAguardandoRemessa.cshtml" />
    <Content Include="Views\Boleto\BoletosGeradosDia.cshtml" />
    <Content Include="Views\Boleto\_PartialTotalBoletosAGerarRemessa.cshtml" />
    <Content Include="Views\Boleto\_PartialTotalBoletosGeradosDia.cshtml" />
    <Content Include="Views\Carne\Alterar.cshtml" />
    <Content Include="Views\Carne\Cadastrar.cshtml" />
    <Content Include="Views\Carne\Index.cshtml" />
    <Content Include="Views\Carne\_PartialBoletosCarne.cshtml" />
    <Content Include="Views\Carne\_PartialItensCarne.cshtml" />
    <Content Include="Views\Carne\_PartialPesquisarConsumidor.cshtml" />
    <Content Include="Views\Cliente\DetalheCliente.cshtml" />
    <Content Include="Views\Cliente\Cadastrar.cshtml" />
    <Content Include="Views\Cliente\CambioFixo.cshtml" />
    <Content Include="Views\Cliente\FormasPagamentoAceitas.cshtml" />
    <Content Include="Views\Cliente\Index.cshtml" />
    <Content Include="Views\Cliente\PartialConfiguracaoPadraoSubCliente.cshtml" />
    <Content Include="Views\Cliente\PartialDadosDoCliente.cshtml" />
    <Content Include="Views\Cliente\PartialPesquisarCliente.cshtml" />
    <Content Include="Views\Cobranca\CancelarCobrancaPorPeriodoEmLote.cshtml" />
    <Content Include="Views\Cobranca\CobrancasCanceladas.cshtml" />
    <Content Include="Views\Cobranca\CobrancasEmAberto.cshtml" />
    <Content Include="Views\Cobranca\CobrancasPagas.cshtml" />
    <Content Include="Views\Cobranca\CobrancasVencidas.cshtml" />
    <Content Include="Views\Cobranca\Detalhe.cshtml" />
    <Content Include="Views\Cobranca\DetalheInconsistencia.cshtml" />
    <Content Include="Views\Cobranca\GerarArquivoRemessa.cshtml" />
    <Content Include="Views\Cobranca\Imprimir.cshtml" />
    <Content Include="Views\Cobranca\Inconsistencias.cshtml" />
    <Content Include="Views\Cobranca\PesquisarCobrancaPorBTC.cshtml" />
    <Content Include="Views\Cobranca\PesquisarCobrancas.cshtml" />
    <Content Include="Views\Cobranca\VerBoleto.cshtml" />
    <Content Include="Views\Cobranca\_PartialAcessoNegado.cshtml" />
    <Content Include="Views\Cobranca\_PartialCobrancasabertas.cshtml" />
    <Content Include="Views\Cobranca\_PartialCobrancasCanceladas.cshtml" />
    <Content Include="Views\Cobranca\_PartialCobrancasEmAberto.cshtml" />
    <Content Include="Views\Cobranca\_PartialCobrancasPagas.cshtml" />
    <Content Include="Views\Cobranca\_PartialCobrancasVencidas.cshtml" />
    <Content Include="Views\Cobranca\_PartialDadosCartao.cshtml" />
    <Content Include="Views\Cobranca\_PartialLogCobranca.cshtml" />
    <Content Include="Views\Cobranca\_PartialPesquisarCobrancas.cshtml" />
    <Content Include="Views\Cobranca\_PartialPesquisarCobrancasPorDadosCartao.cshtml" />
    <Content Include="Views\Cobranca\_PartialTotalCobrancasGeradasDia.cshtml" />
    <Content Include="Views\Cobranca\_PartialValorTotalCobrancasGeradasDia.cshtml" />
    <Content Include="Views\Common\BoasVindas.cshtml" />
    <Content Include="Views\Common\Erro404.cshtml" />
    <Content Include="Views\Common\Erro500.cshtml" />
    <Content Include="Views\Configuracao\Alterar.cshtml" />
    <Content Include="Views\ContaBancaria\Alterar.cshtml" />
    <Content Include="Views\ContaBancaria\Cadastrar.cshtml" />
    <Content Include="Views\ContaBancaria\Index.cshtml" />
    <Content Include="Views\ControleAcesso\AlterarGrupoAcesso.cshtml" />
    <Content Include="Views\ControleAcesso\CadastrarGrupoAcesso.cshtml" />
    <Content Include="Views\ControleAcesso\DesativarGrupoAcesso.cshtml" />
    <Content Include="Views\ControleAcesso\ListarGrupoAcesso.cshtml" />
    <Content Include="Views\Empresa\AdicionarCategoria.cshtml" />
    <Content Include="Views\Empresa\Alterar.cshtml" />
    <Content Include="Views\Empresa\Cadastrar.cshtml" />
    <Content Include="Views\Empresa\GetCategoriaEmpresa.cshtml" />
    <Content Include="Views\Empresa\Index.cshtml" />
    <Content Include="Views\Empresa\RemoverCategoria.cshtml" />
    <Content Include="Views\Empresa\_PartialAlterarDadosEmpresa.cshtml" />
    <Content Include="Views\Empresa\_PartialCobrancasAbertas.cshtml" />
    <Content Include="Views\Empresa\_PartialEmpresasNaoCompletaramCadastro.cshtml" />
    <Content Include="Views\EntradaCliente\Entrada.cshtml" />
    <Content Include="Views\EntradaCliente\Inserir.cshtml" />
    <Content Include="Views\EntradaCliente\PartialEntrada.cshtml" />
    <Content Include="Views\EntradaCliente\PartialInserir.cshtml" />
    <Content Include="Views\Extrato\Index.cshtml" />
    <Content Include="Views\Financeiro\AprovarPagamento.cshtml" />
    <Content Include="Views\Financeiro\Extrato.cshtml" />
    <Content Include="Views\Financeiro\ExtratoBitcoin.cshtml" />
    <Content Include="Views\Financeiro\PartialExtrato.cshtml" />
    <Content Include="Views\Financeiro\SaldoADesbloquear.cshtml" />
    <Content Include="Views\Financeiro\SaldoBloqueado.cshtml" />
    <Content Include="Views\Financeiro\SaldoDisponivel.cshtml" />
    <Content Include="Views\Financeiro\SaldoDosClientes.cshtml" />
    <Content Include="Views\Financeiro\SaldoTodosClientes.cshtml" />
    <Content Include="Views\Financeiro\_PartialAprovarPagamento.cshtml" />
    <Content Include="Views\Financeiro\_PartialCobrancasAbertas.cshtml" />
    <Content Include="Views\GoogleAuthenticator\Cadastrar.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Login\AlterarSenha.cshtml" />
    <Content Include="Views\Login\Index.cshtml" />
    <Content Include="Views\Login\_PartialLogin.cshtml" />
    <Content Include="Views\Mensagem\Index.cshtml" />
    <Content Include="Views\Menu\_PartialMenu.cshtml" />
    <Content Include="Views\Relatorio\RelatorioClientesCadastradosPeriodo.cshtml" />
    <Content Include="Views\RelatorioSaldoAReceberNoDia\Index.cshtml" />
    <Content Include="Views\Relatorio\RelatorioSinteticoMensal.cshtml" />
    <Content Include="Views\Relatorio\RelatorioSinteticoParaEmissaoNFSe.cshtml" />
    <Content Include="Views\Relatorio\RelatorioValoresTransacionadosPeriodo.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioClientesCadastradosPeriodo.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioFaturamentoPayinPayoutBRL.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioFaturamentoPayinPayoutBTC.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioFaturamentoPayinPayoutUltimosDiasBRL.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioFaturamentoPayinPayoutUltimosDiasBTC.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoBarraBoletosPagosNaoPagos.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoBarraBoletosPagosNaoPagosCliente.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoPizzaBoletosPagosNaoPagos.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoPizzaBoletosPagosNaoPagosCliente.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioQuantidadeBoletosPagos.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioQuantidadeCobrancasPagasBTC.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioSaldoAReceber.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioTicketMedio.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioTicketMedioBitcoin.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioValoresTransacionadosPeriodo.cshtml" />
    <Content Include="Views\SaidaCliente\Inserir.cshtml" />
    <Content Include="Views\SaidaCliente\PartialInserir.cshtml" />
    <Content Include="Views\SaidaCliente\PartialSaida.cshtml" />
    <Content Include="Views\SaidaCliente\Saida.cshtml" />
    <Content Include="Views\SaqueBitcoin\PesquisarSaque.cshtml" />
    <Content Include="Views\Shared\Layout.cshtml" />
    <Content Include="Views\Shared\Login.cshtml" />
    <Content Include="Views\Shared\_LayoutInvoice.cshtml" />
    <Content Include="Views\Shared\_NewLayout.cshtml" />
    <Content Include="Views\Shared\_NewLayoutPartialHeadHTML.cshtml" />
    <Content Include="Views\Shared\_NewPartialScripts.cshtml" />
    <Content Include="Views\Shared\_PartialCobrancaBloqueadaAntiFraude.cshtml" />
    <Content Include="Views\Shared\_PartialCobrancaCancelada.cshtml" />
    <Content Include="Views\Shared\_PartialCobrancaCanceladaAntiFraude.cshtml" />
    <Content Include="Views\Shared\_PartialCobrancaPaga.cshtml" />
    <Content Include="Views\Shared\_PartialCobrancaPagaAntiFraude.cshtml" />
    <Content Include="Views\Shared\_PartialCobrancaVencida.cshtml" />
    <Content Include="Views\Shared\_PartialCobrancaVencidaAntiFraude.cshtml" />
    <Content Include="Views\Shared\_PartialFooter.cshtml" />
    <Content Include="Views\Shared\_PartialHeader.cshtml" />
    <Content Include="Views\Shared\_PartialHeadHTML.cshtml" />
    <Content Include="Views\Shared\_PartialModals.cshtml" />
    <Content Include="Views\Shared\_PartialScriptMenu.cshtml" />
    <Content Include="Views\Shared\_PartialScripts.cshtml" />
    <Content Include="Views\Tabela\AlterarCategoria.cshtml" />
    <Content Include="Views\Tabela\CadastrarCategoria.cshtml" />
    <Content Include="Views\Tabela\Categoria.cshtml" />
    <Content Include="Views\Tela\Alterar.cshtml" />
    <Content Include="Views\Tela\Cadastrodetela.cshtml" />
    <Content Include="Views\Tela\Excluirtela.cshtml" />
    <Content Include="Views\Tela\Index.cshtml" />
    <Content Include="Views\Tela\Lista.cshtml" />
    <Content Include="Views\UsuarioAdministrativo\Alterar.cshtml" />
    <Content Include="Views\UsuarioAdministrativo\Cadastrar.cshtml" />
    <Content Include="Views\UsuarioAdministrativo\Index.cshtml" />
    <Content Include="Views\Usuario\Alterar.cshtml" />
    <Content Include="Views\Usuario\Cadastrar.cshtml" />
    <Content Include="Views\Usuario\_PartialUsuarioLogadoTopo.cshtml" />
    <Content Include="Views\ValidacaoConta\Analise.cshtml" />
    <Content Include="Views\ValidacaoConta\Index.cshtml" />
    <Content Include="Views\ValidacaoConta\PartialPesquisarCliente.cshtml" />
    <Content Include="Views\WithdrawRequests\Details.cshtml" />
    <Content Include="Views\WithdrawRequests\Index.cshtml" />
    <Content Include="Views\WithdrawRequests\RequestsList.cshtml" />
    <Content Include="Views\WithdrawRequests\_PartialApprove.cshtml" />
    <Content Include="Views\WithdrawRequests\_PartialProcessado.cshtml" />
    <Content Include="Views\WithdrawRequests\_PartialReject.cshtml" />
    <Content Include="Views\WithdrawRequests\_PartialReturn.cshtml" />
    <Content Include="Views\_ViewStart.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\AutenticacaoAttribute.cs" />
    <Compile Include="Common\ManagerExtensions.cs" />
    <Compile Include="Controllers\AccessDeniedController.cs" />
    <Compile Include="Controllers\AjustesCreditoEDIController.cs" />
    <Compile Include="Controllers\AjustesDebitoEDIController.cs" />
    <Compile Include="Controllers\AprovacaoChavesPIXController.cs" />
    <Compile Include="Controllers\AprovarCobrancaController.cs" />
    <Compile Include="Controllers\AprovarCobrancaWebhookController.cs" />
    <Compile Include="Controllers\AutomacoesController.cs" />
    <Compile Include="Controllers\BaseControllers\BaseBlacklistController.cs" />
    <Compile Include="Controllers\BaseControllers\BaseChavePIXFavorecidosController.cs" />
    <Compile Include="Controllers\BaseControllers\BaseController.cs" />
    <Compile Include="Controllers\BaseControllers\BaseSemAutenticacaoController.cs" />
    <Compile Include="App_Start\Role.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Attributes\AutorizacaoAttribute.cs" />
    <Compile Include="Attributes\ExigePermissaoEscritaAttribute.cs" />
    <Compile Include="Common\ManagerGlobal.cs" />
    <Compile Include="Common\PigPagException.cs" />
    <Compile Include="Controllers\AceiteContratoLicenciamentoController.cs" />
    <Compile Include="Controllers\AgendaRecebiveisController.cs" />
    <Compile Include="Controllers\AjustesController.cs" />
    <Compile Include="Controllers\AplicacaoController.cs" />
    <Compile Include="Controllers\AtencipacaoRecebiveisController.cs" />
    <Compile Include="Controllers\BaseControllers\BaseAutorizacaoController.cs" />
    <Compile Include="Controllers\BitcoinController.cs" />
    <Compile Include="Controllers\BlacklistISPBConsultaController.cs" />
    <Compile Include="Controllers\BlacklistChavePixConsultaController.cs" />
    <Compile Include="Controllers\BlacklistChavePixController.cs" />
    <Compile Include="Controllers\BlacklistCPFCNPJConsultaController.cs" />
    <Compile Include="Controllers\BlacklistCPFCNPJController.cs" />
    <Compile Include="Controllers\BlacklistISPBController.cs" />
    <Compile Include="Controllers\BoletoController.cs" />
    <Compile Include="Controllers\BureauDataCustomerController.cs" />
    <Compile Include="Controllers\CedenteController.cs" />
    <Compile Include="Controllers\ChargebackController.cs" />
    <Compile Include="Controllers\ChavePIXFavorecidosTodasChavesController.cs" />
    <Compile Include="Controllers\ChavePIXFavorecidosAguardandoValidacaoController.cs" />
    <Compile Include="Controllers\ChavePIXController.cs" />
    <Compile Include="Controllers\ClienteSemTerminalController.cs" />
    <Compile Include="Controllers\ClienteController.cs" />
    <Compile Include="Controllers\CobrancaEstornoRelatorioController.cs" />
    <Compile Include="Controllers\CobrancaPagadorCobrancaRelatorioController.cs" />
    <Compile Include="Controllers\CobrancaPesquisarController.cs" />
    <Compile Include="Controllers\CobrancaController.cs" />
    <Compile Include="Controllers\CommonController.cs" />
    <Compile Include="Controllers\ComprovanteVendaController.cs" />
    <Compile Include="Controllers\ContaBancariaController.cs" />
    <Compile Include="Controllers\ContaBancariaEmpresasController.cs" />
    <Compile Include="Controllers\ContaBancariaFavorecidoClienteController.cs" />
    <Compile Include="Controllers\ConstanteController.cs" />
    <Compile Include="Controllers\ContatoPessoaController.cs" />
    <Compile Include="Controllers\DashboardController.cs" />
    <Compile Include="Controllers\DocumentoPessoaController.cs" />
    <Compile Include="Controllers\EnderecoPessoaController.cs" />
    <Compile Include="Controllers\EntradaClienteController.cs" />
    <Compile Include="Controllers\EnvioWebhookCobrancaController.cs" />
    <Compile Include="Controllers\EnvioWebhookSolicitacaoSaqueController.cs" />
    <Compile Include="Controllers\ErroController.cs" />
    <Compile Include="Controllers\ExtratoBancarioController.cs" />
    <Compile Include="Controllers\ExtratoContabilController.cs" />
    <Compile Include="Controllers\FormaPagamentoController.cs" />
    <Compile Include="Controllers\GerenciarAcessoPerfilController.cs" />
    <Compile Include="Controllers\GoogleAuthenticatorController.cs" />
    <Compile Include="Controllers\HealthController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\InconsistenciaController.cs" />
    <Compile Include="Controllers\IntegrationBancariaController.cs" />
    <Compile Include="Controllers\IntegrationDelbankController.cs" />
    <Compile Include="Controllers\LimiteSaqueCobrancaController.cs" />
    <Compile Include="Controllers\LoginController.cs" />
    <Compile Include="Controllers\LogSolicitacaoSaqueController.cs" />
    <Compile Include="Controllers\MensagemController.cs" />
    <Compile Include="Controllers\MonitoriaController.cs" />
    <Compile Include="Controllers\OperacaoServicoController.cs" />
    <Compile Include="Controllers\OrdemPagamentoOldController.cs" />
    <Compile Include="Controllers\PagadorCobrancaController.cs" />
    <Compile Include="Controllers\PessoaController.cs" />
    <Compile Include="Controllers\PixController.cs" />
    <Compile Include="Controllers\PixKeyMemoryCacheController.cs" />
    <Compile Include="Controllers\RelatorioSaldoAReceberNoDiaController.cs" />
    <Compile Include="Controllers\RelatorioSaldoMoedaClienteController.cs" />
    <Compile Include="Controllers\SimuladorPrecoController.cs" />
    <Compile Include="Controllers\PoliticaComercialController.cs" />
    <Compile Include="Controllers\QRCodePIXController.cs" />
    <Compile Include="Controllers\QuadroSocietarioController.cs" />
    <Compile Include="Controllers\RelatorioController.cs" />
    <Compile Include="Controllers\RepresentanteComercialController.cs" />
    <Compile Include="Controllers\RequisicoesApiController.cs" />
    <Compile Include="Controllers\SaidaClienteController.cs" />
    <Compile Include="Controllers\SaldoController.cs" />
    <Compile Include="Controllers\SolicitacaoAntecipacaoAutomaticaClientesController.cs" />
    <Compile Include="Controllers\OrdemPagamentoController.cs" />
    <Compile Include="Controllers\SolicitacaoSaquesRelatorioController.cs" />
    <Compile Include="Controllers\SolicitacaoSaquesController.cs" />
    <Compile Include="Controllers\TaxaClienteOperacaoServicoController.cs" />
    <Compile Include="Controllers\TaxaTarifaController.cs" />
    <Compile Include="Controllers\TerminalController.cs" />
    <Compile Include="Controllers\TokenCriptografiaController.cs" />
    <Compile Include="Controllers\TransacaoCartaoCobrancaController.cs" />
    <Compile Include="Controllers\TransferenciaInternaController.cs" />
    <Compile Include="Controllers\UsuarioAdministrativoController.cs" />
    <Compile Include="Controllers\UsuarioController.cs" />
    <Compile Include="Controllers\MenuController.cs" />
    <Compile Include="Controllers\ValidacaoContaController.cs" />
    <Compile Include="Controllers\WebhookFornecedorSolicitacaoSaqueController.cs" />
    <Compile Include="Controllers\WebhookNotificarClienteController.cs" />
    <Compile Include="Enums\TelaAdministrativoIdentificadorEnum.cs" />
    <Compile Include="Enums\TelaAdministrativoRegraEnum.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\AutorizacaoHelper.cs" />
    <Compile Include="Helpers\CultureHelper.cs" />
    <Compile Include="Helpers\Help.cs" />
    <Compile Include="Helpers\LinqExtensions.cs" />
    <Compile Include="Helpers\ManagerExtensions.cs" />
    <Compile Include="Helpers\PredicateBuilder.cs" />
    <Compile Include="Models\AgendaRecebiveis\PartialListarAgendaClientesModel.cs" />
    <Compile Include="Models\AgendaRecebiveis\_PartialAgendaRecebiveisClienteModel.cs" />
    <Compile Include="Models\AjusteCreditoDebito\AjusteCreditoDebitoModel.cs" />
    <Compile Include="Models\Aplicacao\_PartialAddAplicacaoModel.cs" />
    <Compile Include="Models\Aplicacao\_PartialAlterarAplicacaoModel.cs" />
    <Compile Include="Models\Aplicacao\_PartialAplicacaoClienteModel.cs" />
    <Compile Include="Models\AprovacaoChavePIX\ChavePIXModel.cs" />
    <Compile Include="Models\AtencipacaoRecebiveis\AgendaClienteModel.cs" />
    <Compile Include="Models\BureauDataCustomer\BatchStatusBureauModel.cs" />
    <Compile Include="Models\BureauDataCustomer\BureauDataModel.cs" />
    <Compile Include="Models\BureuaCustomerData\BigDataCorpMainModel.cs" />
    <Compile Include="Models\BureuaCustomerData\BureauDataCorpBaseModel.cs" />
    <Compile Include="Models\BureuaCustomerData\BureauDataCorpCsvModel.cs" />
    <Compile Include="Models\BureuaCustomerData\BureauDataCorpExcelModel.cs" />
    <Compile Include="Models\Chargeback\DetalheChargebackModel.cs" />
    <Compile Include="Models\ChavePIXContaBancariaFavorecidoClientes\ListaChavesNaoValidadasModel.cs" />
    <Compile Include="Models\ChavePIXFavorecidosTodasChaves\ChavePIXFavorecidosTodasChavesModel.cs" />
    <Compile Include="Models\Cidade\CidadeModel.cs" />
    <Compile Include="Models\Cliente\DetalheClienteModel.cs" />
    <Compile Include="Models\Cliente\PartialAdicionarBlacklistCPFCNPJClienteModel.cs" />
    <Compile Include="Models\Cliente\PartialPesquisarClienteModel.cs" />
    <Compile Include="Models\Cobranca\AbrirContestacaoCobrancaModel.cs" />
    <Compile Include="Models\Cobranca\ClienteParaCobrancaModel.cs" />
    <Compile Include="Models\Cobranca\CobrancaModel.cs" />
    <Compile Include="Models\Cobranca\DetalheCobrancaModel.cs" />
    <Compile Include="Models\Cobranca\ItemListaCobrancaModel.cs" />
    <Compile Include="Models\Cobranca\LogCobrancaModel.cs" />
    <Compile Include="Models\Cobranca\PagadorCobrancaModel.cs" />
    <Compile Include="Models\Cobranca\TransacaoCartaoCobrancaModel.cs" />
    <Compile Include="Models\Cobranca\_PartialPesquisarModel.cs" />
    <Compile Include="Models\Common\BoasVindasModel.cs" />
    <Compile Include="Models\Constante\ConstanteViewModel.cs" />
    <Compile Include="Models\ContaBancariaEmpresa\ContaBancariaEmpresaDto.cs" />
    <Compile Include="Models\ContatoPessoa\_PartialAdicionarContatoModel.cs" />
    <Compile Include="Models\ContatoPessoa\_PartialAtualizarContatoModel.cs" />
    <Compile Include="Models\ContatoPessoa\_PartialContatoPessoaClienteModel.cs" />
    <Compile Include="Models\Dashboard\StatusAtividadeCliente.cs" />
    <Compile Include="Models\Dashboard\TotaisCobrancasNoDiaModel.cs" />
    <Compile Include="Models\Dashboard\TotaisSaquesNoDiaModel.cs" />
    <Compile Include="Models\DataTables\ColumnProperty.cs" />
    <Compile Include="Models\DataTables\CustomButton.cs" />
    <Compile Include="Models\DataTables\DataTablesModel.cs" />
    <Compile Include="Models\DataTables\DataUrl.cs" />
    <Compile Include="Models\DataTables\EditType.cs" />
    <Compile Include="Models\DataTables\FilterParameter.cs" />
    <Compile Include="Models\DataTables\IRender.cs" />
    <Compile Include="Models\DataTables\PigPagButtonClassDefaults.cs" />
    <Compile Include="Models\DataTables\PigPagColumnClassDefaults.cs" />
    <Compile Include="Models\DataTables\RenderBoolean.cs" />
    <Compile Include="Models\DataTables\RenderButtonCustom.cs" />
    <Compile Include="Models\DataTables\RenderButtonEdit.cs" />
    <Compile Include="Models\DataTables\RenderButtonPrint.cs" />
    <Compile Include="Models\DataTables\RenderButtonRemove.cs" />
    <Compile Include="Models\DataTables\RenderButtonsInlineEdit.cs" />
    <Compile Include="Models\DataTables\RenderButtonSearch.cs" />
    <Compile Include="Models\DataTables\RenderButtonView.cs" />
    <Compile Include="Models\DataTables\RenderCheckBox.cs" />
    <Compile Include="Models\DataTables\RenderChildCaret.cs" />
    <Compile Include="Models\DataTables\RenderCurrency.cs" />
    <Compile Include="Models\DataTables\RenderCustom.cs" />
    <Compile Include="Models\DataTables\RenderDate.cs" />
    <Compile Include="Models\DataTables\RenderDateTime.cs" />
    <Compile Include="Models\DataTables\RenderLink.cs" />
    <Compile Include="Models\DataTables\RenderLinkRedirect.cs" />
    <Compile Include="Models\DataTables\RenderPicture.cs" />
    <Compile Include="Models\DataTable\DataTableAjaxPostModel.cs" />
    <Compile Include="Models\DocumentoPessoa\_PartialDocumentoPessoaClienteModel.cs" />
    <Compile Include="Models\EnderecoPessoa\_PartialAdicionarEnderecoModel.cs" />
    <Compile Include="Models\EnderecoPessoa\_PartialEnderecoPessoaClienteModel.cs" />
    <Compile Include="Models\ExtratoContabil\_PartialExtratoContabilBRLModel.cs" />
    <Compile Include="Models\ExtratoContabil\_PartialExtratoContabilBTCModel.cs" />
    <Compile Include="Models\FormaPagamento\_PartialAddFormaPagamentoClienteModel.cs" />
    <Compile Include="Models\FormaPagamento\_PartialCarregarPrecoPorAtividadeModel.cs" />
    <Compile Include="Models\FormaPagamento\_PartialFormaPagamentoClienteModel.cs" />
    <Compile Include="Models\FormaPagamento\LugaresAceitosModel.cs" />
    <Compile Include="Models\GoogleAuthenticator\GoogleAuthenticatorModel.cs" />
    <Compile Include="Models\Graficos\GraficoBarraModel.cs" />
    <Compile Include="Models\Graficos\GraficoLinhaModel.cs" />
    <Compile Include="Models\Graficos\GraficoPizzaModel.cs" />
    <Compile Include="Models\GrupoAcessoUsuario\SelectGrupoAcessoUsuarioReturnModel.cs" />
    <Compile Include="Models\Inconsistencia\ListarModel.cs" />
    <Compile Include="Models\Json\JSonReturnModel.cs" />
    <Compile Include="Models\Login\LembrarSenhaModel.cs" />
    <Compile Include="Models\Login\LoginModel.cs" />
    <Compile Include="Models\Log\LogSaqueModel.cs" />
    <Compile Include="Models\Mensagem\MensagemModel.cs" />
    <Compile Include="Models\MenuModel.cs" />
    <Compile Include="Models\Moeda\MoedaModel.cs" />
    <Compile Include="Models\OperacaoServico\_PartialOperacaoServicoClienteModel.cs" />
    <Compile Include="Models\OperacaoServico\_PartialTaxaAntecipacaoOperacaoServicoModel.cs" />
    <Compile Include="Models\OrdemPagamento\AprovarOrdemPagamentoModel.cs" />
    <Compile Include="Models\OrdemPagamento\RecusarOrdemPagamentoModel.cs" />
    <Compile Include="Models\OrdemPagamento\GerarAquivoRemessaOrdemPagamentoModel.cs" />
    <Compile Include="Models\OrdemPagamento\ListarArquivoOrdemPagamentoModel.cs" />
    <Compile Include="Models\OrdemPagamento\ListarOrdensPagamentoASeremExecutadasModel.cs" />
    <Compile Include="Models\Pais\PaisModel.cs" />
    <Compile Include="Models\Pessoa\_PartialAlterarPessoaFisicaModel.cs" />
    <Compile Include="Models\Pessoa\_PartialAlterarPessoaJuridicaModel.cs" />
    <Compile Include="Models\PixKeyMemoryCache\PixKeyMemoryCacheModel.cs" />
    <Compile Include="Models\PixKeyMemoryCache\ResultPixInfoModel.cs" />
    <Compile Include="Models\Pix\PesquisarQRCodePixModel.cs" />
    <Compile Include="Models\PoliticaComercial\PoliticaComercialAddModel.cs" />
    <Compile Include="Models\PoliticaComercial\PoliticaComercialModel.cs" />
    <Compile Include="Models\PoliticaComercial\SimularPrecificacaoModel.cs" />
    <Compile Include="Models\PoliticaComercial\_PartialCarregarPoliticaPrecoCNAEModel.cs" />
    <Compile Include="Models\QuadroSocietario\_PartialQuadroSocietarioClienteModel.cs" />
    <Compile Include="Models\Relatorio\ClientePorDataCadastroReturnModel.cs" />
    <Compile Include="Models\Relatorio\RelatorioSaldoAReceberModel.cs" />
    <Compile Include="Models\Relatorio\RelatorioSinteticoMensalModel.cs" />
    <Compile Include="Models\Relatorio\RelatorioSinteticoParaEmissaoNFSeModel.cs" />
    <Compile Include="Models\Relatorio\RelatorioValoresTransacionadosModel.cs" />
    <Compile Include="Models\Relatorio\SaldoMoedaClienteModel.cs" />
    <Compile Include="Models\Relatorio\VendasNegadasDiaModel.cs" />
    <Compile Include="Models\SaidaCliente\_PartialAddSaidaClienteModel.cs" />
    <Compile Include="Models\SegmentoMCC\SegmentoMCCModel.cs" />
    <Compile Include="Models\SolicitacaoSaque\DetalheModel.cs" />
    <Compile Include="Models\SolicitacaoSaque\ListarSolicitacaoSaqueModel.cs" />
    <Compile Include="Models\SolicitacaoSaque\SolicitacaoSaqueModel.cs" />
    <Compile Include="Models\TaxaClienteOperacaoServico\_PartialConfigurarTaxaOperacaoServicoFormaPagamentoModel.cs" />
    <Compile Include="Models\TaxaTarifa\_PartialTaxaTarifaClienteModel.cs" />
    <Compile Include="Models\TecnologiaWeb\TecnologiaWebModel.cs" />
    <Compile Include="Models\Terminal\_PartialAddTerminalModel.cs" />
    <Compile Include="Models\Terminal\_PartialAlterarTerminalModel.cs" />
    <Compile Include="Models\Terminal\_PartialListarTerminaisClienteModel.cs" />
    <Compile Include="Models\TipoAplicacao\TipoAplicacaoModel.cs" />
    <Compile Include="Models\TipoContato\TipoContatoModel.cs" />
    <Compile Include="Models\TipoDocumento\TipoDocumentoModel.cs" />
    <Compile Include="Models\TipoEndereco\TipoEnderecoModel.cs" />
    <Compile Include="Models\TipoPessoa\TipoPessoaModel.cs" />
    <Compile Include="Models\TransferenciaInterna\FazerTransferenciaEntreContasModel.cs" />
    <Compile Include="Models\UF\UFModel.cs" />
    <Compile Include="Models\UsuarioAdministrativo\AlterarModel.cs" />
    <Compile Include="Models\UsuarioAdministrativo\CadastrarModel.cs" />
    <Compile Include="Models\UsuarioAdministrativo\IndexModel.cs" />
    <Compile Include="Models\UsuarioAdministrativo\SelecaoOperador.cs" />
    <Compile Include="Models\UsuarioAdministrativo\UsuarioAdministrativoModel.cs" />
    <Compile Include="Models\Usuario\UsuarioModel.cs" />
    <Compile Include="Models\Usuario\_PartialListarUsuariosCliente.cs" />
    <Compile Include="Models\ValidacaoConta\AnaliseModel.cs" />
    <Compile Include="Models\ValidacaoConta\ValidacaoContaModel.cs" />
    <Compile Include="Models\WebhookProgressInfo.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ServiceScopeModule.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\web.config" />
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
    <Content Include="plugins\ckeditor\CHANGES.md" />
    <Content Include="plugins\ckeditor\LICENSE.md" />
    <Content Include="plugins\ckeditor\plugins\scayt\LICENSE.md" />
    <Content Include="plugins\ckeditor\plugins\scayt\README.md" />
    <Content Include="plugins\ckeditor\plugins\wsc\LICENSE.md" />
    <Content Include="plugins\ckeditor\plugins\wsc\README.md" />
    <Content Include="plugins\ckeditor\README.md" />
    <Content Include="plugins\ckeditor\skins\moono\readme.md" />
    <Content Include="plugins\input-mask\phone-codes\phone-be.json" />
    <Content Include="plugins\input-mask\phone-codes\phone-codes.json" />
    <Content Include="scripts\jquery-1.10.2.min.map" />
    <Content Include="scripts\jquery-3.0.0.min.map" />
    <Content Include="scripts\jquery-3.0.0.slim.min.map" />
    <Content Include="Views\Common\_PartialUsuarioLogadoTopo.cshtml" />
    <Content Include="Views\Pessoa\_PartialAlterarPessoaFisica.cshtml" />
    <Content Include="Views\Pessoa\_PartialAlterarPessoaJuridica.cshtml" />
    <Content Include="Views\FormaPagamento\_PartialFormaPagamentoCliente.cshtml" />
    <Content Include="Views\FormaPagamento\_PartialAddFormaPagamentoCliente.cshtml" />
    <Content Include="Views\TaxaClienteOperacaoServico\_PartialConfigurarTaxaOperacaoServicoFormaPagamento.cshtml" />
    <Content Include="Views\Mensagem\_PartialIndex.cshtml" />
    <Content Include="Views\AgendaRecebiveis\_PartialAgendaRecebiveisCliente.cshtml" />
    <Content Include="Views\ExtratoContabil\_PartialExtratoContabilClienteBRL.cshtml" />
    <Content Include="Views\ExtratoContabil\_PartialExtratoContabilClienteBTC.cshtml" />
    <Content Include="Views\QuadroSocietario\_PartialQuadroSocietarioCliente.cshtml" />
    <Content Include="Views\Aplicacao\_PartialAplicacaoCliente.cshtml" />
    <Content Include="Views\Aplicacao\_PartialAddAplicacao.cshtml" />
    <Content Include="Views\Aplicacao\_PartialAlterarAplicacao.cshtml" />
    <Content Include="Views\EnderecoPessoa\_PartialEnderecoPessoaCliente.cshtml" />
    <Content Include="Views\DocumentoPessoa\_PartialDocumentoPessoaCliente.cshtml" />
    <Content Include="Views\ContatoPessoa\_PartialContatoPessoaCliente.cshtml" />
    <Content Include="Views\ContatoPessoa\_PartialAdicionarContato.cshtml" />
    <Content Include="Views\EnderecoPessoa\_PartialAdicionarEndereco.cshtml" />
    <Content Include="Views\ContatoPessoa\_PartialAtualizarContato.cshtml" />
    <Content Include="Views\Saldo\_PartialSaldoDisponivelReal.cshtml" />
    <Content Include="Views\Saldo\_PartialSaldoAReceberReal.cshtml" />
    <Content Include="Views\Saldo\_PartialSaldoAReceberDiaSeguinteReal.cshtml" />
    <Content Include="Views\Saldo\_PartialSaldoAReceberProximos7DiasReal.cshtml" />
    <Content Include="Views\Saldo\_PartialSaldoDisponivelBitcoin.cshtml" />
    <Content Include="Views\SaidaCliente\_PartialAddSaidaCliente.cshtml" />
    <Content Include="Views\AgendaRecebiveis\_PartialAgendaRecebiveisClienteBTC.cshtml" />
    <Content Include="Views\Saldo\_PartialSaldoAReceberBitcoin.cshtml" />
    <Content Include="Views\OperacaoServico\_PartialOperacaoServicoCliente.cshtml" />
    <Content Include="Views\Inconsistencia\Listar.cshtml" />
    <Content Include="Views\PoliticaComercial\Index.cshtml" />
    <Content Include="Views\PoliticaComercial\_PartialPesquisarCliente.cshtml" />
    <Content Include="Views\PoliticaComercial\Add.cshtml" />
    <Content Include="Views\PoliticaComercial\_PartialCarregarPoliticaPrecoCNAE.cshtml" />
    <Content Include="Views\Terminal\_PartialListarTerminaisCliente.cshtml" />
    <Content Include="Views\Terminal\_PartialAddTerminal.cshtml" />
    <Content Include="Views\Terminal\_PartialAlterarTerminal.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoBarraCobrancasPagasNaoPagas.cshtml" />
    <Content Include="Views\Erro\Index.cshtml" />
    <Content Include="Views\DocumentoPessoa\_PartialListaArquivosDocumentosPessoaCliente.cshtml" />
    <Content Include="Views\Usuario\_PartialListarUsuariosCliente.cshtml" />
    <Content Include="Views\Common\_PartialNotificacoesTopo.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoLinhaVolumeBoletosGerados.cshtml" />
    <Content Include="Views\AgendaRecebiveis\PesquisaClienteAgenda.cshtml" />
    <Content Include="Views\Common\_PartialSaldoDisponivelReal.cshtml" />
    <Content Include="Views\Common\_PartialSaldoDisponivelBitcoin.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoLinhaVolumeFaturadoBRL.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoLinhaVolumeFaturadoBTC.cshtml" />
    <Content Include="Views\EntradaCliente\_PartialAddEntradaCliente.cshtml" />
    <Content Include="Views\OperacaoServico\_PartialTaxaAntecipacaoOperacaoServico.cshtml" />
    <Content Include="Views\SolicitacaoAntecipacaoAutomaticaClientes\Create.cshtml" />
    <Content Include="Views\SolicitacaoAntecipacaoAutomaticaClientes\Delete.cshtml" />
    <Content Include="Views\SolicitacaoAntecipacaoAutomaticaClientes\Details.cshtml" />
    <Content Include="Views\SolicitacaoAntecipacaoAutomaticaClientes\Edit.cshtml" />
    <Content Include="Views\SolicitacaoAntecipacaoAutomaticaClientes\Index.cshtml" />
    <Content Include="Views\Relatorio\_PartialQuantidadeCobrancasGeradasDia.cshtml" />
    <Content Include="Views\Relatorio\_PartialTotalCobrancasGeradasDia.cshtml" />
    <Content Include="Views\Relatorio\_PartialQuantidadeBoletosGeradasDia.cshtml" />
    <Content Include="Views\Relatorio\_PartialQuantidadeTransacaoCartaoGeradasDia.cshtml" />
    <Content Include="Views\Relatorio\_PartialQuantidadeTransacaoCartaoNegadasDia.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\ClientesComAgendaDisponivel.cshtml" />
    <Content Include="Views\AntecipacaoRecebiveis\AgendaCliente.cshtml" />
    <Content Include="Views\SolicitacaoSaques\Create.cshtml" />
    <Content Include="Views\SolicitacaoSaques\Delete.cshtml" />
    <Content Include="Views\SolicitacaoSaques\Details.cshtml" />
    <Content Include="Views\SolicitacaoSaques\Edit.cshtml" />
    <Content Include="Views\SolicitacaoSaques\Index.cshtml" />
    <Content Include="Views\SolicitacaoSaques\_PartialApprove.cshtml" />
    <Content Include="Views\SolicitacaoSaques\_PartialProcessado.cshtml" />
    <Content Include="Views\SolicitacaoSaques\_PartialReject.cshtml" />
    <Content Include="Views\SolicitacaoSaques\_PartialReturn.cshtml" />
    <Content Include="Views\RepresentanteComercial\_PartialRepresentanteComercialCliente.cshtml" />
    <Content Include="Views\Cliente\_PartialTipoCliente.cshtml" />
    <Content Include="Views\ClienteSemTerminal\Index.cshtml" />
    <Content Include="Views\TransacaoCartaoCobranca\Lista.cshtml" />
    <Content Include="Views\Shared\Table.cshtml" />
    <Content Include="Views\Shared\_Table.Definition.cshtml" />
    <Content Include="Views\Shared\_GridLocalization.cshtml" />
    <Content Include="Views\Inconsistencia\Index.cshtml" />
    <Content Include="Views\Boleto\ListaBoletosDia.cshtml" />
    <Content Include="Views\Chargeback\Index.cshtml" />
    <Content Include="Views\Chargeback\Detalhe.cshtml" />
    <Content Include="Views\AjustesCreditoEDI\Index.cshtml" />
    <Content Include="Views\AjustesDebitoEDI\Index.cshtml" />
    <Content Include="Views\ExtratoBancario\ImportarExtrato.cshtml" />
    <Content Include="Views\ExtratoBancario\ListaExtrato.cshtml" />
    <Content Include="Views\Shared\_PartialCobrancaContestada.cshtml" />
    <Content Include="Views\TransacaoCartaoCobranca\ListaNaoAutorizadas.cshtml" />
    <Content Include="Views\Cliente\_PartialNicknameCliente.cshtml" />
    <Content Include="Views\Cliente\_PartialSenhaSegurancaCliente.cshtml" />
    <Content Include="Views\SolicitacaoSaques\_PartialPayPIX.cshtml" />
    <Content Include="Views\SolicitacaoSaques\_PartialPayTED.cshtml" />
    <Content Include="Views\SolicitacaoSaques\_PartialListarSolicitacaoSaque.cshtml" />
    <Content Include="Views\DocumentoPessoa\EditarDocumento.cshtml" />
    <Content Include="Views\ComprovanteVenda\ListaComprovanteVendaEDI.cshtml" />
    <Content Include="Views\AgendaRecebiveis\ListarAgendaClientes.cshtml" />
    <Content Include="Views\AgendaRecebiveis\PartialListarAgendaClientes.cshtml" />
    <Content Include="Views\RelatorioSaldoMoedaCliente\Index.cshtml" />
    <Content Include="Views\ContaBancaria\ListaContasBancariasCliente.cshtml" />
    <Content Include="Views\Cedente\ListaCedenteConta.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoBarraPIXPagosNaoPagos.cshtml" />
    <Content Include="Views\Relatorio\_PartialSomatorioArrecadadoTaxaTarifaNoDia.cshtml" />
    <Content Include="Views\QRCodePIX\_PartialListarQRCodesCobranca.cshtml" />
    <Content Include="Views\QRCodePIX\ListaQRCodesCobranca.cshtml" />
    <Content Include="Views\Shared\_PartialMensage.cshtml" />
    <Content Include="Views\ContaBancariaFavorecidoCliente\_PartialContaBancariaFavorecidoClientePorId.cshtml" />
    <Content Include="Views\Relatorio\_PartialRelatorioGraficoLinhaTicketMedioBRL.cshtml" />
    <Content Include="Views\Relatorio\_PartialQuantidadeQRcodePIXGeradosDia.cshtml" />
    <Content Include="Views\Monitoria\Index.cshtml" />
    <Content Include="Views\Monitoria\_PartialQuantidadeWebhookPIXAguardandoProcessamentoDia.cshtml" />
    <Content Include="Views\Monitoria\_PartialQuantidadeWebhookPIXProcessadosDia.cshtml" />
    <Content Include="Views\Monitoria\_PartialStatusCobranca.cshtml" />
    <Content Include="Views\EnvioWebhookSolicitacaoSaque\_PartialWebhookSolicitacaoSaque.cshtml" />
    <Content Include="Views\EnvioWebhookCobranca\_PartialWebhookCobranca.cshtml" />
    <Content Include="Views\OrdemPagamento\_PartialApprove.cshtml" />
    <Content Include="Views\OrdemPagamento\_PartialListarOrdemPagamento.cshtml" />
    <Content Include="Views\OrdemPagamento\_PartialReject.cshtml" />
    <Content Include="Views\OrdemPagamento\_PartialReturn.cshtml" />
    <Content Include="Views\OrdemPagamento\Details.cshtml" />
    <Content Include="Views\OrdemPagamento\Edit.cshtml" />
    <Content Include="Views\OrdemPagamento\Index.cshtml" />
    <Content Include="Views\ChavePIXFavorecidosAguardandoValidacao\Create.cshtml" />
    <Content Include="Views\ChavePIXFavorecidosAguardandoValidacao\Delete.cshtml" />
    <Content Include="Views\ChavePIXFavorecidosAguardandoValidacao\Details.cshtml" />
    <Content Include="Views\ChavePIXFavorecidosAguardandoValidacao\Edit.cshtml" />
    <Content Include="Views\ChavePIXFavorecidosAguardandoValidacao\Index.cshtml" />
    <Content Include="Views\WebhookFornecedorSolicitacaoSaque\_PartialListarWebhookSaque.cshtml" />
    <Content Include="Views\SolicitacaoSaques\ListaSaquesAguardandoConclusao.cshtml" />
    <Content Include="Views\LogSolicitacaoSaque\_PartialLogSolicitacaoSaque.cshtml" />
    <Content Include="Views\SolicitacaoSaques\_PartialListaSolicitacaoPorIdContaBancariaFavorecido.cshtml" />
    <Content Include="Views\OrdemPagamento\_PartialCancel.cshtml" />
    <Content Include="Views\ContaBancariaEmpresas\Create.cshtml" />
    <Content Include="Views\ContaBancariaEmpresas\Delete.cshtml" />
    <Content Include="Views\ContaBancariaEmpresas\Details.cshtml" />
    <Content Include="Views\ContaBancariaEmpresas\Edit.cshtml" />
    <Content Include="Views\ContaBancariaEmpresas\Index.cshtml" />
    <Content Include="Views\AceiteContratoLicenciamento\_PartialContratoAssinado.cshtml" />
    <Content Include="Views\Dashboard\Index.cshtml" />
    <Content Include="Views\Dashboard\TotaisSaquesNoDia.cshtml" />
    <Content Include="Views\Dashboard\TotaisCobrancasNoDia.cshtml" />
    <Content Include="Views\AprovarCobranca\Index.cshtml" />
    <Content Include="Views\AprovarCobrancaWebhook\Index.cshtml" />
    <Content Include="Views\Dashboard\PizzaStatusSaquesDia.cshtml" />
    <Content Include="Views\QRCodePIX\ListaQRCodesCobrancaRelatorio.cshtml" />
    <Content Include="Views\ContaBancariaEmpresas\PartialGerenciarPixContasBancarias.cshtml" />
    <Content Include="Views\SolicitacaoSaques\ImprimirComprovante.cshtml" />
    <Content Include="Views\Constante\Index.cshtml" />
    <Content Include="Views\Constante\Create.cshtml" />
    <Content Include="Views\Constante\Edit.cshtml" />
    <Content Include="Views\Constante\Delete.cshtml" />
    <Content Include="Views\Cobranca\PagadorCobranca.cshtml" />
    <Content Include="Views\SolicitacaoSaquesRelatorio\Index.cshtml" />
    <Content Include="Views\RequisicoesApi\ListaRequisicoesApi.cshtml" />
    <Content Include="Views\BlacklistConsulta\Index.cshtml" />
    <Content Include="Views\ExtratoContabil\ExtratoContabilClienteRelatorio.cshtml" />
    <Content Include="Views\Cobranca\CobrancaEstornoRelatorio.cshtml" />
    <Content Include="Views\ContaBancariaEmpresas\PartialListarConfiguracoesGerenciarPixContasBancarias.cshtml" />
    <Content Include="Views\Cliente\Add.cshtml" />
    <Content Include="Views\GerenciarAcessoPerfil\Index.cshtml" />
    <Content Include="Views\GerenciarAcessoPerfil\SelecionarOperador.cshtml" />
    <Content Include="Views\GerenciarAcessoPerfil\_ListarTelasPorPerfil.cshtml" />
    <Content Include="Views\Cobranca\_PartialLogCobrancaLista.cshtml" />
    <Content Include="Views\Dashboard\_PartialDashboard.cshtml" />
    <Content Include="Views\Blacklist\Index.cshtml" />
    <Content Include="Views\Blacklist\_PartialListaBloqueios.cshtml" />
    <Content Include="Views\ChavePIXFavorecidosTodasChaves\Index.cshtml" />
    <Content Include="Views\ChavePIXFavorecidosTodasChaves\Edit.cshtml" />
    <Content Include="Views\Blacklist\_PartialIndex.cshtml" />
    <Content Include="Views\AccessDenied\Index.cshtml" />
    <Content Include="Views\SimuladorPreco\Index.cshtml" />
    <Content Include="Views\CobrancaPesquisar\_PartialPesquisar.cshtml" />
    <Content Include="Views\CobrancaPesquisar\Detalhe.cshtml" />
    <Content Include="Views\CobrancaPesquisar\Index.cshtml" />
    <Content Include="Views\CobrancaPesquisar\VerBoleto.cshtml" />
    <Content Include="Views\CobrancaPesquisar\_PartialLogCobranca.cshtml" />
    <Content Include="Views\AjustesDebitoEDI\Detalhe.cshtml" />
    <Content Include="Views\CobrancaPagadorCobrancaRelatorio\Index.cshtml" />
    <Content Include="Views\CobrancaEstornoRelatorio\Index.cshtml" />
    <Content Include="Views\OperacaoServico\Index.cshtml" />
    <Content Include="Views\OperacaoServico\Edit.cshtml" />
    <Content Include="Views\OperacaoServico\Create.cshtml" />
    <Content Include="Views\Cliente\_PartialListarChavePIX.cshtml" />
    <Content Include="Views\Pix\PesquisarPagamento.cshtml" />
    <Content Include="Views\Pix\_PartialPesquisarQRCode.cshtml" />
    <Content Include="Views\WebhookNotificarCliente\Index.cshtml" />
    <Content Include="Views\TransferenciaInterna\FazerTransferenciaEntreContas.cshtml" />
    <Content Include="Views\Cobranca\ReenviarWebhookEmLote.cshtml" />
    <Content Include="Views\TokenCriptografia\_PartialTokenCriptografia.cshtml" />
    <Content Include="Views\Cliente\_PartialAlterarCNAEPrincipal.cshtml" />
    <Content Include="Views\ContaBancaria\GetExtratoConta.cshtml" />
    <Content Include="Views\EnderecoPessoa\_PartialAlterarEndereco.cshtml" />
    <Content Include="Views\SolicitacaoSaques\ReenviarWebhookEmLote.cshtml" />
    <Content Include="Views\Login\LembrarSenha.cshtml" />
    <Content Include="Views\Login\CriarNovaSenha.cshtml" />
    <Content Include="Views\Automacoes\BalanceamentoDeContas.cshtml" />
    <Content Include="Views\Automacoes\_PartialAcessoNegado.cshtml" />
    <Content Include="Views\LimiteSaqueCobranca\Cadastro.cshtml" />
    <Content Include="Views\LimiteSaqueCobranca\Index.cshtml" />
    <Content Include="Views\LimiteSaqueCobranca\PartialListarConfiguracoes.cshtml" />
    <Content Include="Views\LimiteSaqueCobranca\SelecionarOperador.cshtml" />
    <Content Include="Views\LimiteSaqueCobranca\PartialGerenciarPix.cshtml" />
    <Content Include="Views\LimiteSaqueCobranca\_Forms.cshtml" />
    <Content Include="Views\AprovacaoChavesPIX\Index.cshtml" />
    <Content Include="Views\AprovacaoChavesPIX\_PartialPesquisar.cshtml" />
    <Content Include="Views\BureauDataCustomer\Index.cshtml" />
    <Content Include="Views\BureauDataCustomer\_PartialGetStatus.cshtml" />
    <Content Include="Views\PixKeyMemoryCache\Index.cshtml" />
    <Content Include="Views\PixKeyMemoryCache\_PartialPesquisar.cshtml" />
    <Content Include="Views\IntegrationBancaria\Index.cshtml" />
    <Content Include="Views\IntegrationDelbank\Index.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="App_LocalResources\" />
    <Folder Include="Models\FormasPagamentoCobranca\" />
    <Folder Include="plugins\bower_components\node_modules\" />
    <Folder Include="Views\Ajustes\" />
    <Folder Include="Views\AtencipacaoRecebiveis\" />
    <Folder Include="Views\BaseSemAutenticacao\" />
    <Folder Include="Views\Fornecedor\" />
    <Folder Include="Views\Health\" />
    <Folder Include="Views\PagadorCobranca\" />
    <Folder Include="Views\Pagamento\" />
    <Folder Include="Views\TaxaTarifa\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\MultiPay.DomainService.Mongo\MultiPay.DomainService.Mongo.csproj">
      <Project>{5f87541a-97d3-4784-8082-ce6e07fdcf39}</Project>
      <Name>MultiPay.DomainService.Mongo</Name>
    </ProjectReference>
    <ProjectReference Include="..\MultiPay.PIX\MultiPay.PIX.csproj">
      <Project>{4762aaa6-007c-461b-9182-e0556e49158a}</Project>
      <Name>MultiPay.PIX</Name>
    </ProjectReference>
    <ProjectReference Include="..\MultiPay.Plugin.BancoAarin\MultiPay.Plugin.BancoAarin.csproj">
      <Project>{390449dc-bbbb-4167-abd8-df4377480cec}</Project>
      <Name>MultiPay.Plugin.BancoAarin</Name>
    </ProjectReference>
    <ProjectReference Include="..\MultiPay.Plugin.BancoDelbank\MultiPay.Plugin.BancoDelbank.csproj">
      <Project>{e492832c-ebbe-4bf0-a3ad-06966ee53e65}</Project>
      <Name>MultiPay.Plugin.BancoDelbank</Name>
    </ProjectReference>
    <ProjectReference Include="..\MultiPay.Plugin.BancoDock\MultiPay.Plugin.BancoDock.csproj">
      <Project>{52c61c30-4e17-4601-8895-707d68dae566}</Project>
      <Name>MultiPay.Plugin.BancoDock</Name>
    </ProjectReference>
    <ProjectReference Include="..\MultiPay.Plugin.BancoGenial\MultiPay.Plugin.BancoGenial.csproj">
      <Project>{ccb363e1-328c-45cf-b1a2-6cf03824e357}</Project>
      <Name>MultiPay.Plugin.BancoGenial</Name>
    </ProjectReference>
    <ProjectReference Include="..\MultiPay.Plugin.BancoSicoob\MultiPay.Plugin.BancoSicoob.csproj">
      <Project>{c5f5781d-3c13-4a9b-b543-3f76a26bea63}</Project>
      <Name>MultiPay.Plugin.BancoSicoob</Name>
    </ProjectReference>
    <ProjectReference Include="..\MultiPay.Plugin.Core\MultiPay.Plugin.Core.csproj">
      <Project>{8a0435ef-a5ac-4401-9d6a-2570d9235f21}</Project>
      <Name>MultiPay.Plugin.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\MultiPay.Plugin.RabbitMqBase\MultiPay.Plugin.RabbitMqBase.csproj">
      <Project>{0d69e762-8951-474d-b6a9-7f466f940651}</Project>
      <Name>MultiPay.Plugin.RabbitMqBase</Name>
    </ProjectReference>
    <ProjectReference Include="..\MultiPay.Plugin.SharedServices\MultiPay.Plugin.SharedServices.csproj">
      <Project>{246D1E7E-978C-491A-9E3F-79DB08FF0F3B}</Project>
      <Name>MultiPay.Plugin.SharedServices</Name>
    </ProjectReference>
    <ProjectReference Include="..\Multipay.Service.Criptography\Multipay.Service.Criptography.csproj">
      <Project>{********-a95a-4d1f-8832-7bf7f704a8cc}</Project>
      <Name>Multipay.Service.Criptography</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Accounting\PigPag.Accounting.csproj">
      <Project>{2420D81F-4C7E-4EC0-8D26-2D085FA20EC9}</Project>
      <Name>PigPag.Accounting</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.BitcoinCore\PigPag.BitcoinCore.csproj">
      <Project>{31b5080e-34e4-4e99-a350-8a49ebf98c46}</Project>
      <Name>PigPag.BitcoinCore</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Common\PigPag.Common.csproj">
      <Project>{c4e472e9-6a16-4694-b28d-4642c824ee8b}</Project>
      <Name>PigPag.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Constants\PigPag.Constants.csproj">
      <Project>{76c3364e-874c-47a7-bc52-515736d70462}</Project>
      <Name>PigPag.Constants</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.CreditCard\PigPag.CreditCard.csproj">
      <Project>{E3305E35-009F-4E9F-A056-D46FF2DD68F0}</Project>
      <Name>PigPag.CreditCard</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.DataModel\PigPag.DataModel.csproj">
      <Project>{5df255c9-dcee-48e8-9f15-c893d548584d}</Project>
      <Name>PigPag.DataModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.DBDomain\PigPag.DBDomain.csproj">
      <Project>{5d5da67a-cd75-410d-a9ee-271868f602fd}</Project>
      <Name>PigPag.DBDomain</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Domain.Manager\PigPag.Domain.Manager.csproj">
      <Project>{7cccda51-73fc-4bcd-929b-fdba2a7c9ce9}</Project>
      <Name>PigPag.Domain.Manager</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.DomainService.Connection\PigPag.DomainService.Connection.csproj">
      <Project>{f7075b0a-5a64-42b6-b471-5e793ff43e7c}</Project>
      <Name>PigPag.DomainService.Connection</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.DomainService.Manager\PigPag.DomainService.Manager.csproj">
      <Project>{7FA1C145-F333-4FE9-A5B6-71386A5C8870}</Project>
      <Name>PigPag.DomainService.Manager</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Email\PigPag.Email.csproj">
      <Project>{84f33bf6-d705-4cb5-ad0e-2265417e77c7}</Project>
      <Name>PigPag.Email</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Plugin.BMP\PigPag.Plugin.BMP.csproj">
      <Project>{6489ba33-00b1-4416-8123-858d229f5cd8}</Project>
      <Name>PigPag.Plugin.BMP</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Plugin.BS2\PigPag.Plugin.BS2.csproj">
      <Project>{28ac4c1d-cfd4-4f48-9ec1-c334d32b281f}</Project>
      <Name>PigPag.Plugin.BS2</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Plugin.CERC\PigPag.Plugin.CERC.csproj">
      <Project>{4eb36e6d-baa6-4fa1-b0dd-6ea88cd35267}</Project>
      <Name>PigPag.Plugin.CERC</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Resources\PigPag.Resources.csproj">
      <Project>{c1629376-738f-4266-8d76-a53d26c7d13b}</Project>
      <Name>PigPag.Resources</Name>
    </ProjectReference>
    <ProjectReference Include="..\PigPag.Security\PigPag.Security.csproj">
      <Project>{b9ae4f3b-cc7a-4a17-879f-7dc7e75982f2}</Project>
      <Name>PigPag.Security</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\AWSSDK.SecretsManager.3.7.302.49\analyzers\dotnet\cs\AWSSDK.SecretsManager.CodeAnalysis.dll" />
    <Analyzer Include="..\packages\AWSSDK.SecretsManager.3.7.302.49\analyzers\dotnet\cs\SharedAnalysisCode.dll" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>51153</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:15364/</IISUrl>
          <OverrideIISAppRootUrl>True</OverrideIISAppRootUrl>
          <IISAppRootUrl>http://localhost:15364/</IISAppRootUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.Data.SqlClient.SNI.5.1.1\build\net462\Microsoft.Data.SqlClient.SNI.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Data.SqlClient.SNI.5.1.1\build\net462\Microsoft.Data.SqlClient.SNI.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.19.6\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.19.6\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.19.6\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.19.6\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
  </Target>
  <Target Name="CopyRoslynFiles" AfterTargets="AfterBuild" Condition="!$(Disable_CopyWebApplication) And '$(OutDir)' != '$(OutputPath)'">
    <ItemGroup>
      <RoslynFiles Include="$(CscToolPath)\*" />
    </ItemGroup>
    <MakeDir Directories="$(WebProjectOutputDir)\bin\roslyn" />
    <Copy SourceFiles="@(RoslynFiles)" DestinationFolder="$(WebProjectOutputDir)\bin\roslyn" SkipUnchangedFiles="true" Retries="$(CopyRetryCount)" RetryDelayMilliseconds="$(CopyRetryDelayMilliseconds)" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <Import Project="..\packages\Microsoft.Data.SqlClient.SNI.5.1.1\build\net462\Microsoft.Data.SqlClient.SNI.targets" Condition="Exists('..\packages\Microsoft.Data.SqlClient.SNI.5.1.1\build\net462\Microsoft.Data.SqlClient.SNI.targets')" />
  <Import Project="..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.19.6\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets" Condition="Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.19.6\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets')" />
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>