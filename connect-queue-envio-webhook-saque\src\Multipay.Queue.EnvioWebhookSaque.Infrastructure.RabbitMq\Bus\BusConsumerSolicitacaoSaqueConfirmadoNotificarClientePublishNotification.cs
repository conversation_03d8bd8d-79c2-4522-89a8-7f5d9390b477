﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Events;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Settings;
using Multipay.RabbitMQExtension.Bus;
using Multipay.RabbitMQExtension.Connections;

namespace Multipay.Queue.Webhook.Infrastructure.RabbitMq.Bus
{
    internal class BusConsumerSolicitacaoSaqueConfirmadoNotificarClientePublishNotification :
        EventBusConsumerRabbitMQ<SaqueConfirmadoNotificarClienteEvent>,
        IBusConsumerSolicitacaoSaqueConfirmadoNotificarClientePublishNotification
    {
        public BusConsumerSolicitacaoSaqueConfirmadoNotificarClientePublishNotification(
            IRabbitMQPersistentConnection persistentConnection,
            IOptions<RabbitMqSettings> options,
            ILogger<BusConsumerSolicitacaoSaqueConfirmadoNotificarClientePublishNotification> logger)
            : base(persistentConnection, logger, options.Value.NotificarSaqueClientePublishNotification)
        {
        }
    }
}