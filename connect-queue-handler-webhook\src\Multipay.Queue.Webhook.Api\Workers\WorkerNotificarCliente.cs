﻿using Multipay.Queue.NotificarCliente.Application.Services;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Api.Workers
{
    public class WorkerNotificarCliente : BackgroundConsumerService<NotificarClienteEvent>
    {
        private readonly IServiceProvider _serviceProvider;

        public WorkerNotificarCliente(
            ILogger<WorkerNotificarCliente> logger,
            IServiceProvider serviceProvider,
            IBusConsumerNotificarCliente bus) : base(logger, bus)
        {
            _serviceProvider = serviceProvider;
        }

        protected override async Task<bool> ProcessEventHandler(string mensagem, NotificarClienteEvent @event, bool _)
        {
            using IServiceScope scope = _serviceProvider.CreateScope();

            try
            {
                var service = scope.ServiceProvider.GetRequiredService<INotiticarClienteService>();
                return await service.EnviarNotificacao(@event!);
            }
            finally
            {
                var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                unitOfWork.Dispose();

                var logsService = scope.ServiceProvider.GetRequiredService<ICentralizadorLogsService>();
                logsService.Salvar();
            }
        }
    }
}