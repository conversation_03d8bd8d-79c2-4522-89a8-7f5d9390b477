﻿using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Repositories.MongoRepositories
{
    public interface IBaseRepository<TDocument> where TDocument : class
    {
        void InsertMany(IEnumerable<TDocument> items);
    }

    public class BaseRepository<TDocument> : IBaseRepository<TDocument> where TDocument : class
    {
        private readonly ILogger logger;
        protected readonly IMongoCollection<TDocument> _collection;

        public BaseRepository(
            ILogger logger,
            IMongoCollection<TDocument> collection)
        {
            this.logger = logger;
            _collection = collection;
        }

        public virtual bool OnException(Exception ex)
        {
            logger.LogWarning(ex, "Falha ao processar {CollectionName}", _collection.CollectionNamespace.CollectionName);
            return false;
        }

        public TDocument? GetById(string _id)
        {
            var filter = Builders<TDocument>.Filter.Eq("_id", ObjectId.Parse(_id));
            var item = _collection.Find(filter).FirstOrDefault();

            if (item == null)
                return default;

            return item;
        }

        public void InsertOne(TDocument item)
        {
            try
            {
                //var document = item.ToBsonDocument();
                _collection.InsertOne(item);
            }
            catch (Exception ex)
            {
                if (OnException(ex))
                    throw;
            }
        }

        public void InsertMany(IEnumerable<TDocument> items)
        {
            try
            {
                //var documents = items.Select(i => i.ToBsonDocument());
                _collection.InsertMany(items);
            }
            catch (Exception ex)
            {
                if (OnException(ex))
                    throw;
            }
        }

        public Task InsertOneAsync(TDocument item)
        {
            try
            {
                //var document = item.ToBsonDocument();
                return _collection.InsertOneAsync(item);
            }
            catch (Exception ex)
            {
                if (OnException(ex))
                    throw;
                return Task.CompletedTask;
            }
        }

        public Task InsertManyAsync(IEnumerable<TDocument> items)
        {
            try
            {
                //var documents = items.Select(i => i.ToBsonDocument());
                return _collection.InsertManyAsync(items);
            }
            catch (Exception ex)
            {
                if (OnException(ex))
                    throw;
                return Task.CompletedTask;
            }
        }

        public void UpdateOne(string _id, Dictionary<string, object> properties)
        {
            try
            {
                var filterUpdate = UpdateBuildFilterAndUpdate(_id, properties);
                var filter = filterUpdate.Item1;
                var update = filterUpdate.Item2;

                _collection.UpdateOne(filter, update);
            }
            catch (Exception ex)
            {
                if (OnException(ex))
                    throw;
            }
        }

        public Task UpdateOneAsync(string _id, Dictionary<string, object> properties)
        {
            try
            {
                var (filter, update) = UpdateBuildFilterAndUpdate(_id, properties);
                return _collection.UpdateOneAsync(filter, update);
            }
            catch (Exception ex)
            {
                if (OnException(ex))
                    throw;
                return Task.CompletedTask;
            }
        }

        private (FilterDefinition<TDocument>, UpdateDefinition<TDocument>) UpdateBuildFilterAndUpdate(string _id, Dictionary<string, object> properties)
        {
            var filter = Builders<TDocument>.Filter.Eq("_id", ObjectId.Parse(_id));
            var update = Builders<TDocument>.Update.Set("DataAtualizacaoUtc", DateTime.UtcNow);
            foreach (var item in properties)
                update = update.Set(item.Key, item.Value);
            return (filter, update);
        }
    }
}