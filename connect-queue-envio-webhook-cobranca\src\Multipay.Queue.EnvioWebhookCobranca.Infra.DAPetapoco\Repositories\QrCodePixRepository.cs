﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookCobranca.Core.Models;
using PetaPoco;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.DAPetapoco.Repositories
{
    public interface IQrCodePixRepository
    {
        Task AtualizarEndToEndPorTxId(string txid, string endToEndPagamento);

        Task<QRCodePIXModel?> ObterPorIdCobranca(int id);

        Task<QRCodePIXModel?> ObterPorTxId(string txId);
    }

    internal class QrCodePixRepository : IQrCodePixRepository
    {
        private readonly ILogger logger;
        private readonly IDatabase database;

        public QrCodePixRepository(
            ILogger logger,
            IDatabase database)
        {
            this.logger = logger;
            this.database = database;
        }

        public async Task AtualizarEndToEndPorTxId(string txid, string endToEndPagamento)
        {
            try
            {
                await database.ExecuteNonQueryProcAsync("Arquitetura.Update_QRCodePIXEndToEnd", new
                {
                    txid,
                    endToEndPagamento
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(QrCodePixRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<QRCodePIXModel?> ObterPorIdCobranca(int idCobranca)
        {
            try
            {
                using var reader = await database.QueryProcAsync<QRCodePIXModel>("Painel.Select_QRCodePIXPorIdCobranca", new
                {
                    idCobranca
                });

                if (await reader.ReadAsync())
                {
                    return reader.Poco;
                }
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(QrCodePixRepository), database.LastCommand);
                throw;
            }
        }

        public async Task<QRCodePIXModel?> ObterPorTxId(string txId)
        {
            try
            {
                using var reader = await database.QueryProcAsync<QRCodePIXModel>("Painel.Select_QRCodePIXPorTxId", new
                {
                    txId
                });

                if (await reader.ReadAsync())
                {
                    return reader.Poco;
                }
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{nameof}]LastCommand: {LastCommand}", nameof(QrCodePixRepository), database.LastCommand);
                throw;
            }
        }
    }
}