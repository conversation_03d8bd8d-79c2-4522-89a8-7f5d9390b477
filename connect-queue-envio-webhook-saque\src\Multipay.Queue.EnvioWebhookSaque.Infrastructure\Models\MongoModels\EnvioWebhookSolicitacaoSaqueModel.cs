﻿using MongoDB.Bson.Serialization.Attributes;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels
{
    [BsonIgnoreExtraElements]
    public class EnvioWebhookSolicitacaoSaqueModel : BaseModel
    {
        public EnvioWebhookSolicitacaoSaqueModel(int idSolicitacaoSaque, string codigoSolicitacaoSaque, string acao, string urlWebhook, string requestJson, string httpResponseCode, string responseJson)
        {
            IdSolicitacaoSaque = idSolicitacaoSaque;
            CodigoSolicitacaoSaque = codigoSolicitacaoSaque;
            Acao = acao;
            UrlWebhook = urlWebhook;
            RequestJson = requestJson;
            HttpResponseCode = httpResponseCode;
            ResponseJson = responseJson;
            DataCriacaoUtc = DateTime.UtcNow;
            DataAtualizacaoUtc = string.IsNullOrEmpty(httpResponseCode) ? null : DataCriacaoUtc;
        }

        public int IdSolicitacaoSaque { get; private set; }
        public string CodigoSolicitacaoSaque { get; private set; }
        public string Acao { get; private set; }
        public string UrlWebhook { get; private set; }
        public string RequestJson { get; private set; }
        public string HttpResponseCode { get; private set; }
        public string ResponseJson { get; private set; }
        public DateTime DataCriacaoUtc { get; private set; }
        public DateTime? DataAtualizacaoUtc { get; private set; }

        public override Dictionary<string, object?> GetUpdateDictionary()
        {
            return new()
            {
                { nameof(HttpResponseCode), HttpResponseCode },
                { nameof(ResponseJson), ResponseJson }
            };
        }

        public void SetResponseData(string requestJson, string httpResponseCode, string responseJson)
        {
            RequestJson = requestJson;
            HttpResponseCode = httpResponseCode;
            ResponseJson = responseJson;
        }
    }
}