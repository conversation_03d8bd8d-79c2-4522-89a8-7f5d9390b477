﻿using Multipay.Queue.Webhook.Application.Models.Aarin;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;

namespace Multipay.Queue.Webhook.Application.Services.Aarin;
public interface IPixReversalService
{
    Task<bool> VerifyPixCorrelationAsync<T>(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, CancellationToken cancellationToken);
    Task<bool> ProcessReversalPixAsync<T>(WebhookRecebidoEvent @event, BehaviorReversalPixModel behavior, CancellationToken cancellationToken);
}
