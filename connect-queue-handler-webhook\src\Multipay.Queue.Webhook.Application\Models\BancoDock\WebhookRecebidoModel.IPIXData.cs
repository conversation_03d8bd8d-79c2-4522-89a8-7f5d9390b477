﻿using Multipay.Queue.Webhook.Core.Interfaces;
using System.Globalization;

namespace Multipay.Queue.Webhook.Application.Models.BancoDock
{
    internal partial class WebhookRecebidoModel
    {
        public partial class PayloadData : IPIXData
        {
            string? IPIXData.IspbPagador => DebitParty?.Ispb;
            string? IPIXData.NomeBancoPagador => DebitParty?.BankName;
            string? IPIXData.AgenciaBancoPagador => DebitParty?.BankBranchNumber;
            string? IPIXData.ContaBancoPagador => DebitParty?.BankAccountNumber;

            string IPIXData.Txid => IdTx ?? TransactionCode;

            string IPIXData.Valor => FinalAmount.ToString(CultureInfo.GetCultureInfo("pt-BR"));

            string IPIXData.CpfPagador => DebitParty!.NationalRegistration;

            string IPIXData.NomePagador => DebitParty!.Name;

            string IPIXData.EndToEndId => EndToEndId;        
        }
    }
}