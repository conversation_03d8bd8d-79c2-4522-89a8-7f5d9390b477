{"version": 3, "targets": {"net9.0": {"ResultT/1.0.1": {"type": "package", "compile": {"lib/net9.0/ResultT.dll": {}}, "runtime": {"lib/net9.0/ResultT.dll": {}}}}}, "libraries": {"ResultT/1.0.1": {"sha512": "tMKv8yuwubCKrKSN27f86v4DXLCYhH3Lcddbms72LkuKas4xydxHBRBfhUdELQF9L1pmjexUvJXQuoLlSz9xYA==", "type": "package", "path": "resultt/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net9.0/ResultT.dll", "resultt.1.0.1.nupkg.sha512", "resultt.nuspec"]}}, "projectFileDependencyGroups": {"net9.0": ["ResultT >= 1.0.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\Domain.csproj", "projectName": "Domain", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\connect-background-worker\\src\\Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"ResultT": {"target": "Package", "version": "[1.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}