﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Multipay.Queue.Webhook.Application.Models.Celcoin;
using Multipay.Queue.Webhook.Application.Services.Celcoin;
using Multipay.Queue.Webhook.Application.Services.CobrancaRecebida;
using Multipay.Queue.Webhook.Application.Services.SaqueConfirmado;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco.Repositories;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Events;
using Serilog;
using Serilog.Context;

namespace Multipay.Queue.Webhook.Application.Services
{
    public class WebhookRecebidoCelcoinService : IWebhookRecebidoService
    {
        private readonly ILogger<WebhookRecebidoCelcoinService> logger;

        public WebhookRecebidoCelcoinService(ILogger<WebhookRecebidoCelcoinService> logger)
        {
            this.logger = logger;
        }
        public TipoBanco TipoBanco => TipoBanco.Celcoin;

        public async Task<bool> ProcessarWebhook(IServiceProvider serviceProvider, WebhookRecebidoEvent @event)
        {


            if (@event.Tipo == "pix/recebimento")
            {
                this.logger.LogInformation("[{0}] Iniciando PIX Recebimento, Tipo: {1}", nameof(WebhookRecebidoCelcoinService), @event.Tipo);

                var service = serviceProvider.GetRequiredService<CobrancaRecebidaCelcoinService>();
                return await service.Processar($"{@event.Data}", @event.IdWebhook);
            }

            this.logger.LogInformation("[{0}] Iniciando verificacao de devolucao, Tipo: {1}", nameof(CelcoinPixReversalService), @event.Tipo);

            try
            {

                var serviceRefund = serviceProvider.GetRequiredService<ICelcoinPixReversalService>();
                if (await serviceRefund.VerifyPixCorrelationAsync<ReversalPixCelcoinModel>(@event, new BehaviorReversalPixModel
                {
                    CheckCorrelationId = true,
                    CheckStatus = true,
                    CheckValue = true,
                    StatusAttributeName = "Status",
                    CompletedStatusName = "Completed",
                    CorrelationIdAttributeName = "PixId",
                    FinancialInstitution = "Celcoin",
                    OriginIp = ""
                }, default))
                {
                    this.logger.LogInformation("[{0}] Pix devolução encontrado, Tipo: {1}", nameof(CelcoinPixReversalService), @event.Tipo);
                    @event.Tipo = "pix/devolucao";
                }
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "[{0}] Falha na busca de Devolucao, Ex: {ex}", nameof(CelcoinPixReversalService), ex);
            }

            this.logger.LogInformation("[{0}] Final de verificacao de devolucao, Tipo: {1}", nameof(CelcoinPixReversalService), @event.Tipo);

            if (@event.Tipo == "pix/pagamento")
            {
                this.logger.LogInformation("[{0}] Iniciando PIX pagamento, Tipo: {1}", nameof(CelcoinPixReversalService), @event.Tipo);

                var service = serviceProvider.GetRequiredService<IPagamentoConfirmadoCelcoinService>();
                return await service.ProcessarPagamento(@event);
            }

            if (@event.Tipo == "pix/devolucao")
            {

                this.logger.LogInformation("[{0}] Iniciando PIX devolucao, Tipo: {1}", nameof(CelcoinPixReversalService), @event.Tipo);

                var service = serviceProvider.GetRequiredService<ICelcoinPixReversalService>();
                return await service.ProcessReversalPixAsync<ReversalPixCelcoinModel>(@event, new BehaviorReversalPixModel
                {
                    CheckCorrelationId = true,
                    CheckStatus = true,
                    CheckValue = true,
                    StatusAttributeName = "Status",
                    CompletedStatusName = "Completed",
                    CorrelationIdAttributeName = "PixId",
                    FinancialInstitution = "Celcoin",
                    ValueAttributeName = "Value",
                    OriginIp = ""
                }, default);
            }

            logger.LogWarning($"Mensagem não tratada. {@event}");

            return await Task.FromResult(true);
        }

        public string? GetCorrelationId(WebhookRecebidoEvent @event)
        {
            try
            {
                if (@event.Data is null)
                    return string.Empty;

                var parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<ReversalPixCelcoinModel>($"{@event.Data}");
                if (parameters is null)
                    return string.Empty;

                return parameters.Body.OriginalEndToEndId;
            }
            catch(Exception ex)
            {
                this.logger.LogError(ex, "[{0}] Não foi possivel obter o pixid, Tipo: {1}", nameof(CelcoinPixReversalService), @event.Tipo);
                return string.Empty;
            }
        }
    }

}
