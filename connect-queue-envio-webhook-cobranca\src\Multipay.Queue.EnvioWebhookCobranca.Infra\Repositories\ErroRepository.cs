﻿using Microsoft.EntityFrameworkCore;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Data;

namespace Multipay.Queue.Webhook.Infrastructure.Repositories
{
    public interface IErroRepository
    {
        Task InsertErro(string controller, string action, string url, string mensagemErro, string stackTrace, string texto);
    }

    internal class ErroRepository : IErroRepository
    {
        private readonly DbContext dbContext;

        public ErroRepository(DbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public virtual async Task InsertErro(string controller, string action, string url, string mensagemerro, string stacktrace, string texto)
        {
            await dbContext.CallStoredProcedureAsync("Arquitetura.Insert_Erro",
                controller.ToSqlParameter(),
                action.ToSqlParameter(),
                url.ToSqlParameter(),
                mensagemerro.ToSqlParameter(),
                stacktrace.ToSqlParameter(),
                texto.ToSqlParameter());
        }
    }
}