﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Data;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Repositories.MongoRepositories
{
    public interface IEnvioWebhookSolicitacaoSaqueSaqueRepository : IBaseRepository<EnvioWebhookSolicitacaoSaqueModel>
    { }

    internal class EnvioWebhookSolicitacaoSaqueSaqueRepository : BaseRepository<EnvioWebhookSolicitacaoSaqueModel>, IEnvioWebhookSolicitacaoSaqueSaqueRepository
    {
        public EnvioWebhookSolicitacaoSaqueSaqueRepository(
            ILogger<EnvioWebhookSolicitacaoSaqueSaqueRepository> logger,
            AnspaceMongoContext context) : base(logger, context.EnvioWebhookSolicitacaoSaque)
        {
        }
    }
}