﻿using Multipay.Queue.Webhook.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Multipay.Infrastructure.Plugin.Aarin.Abstractions
{
    public interface IAuthService
    {
        ValueTask<string?> GetAccessToken(ContaBancariaEmpresaModel contaBancariaEmpresa, bool gerarNovoToken, CancellationToken cancellationToken);
    }
}
