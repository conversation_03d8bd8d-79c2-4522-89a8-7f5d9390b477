@model PagedList.IPagedList<PigPag.Manager.Models.Cobranca._PartialPesquisarModel>
<style>
    .limited-text-80 {
        white-space: nowrap;
        width: 20px !important;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
<table class="table table-hover table-striped">
    <thead>
        <tr>
            <th colspan="14" class="text-left"><text>@Model.TotalItemCount.ToString(Constant.Mascaras.MascaraNumeroMilhar) registros encontrados</text></th>
            <div class="row">
                <div class="col-04">
                    <button id="btnReprocessar" name="btnReprocessar" type="button" class="btn btn-primary only-read" onclick="reprocessar();" pigpag-button-confirm pigpag-message="Deseja realmente consultar os itens selecionados? <br/>Esta operação não poderá ser revertida.">Consultar Recebimento</button>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <button id="btnReenviar" name="btnReenviar" type="button" class="btn btn-primary only-read" onclick="reenviar();" pigpag-button-confirm pigpag-message="Deseja realmente reenviar a confirmação ao sistema do clientes? <br/>Esta operação não poderá ser revertida.">Reenviar Confirmação de Pagamento</button>
                </div>
                <div class="col-04"></div>
                <div class="col-04"></div>
            </div>

        </tr>
        <tr>
            <th class="text-center">Consultar <br/><input type="checkbox" id="reprocessarTudo" /></th>
            <th class="text-center">Reenviar <br /><input type="checkbox" id="reenviarTudo" /></th>
            <th class="text-left">Cliente</th>
            <th class="text-left">Código</th>
            <th class="text-left">Fatura</th>
            <th class="text-center">Comprovante</th>
            <th>Nome</th>
            <th class="text-right">Valor Líquido</th>
            <th class="text-right">Valor Pago</th>
            <th class="text-center">Cadastro</th>
            <th class="text-center">Vencimento</th>
            <th class="text-center">Pagamento</th>
            <th class="text-center">Cancelamento</th>
            <th class="text-center">Status</th>
        </tr>
    </thead>
    <tbody>
        @if (Model.TotalItemCount > 0)
        {
            foreach (var c in Model)
            {
                <tr>

                    <td class="text-center">
                        @if (c.DataCancelamento.HasValue || c.DataPagamento.HasValue)
                        {
                        }
                        else
                        {
                            <input type="checkbox" class="reprocessarCheckbox" value="@c.TxIdPIX" />
                        }
                    </td>
                    <td class="text-center">

                        @if (c.DataPagamento.HasValue)
                        {
                            <input type="checkbox" class="reenviarCheckbox" value="@c.IdCobranca" />
                        }
                        else
                        {
                        }
                    </td>
                    <td class="text-left">
                        @if (!string.IsNullOrEmpty(c.NomeFantasiaCliente))
                        {
                            <text>@c.NomeFantasiaCliente</text>
                        }
                        else
                        {
                            <text>@c.NomePessoaCliente</text>
                        }
                    </td>
                    <td class="text-left" style="min-width: 50px"><a href="/CobrancaPesquisar/Detalhe/@c.IdCobranca">@c.CodigoCobranca</a></td>
                    <td class="text-left" style="min-width: 50px"><a href="/CobrancaPesquisar/Detalhe/@c.IdCobranca">@c.NumeroFatura</a></td>
                    <td style="margin: 0; zoom: .8; vertical-align: middle; text-align: center;">
                        @if (c.EndToEndPagamento != null || c.EndToEndDevolucao != null)
                        {
                            <button type="button" onclick="imprimirComprovante('@(c.EndToEndDevolucao != null ? c.EndToEndDevolucao : c.EndToEndPagamento)')" class="btn btn-default" data-toggle="tooltip" data-original-title="@Resource.ImprimirComprovante"><i class="fa-print fa"></i></button>
                        }
                        else
                        {
                            <button type="button" onclick="javascript:void(0)" class="btn btn-default" data-toggle="tooltip" data-original-title="@Resource.ImprimirComprovante" disabled style="opacity: .5;"><i class="fa-print fa"></i></button>
                        }
                    </td>
                    <td style="min-width: 120px">@c.NomePagador</td>
                    <td style="min-width: 60px" class="text-right">@String.Format("{0:###,###,###0.00}", c.ValorLiquido)</td>
                    <td style="min-width: 60px" class="text-right">
                        @if (c.ValorPago.HasValue && c.ValorPago.Value > 0.00000000M)
                        {
                            if (c.IdFormaPagamento == Constant.FormaPagamento.Bitcoin)
                            {
                                <text>@c.ValorPago.Value.ToString(Constant.Mascaras.MascaraMoedaBTC)</text>
                            }
                            else
                            {
                                <text>@String.Format("{0:###,###,###0.00}", c.ValorPago.Value)</text>
                            }
                        }
                        else
                        {
                            <text>-</text>
                        }
                    </td>
                    <td class="text-center">
                        @c.DataCadastro
                    </td>
                    <td class="text-center">
                        @if (c.DataVencimento.HasValue)
                        {
                            <text>@c.DataVencimento.Value.ToShortDateString()</text>
                        }
                        else
                        {
                            <text>-</text>
                        }
                    </td>
                    <td class="text-center">
                        @if (c.DataPagamento.HasValue)
                        {
                            <text>@c.DataPagamento.Value</text>
                        }
                        else
                        {
                            <text>-</text>
                        }
                    </td>
                    <td class="text-center">
                        @if (c.DataCancelamento.HasValue)
                        {
                            <text>@c.DataCancelamento.Value.ToShortDateString()</text>
                        }
                        else
                        {
                            <text>-</text>
                        }
                    </td>
                    <td class="text-center" style="width: 100px">
                        @switch (c.StatusCobranca)
                        {
                            case 1:
                                <span style="display: block; border-radius: 16px; padding: 6px 0;" class="label label-primary">@Resource.Aguardando</span>
                                break;

                            case 2:
                                <span style="display: block; border-radius: 16px; padding: 6px 0;" class="label label-warning">@Resource.Vencido</span>
                                break;

                            case 3:
                                <span style="display: block; border-radius: 16px; padding: 6px 0;" class="label label-danger">@Resource.Cancelado</span>
                                break;

                            case 4:
                                <span style="display: block; border-radius: 16px; padding: 6px 0;" class="label label-success">@Resource.Pago</span>
                                break;
                        }
                    </td>
                </tr>
            }
        }
        else
        {
            <tr>
                <td colspan="14" style="text-align:center">@Resource.NenhumRegistroEncontrado</td>
            </tr>
        }
    </tbody>
</table>

@if (Model.TotalItemCount > 0)
{
    <div class="box-footer clearfix">
        <span>@*Página @(Model.PageCount < Model.PageNumber ? 0 : Model.PageNumber) de @Model.PageCount*@</span>
        <ul class="pagination pagination-sm no-margin pull-right">
            @if (Model.HasPreviousPage)
            {
                <li><a href="javascript:PesquisarCobranca(@(Model.PageNumber - 1));">< @Resource.Anterior</a></li>
            }
            else
            {
                <li><a href="javascript:void(0);">< @Resource.Anterior</a></li>
            }

            <li><a href="javascript:PesquisarCobranca(@(Model.PageNumber + 1));">@Resource.Proxima ></a></li>

        </ul>
    </div>
}

<script>

    $(document).ready(function () {

        $('#reprocessarTudo').click(function () {
            $('.reprocessarCheckbox').prop('checked', this.checked);
        });

        $('.reprocessarCheckbox').change(function () {
            var selectedIds = [];
            $('.reprocessarCheckbox:checked').each(function () {
                selectedIds.push($(this).val());
            });
        });

        $('#reenviarTudo').click(function () {
            $('.reenviarCheckbox').prop('checked', this.checked);
        });

        $('.reenviarCheckbox').change(function () {
            var selectedIds = [];
            $('.reenviarCheckbox:checked').each(function () {
                selectedIds.push($(this).val());
            });
        });



    });

    function imprimirComprovante(e2e) {

        window.open('https://api.delbank.com.br/baas/api/v1/pix/' + e2e + '/proof', '_blank');
    }

    function reprocessar() {

        var selectedIds = [];

        $('.reprocessarCheckbox:checked').each(function () {

            selectedIds.push($(this).val());
        });

        if (!selectedIds.length) {

            swal(
                'Oops...',
                'Nenhum registro selecionado. Selecione pelo menos um registro.',
                'error'
            )

            return;
        }

        swal({
            type: 'question',
            title: 'Atenção!!!',
            text: `${selectedIds.length} registros serão consultados. Deseja continuar?`,
            showLoaderOnConfirm: true,
            allowOutsideClick: true,
            allowEscapeKey: true,
            showCancelButton: true,
            confirmButtonText: 'Sim',
            cancelButtonText: 'Cancelar',
            preConfirm: () => {

                return new Promise((resolve, reject) => {

                    $.ajax({
                        url: '/CobrancaPesquisar/ConsultaRecebimentoQrCode',
                        type: "POST",
                        data: { txIds: selectedIds },
                        success: function (response) {

                            resolve(response);
                        },
                        error: function (xhr, status, error) {

                            reject(error);
                        },
                        timeout: 600000
                    });
                });
            }
        }).then((result) => {

            selectedIds = [];

            $('.reprocessarCheckbox').prop('checked', false);
            $('#reprocessarTudo').prop('checked', false);

            $("#dialogProcessando").modal('hide');

            swal(
                'Cobranças',
                result,
                'success'
            );

        }).catch(swal.noop);
    }

    function reenviar() {

        var selectedIds = [];

        $('.reenviarCheckbox:checked').each(function () {

            selectedIds.push($(this).val());
        });

        if (!selectedIds.length) {

            swal(
                'Oops...',
                'Nenhum registro selecionado. Selecione pelo menos um registro.',
                'error'
            )

            return;
        }

        swal({
            type: 'question',
            title: 'Atenção!!!',
            text: `${selectedIds.length} webhooks serão reenviados. Deseja continuar?`,
            showCancelButton: true,
            confirmButtonText: 'Sim',
            cancelButtonText: 'Cancelar',
            allowOutsideClick: false,
            allowEscapeKey: false
        }).then((result) => {
            if (result.value) {
                startWebhookReenvio(selectedIds);
            }
        }).catch(swal.noop);
    }

    function startWebhookReenvio(selectedIds) {
        $.ajax({
            url: '/Cobranca/ReenviarConfirmacaoPagamento',
            type: "POST",
            data: { ids: selectedIds },
            success: function (response) {
                if (response.success) {
                    showProgressModal(response.jobId, response.total);

                    // Limpar seleções
                    selectedIds = [];
                    $('.reenviarCheckbox').prop('checked', false);
                    $('#reenviarTudo').prop('checked', false);
                } else {
                    swal('Erro', response.message || 'Erro ao iniciar reenvio', 'error');
                }
            },
            error: function (xhr, status, error) {
                swal('Erro', 'Erro ao iniciar reenvio: ' + error, 'error');
            }
        });
    }

    function showProgressModal(jobId, total) {
        // Criar modal de progresso
        var modalHtml = `
            <div id="progressModal" class="modal fade" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                                <i class="fa fa-paper-plane"></i>
                                Reenvio de Webhooks em Andamento
                            </h4>
                        </div>
                        <div class="modal-body">
                            <div class="progress" style="height: 25px; margin-bottom: 15px;">
                                <div id="progressBar" class="progress-bar progress-bar-striped active"
                                     role="progressbar" style="width: 0%; min-width: 2em;">
                                    <span id="progressText">0%</span>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-box bg-blue">
                                        <span class="info-box-icon"><i class="fa fa-list"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total</span>
                                            <span class="info-box-number" id="totalCount">${total}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-box bg-gray">
                                        <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Processados</span>
                                            <span class="info-box-number" id="processedCount">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-box bg-green">
                                        <span class="info-box-icon"><i class="fa fa-check"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Sucessos</span>
                                            <span class="info-box-number" id="successCount">0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-box bg-red">
                                        <span class="info-box-icon"><i class="fa fa-times"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Falhas</span>
                                            <span class="info-box-number" id="failedCount">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i>
                                <strong>Status:</strong> <span id="currentItem">Iniciando...</span>
                            </div>

                            <div class="text-muted text-center">
                                <small>
                                    <i class="fa fa-clock-o"></i>
                                    Tempo decorrido: <span id="duration">00:00</span>
                                </small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button id="closeProgress" class="btn btn-default" disabled>
                                <i class="fa fa-times"></i> Fechar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remover modal anterior se existir
        $('#progressModal').remove();

        $('body').append(modalHtml);
        $('#progressModal').modal({
            backdrop: 'static',
            keyboard: false
        });

        // Iniciar polling
        pollProgress(jobId, total);
    }

    function pollProgress(jobId, total) {
        var pollInterval = setInterval(function() {
            $.ajax({
                url: '/Cobranca/GetWebhookProgress',
                type: 'GET',
                data: { jobId: jobId },
                success: function(data) {
                    if (!data.found) {
                        clearInterval(pollInterval);
                        swal('Erro', data.message || 'Progresso não encontrado', 'error');
                        $('#progressModal').modal('hide');
                        return;
                    }

                    // Atualizar UI
                    updateProgressUI(data);

                    // Se completou
                    if (data.isCompleted) {
                        clearInterval(pollInterval);
                        completeProgress(data);
                    }
                },
                error: function(xhr, status, error) {
                    clearInterval(pollInterval);
                    swal('Erro', 'Erro ao consultar progresso: ' + error, 'error');
                    $('#progressModal').modal('hide');
                }
            });
        }, 2000); // Poll a cada 2 segundos

        // Permitir fechar modal manualmente após 5 segundos
        setTimeout(function() {
            $('#closeProgress').prop('disabled', false);
        }, 5000);
    }

    function updateProgressUI(data) {
        var percentage = data.progressPercentage;

        $('#progressBar').css('width', percentage + '%');
        $('#progressText').text(percentage + '%');
        $('#processedCount').text(data.processed);
        $('#successCount').text(data.success);
        $('#failedCount').text(data.failed);
        $('#currentItem').text(data.currentItem || 'Processando...');
        $('#duration').text(data.duration || '00:00');

        // Atualizar cores baseado no status
        if (data.failed > 0) {
            $('#progressBar').removeClass('progress-bar-success')
                           .addClass('progress-bar-warning');
        }
    }

    function completeProgress(data) {
        $('#progressBar').removeClass('active progress-bar-striped progress-bar-warning')
                       .addClass('progress-bar-success');
        $('#currentItem').html('<i class="fa fa-check"></i> Processamento concluído!');
        $('#closeProgress').prop('disabled', false)
                          .removeClass('btn-default')
                          .addClass('btn-success')
                          .html('<i class="fa fa-check"></i> Concluído');

        // Mostrar resultado final
        var message = `Reenvio concluído!<br>`;
        message += `✅ Sucessos: ${data.success}<br>`;
        if (data.failed > 0) {
            message += `❌ Falhas: ${data.failed}<br>`;
        }
        message += `⏱️ Tempo total: ${data.duration}`;

        swal({
            title: 'Reenvio Concluído!',
            html: message,
            type: data.failed > 0 ? 'warning' : 'success',
            confirmButtonText: 'OK'
        });
    }

    // Fechar modal ao clicar no botão
    $(document).on('click', '#closeProgress', function() {
        $('#progressModal').modal('hide');
    });

    // Limpar modal ao fechar
    $(document).on('hidden.bs.modal', '#progressModal', function() {
        $(this).remove();
    });

</script>
