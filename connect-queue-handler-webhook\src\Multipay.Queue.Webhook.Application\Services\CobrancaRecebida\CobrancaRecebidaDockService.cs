﻿using Multipay.Queue.Webhook.Application.Models.BancoDock;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaDockService : CobrancaRecebidaService<WebhookRecebidoModel, WebhookRecebidoModel.PayloadData>
    {
        public CobrancaRecebidaDockService(
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.Dock)
        {
        }

        protected override IEnumerable<WebhookRecebidoModel.PayloadData> ListPix => new List<WebhookRecebidoModel.PayloadData> { Parameters!.Payload };

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);
    }
}