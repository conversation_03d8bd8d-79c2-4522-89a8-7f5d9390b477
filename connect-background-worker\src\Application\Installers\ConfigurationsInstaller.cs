using System.Diagnostics.CodeAnalysis;
using Application.Configurations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Application.Installers;

[ExcludeFromCodeCoverage]
public static class ConfigurationsInstaller
{
    public static IServiceCollection AddConfigurations(this IServiceCollection services, IConfiguration configuration) =>
        services
            .Configure<CronConfigurarion>(configuration.GetSection(nameof(CronConfigurarion)))
            .Configure<ConnectApiConfiguration>(configuration.GetSection(nameof(ConnectApiConfiguration)))
            .Configure<ConnectPSPConfiguration>(configuration.GetSection(nameof(ConnectPSPConfiguration)));
}
