﻿namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Redis
{
    public class Charge(int idCliente, string codigo, Guid guid, string txId, decimal valorFatura, string numeroFatura, int idCobranca,
        string? urlConfirmacao, string? requestIdWebhook, string? moedaPagamento, string moedaCobranca, Guid guidCliente, Guid? idInconsistencia,
        DateTime? dataPagamento, string criptoTokenApi, string usernameApi, string passwordApi, string scopeApi, bool isEncrypted, string numeroConta)
    {
        private int _idCliente = idCliente;
        private string _codigo = codigo;
        private Guid _guid = guid;
        private string _txId = txId;
        private decimal _valorFatura = valorFatura;
        private string _numeroFatura = numeroFatura;
        private int _idCobranca = idCobranca;
        private string? _urlConfirmacao = urlConfirmacao;
        private string? _requestIdWebhook = requestIdWebhook;
        private string? _moedaPagamento = moedaPagamento;
        private string _moedaCobranca = moedaCobranca;
        private Guid _guidCliente = guidCliente;
        private Guid? _idInconsistencia = idInconsistencia;
        private DateTime? _dataPagamento = dataPagamento;
        private string _criptoTokenApi = criptoTokenApi;
        private string _usernameApi = usernameApi;
        private string _passwordApi = passwordApi;
        private string _scopeApi = scopeApi;
        private bool _isEncrypted = isEncrypted;
        private string _numeroConta = numeroConta;
        public int IdCliente { get => _idCliente; private set => _idCliente = value; }
        public string TxId { get => _txId; private set => _txId = value; }
        public string Codigo { get => _codigo; private set => _codigo = value; }
        public Guid Guid { get => _guid; private set => _guid = value; }
        public decimal ValorFatura { get => _valorFatura; private set => _valorFatura = value; }
        public string NumeroFatura { get => _numeroFatura; private set => _numeroFatura = value; }
        public int IdCobranca { get => _idCobranca; private set => _idCobranca = value; }
        public string? UrlConfirmacao { get => _urlConfirmacao; private set => _urlConfirmacao = value; }
        public string? RequestIdWebhook { get => _requestIdWebhook; private set => _requestIdWebhook = value; }
        public string? MoedaPagamento { get => _moedaPagamento; private set => _moedaPagamento = value; }
        public string MoedaCobranca { get => _moedaCobranca; private set => _moedaCobranca = value; }
        public Guid GuidCliente { get => _guidCliente; private set => _guidCliente = value; }
        public Guid? IdInconsistencia { get => _idInconsistencia; private set => _idInconsistencia = value; }
        public DateTime? DataPagamento { get => _dataPagamento; private set => _dataPagamento = value; }
        public string UsernameApi { get => _usernameApi; private set => _usernameApi = value; }
        public string PasswordApi { get => _passwordApi; private set => _passwordApi = value; }
        public string CriptoTokenApi { get => _criptoTokenApi; private set => _criptoTokenApi = value; }
        public string ScopeApi { get => _scopeApi; private set => _scopeApi = value; }
        public bool IsEncrypted { get => _isEncrypted; private set => _isEncrypted = value; }
        public string NumeroConta { get => _numeroConta; set => _numeroConta = value; }

        public bool TemInconsistencia
        {
            get
            {
                if (!string.IsNullOrEmpty(_idInconsistencia.ToString()))
                    return true;
                return false;
            }
        }
    }
}