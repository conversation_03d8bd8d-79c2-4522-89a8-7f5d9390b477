﻿using Multipay.RabbitMQExtension.DTOs;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.RabbitMq.Settings
{
    public class RabbitMqSettings : RabbitMqSettingsBase
    {
        public Config NotificarSaqueCliente { get; set; } = null!;
        public Config NotificarSaqueClientePublishNotification { get; set; } = null!;
        public Config SolicitacaoSaques { get; set; } = null!;
        public Config SolicitacaoSaquesEstornado { get; set; } = null!;
        public Config SolicitacaoSaquesEstornadoRetry { get; set; } = null!;
        public Config NotificarClienteRetry { get; set; } = null!;
    }
}