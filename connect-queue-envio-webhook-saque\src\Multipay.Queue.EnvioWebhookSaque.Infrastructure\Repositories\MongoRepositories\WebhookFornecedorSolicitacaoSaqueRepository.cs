﻿using Microsoft.Extensions.Logging;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Data;
using Multipay.Queue.EnvioWebhookSaque.Infrastructure.Models.MongoModels;

namespace Multipay.Queue.EnvioWebhookSaque.Infrastructure.Repositories.MongoRepositories
{
    public interface IWebhookFornecedorSolicitacaoSaqueRepository : IBaseRepository<WebhookFornecedorSolicitacaoSaqueModel>
    { }

    internal class WebhookFornecedorSolicitacaoSaqueRepository : BaseRepository<WebhookFornecedorSolicitacaoSaqueModel>, IWebhookFornecedorSolicitacaoSaqueRepository
    {
        public WebhookFornecedorSolicitacaoSaqueRepository(
            ILogger<WebhookFornecedorSolicitacaoSaqueRepository> logger,
            AnspaceMongoContext context) : base(logger, context.WebhookFornecedorSolicitacaoSaque) { }
    }
}