﻿using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Driver;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Models.MongoModels;
using Multipay.Queue.EnvioWebhookCobranca.Infra.Setup;

namespace Multipay.Queue.EnvioWebhookCobranca.Infra.Data
{
    internal class AnspaceMongoContext
    {
        public IMongoCollection<EnvioWebhookCobrancaModel> EnvioWebhookCobranca { get; }
        public IMongoCollection<LogCobrancaModel> LogCobranca { get; }
        public IMongoCollection<RequisicoesAPIModel> RequisicoesAPI { get; }
        public IMongoCollection<RequisicoesBMPModel> RequisicoesBMP { get; }
        public IMongoCollection<RequisicoesFornecedorModel> RequisicoesFornecedor { get; }
        public IMongoCollection<WebhookFornecedorModel> WebhookFornecedor { get; }
        public IMongoCollection<WebhookFornecedorCobrancaModel> WebhookFornecedorCobranca { get; }
        public IMongoCollection<CobrancasGeradasModel> CobrancasGeradas { get; }

        private MongoClient mongoClient;
        private IMongoDatabase mongoDatabase;

        public AnspaceMongoContext(IOptions<MongoStoreDatabaseSettings> storeDatabaseSettings)
        {
            mongoClient = new MongoClient(storeDatabaseSettings.Value.ConnectionString);

            mongoDatabase = mongoClient.GetDatabase(storeDatabaseSettings.Value.DatabaseName);

            EnvioWebhookCobranca = mongoDatabase.GetCollection<EnvioWebhookCobrancaModel>(storeDatabaseSettings.Value.CollectionNames.EnvioWebhookCobranca);
            LogCobranca = mongoDatabase.GetCollection<LogCobrancaModel>(storeDatabaseSettings.Value.CollectionNames.LogCobranca);
            RequisicoesAPI = mongoDatabase.GetCollection<RequisicoesAPIModel>(storeDatabaseSettings.Value.CollectionNames.RequisicoesAPI);
            RequisicoesBMP = mongoDatabase.GetCollection<RequisicoesBMPModel>(storeDatabaseSettings.Value.CollectionNames.RequisicoesBMP);
            RequisicoesFornecedor = mongoDatabase.GetCollection<RequisicoesFornecedorModel>(storeDatabaseSettings.Value.CollectionNames.RequisicoesFornecedor);
            WebhookFornecedor = mongoDatabase.GetCollection<WebhookFornecedorModel>(storeDatabaseSettings.Value.CollectionNames.WebhookFornecedor);
            WebhookFornecedorCobranca = mongoDatabase.GetCollection<WebhookFornecedorCobrancaModel>(storeDatabaseSettings.Value.CollectionNames.WebhookFornecedorCobranca);
            CobrancasGeradas = mongoDatabase.GetCollection<CobrancasGeradasModel>(storeDatabaseSettings.Value.CollectionNames.CobrancasGeradas);
        }

        internal bool CheckMongoDBConnection()
        {
            try
            {
                var pingCommand = new BsonDocument { { "ping", 1 } };
                var pingResultLog = mongoDatabase.RunCommand<BsonDocument>(pingCommand);

                return pingResultLog["ok"] == 1;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}