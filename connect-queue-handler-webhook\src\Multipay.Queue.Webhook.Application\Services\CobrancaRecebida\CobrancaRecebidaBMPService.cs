﻿using Multipay.Queue.Webhook.Application.Models.BMP;
using Multipay.Queue.Webhook.Core.Commons.Constants;
using Multipay.Queue.Webhook.Core.Interfaces;
using Multipay.Queue.Webhook.Infrastructure.DAPetapoco;
using Multipay.Queue.Webhook.Infrastructure.Models.MongoModels;
using Multipay.Queue.Webhook.Infrastructure.RabbitMq.Abstractions;
using Newtonsoft.Json;

namespace Multipay.Queue.Webhook.Application.Services.CobrancaRecebida
{
    internal class CobrancaRecebidaBMPService : CobrancaRecebidaService<CallbackRecebidoModel, CallbackRecebidoModel>
    {
        public CobrancaRecebidaBMPService(
            ICobrancaService cobrancaService,
            ICentralizadorLogsService centralizadorLogsService,
            IUnitOfWork unitOfWork,
            IInconsistenciaCobrancaService inconsistenciaCobrancaService,
            IBusNotificarCobrancaConfirmadaCliente busNotificarCliente) :
            base(cobrancaService, centralizadorLogsService, unitOfWork, busNotificarCliente, inconsistenciaCobrancaService, Bancos.BMP)
        {
        }

        protected override string SerializeParameters => JsonConvert.SerializeObject(Parameters);

        protected override IEnumerable<CallbackRecebidoModel> ListPix => new List<CallbackRecebidoModel> { Parameters! };
    }
}