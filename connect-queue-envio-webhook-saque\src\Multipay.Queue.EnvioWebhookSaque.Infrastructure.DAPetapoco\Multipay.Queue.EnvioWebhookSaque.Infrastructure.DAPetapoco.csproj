﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.6" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
		<PackageReference Include="PetaPoco.Compiled" Version="6.0.677" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Multipay.Queue.EnvioWebhookSaque.Core\Multipay.Queue.EnvioWebhookSaque.Core.csproj" />
	  <ProjectReference Include="..\Multipay.Service.Criptography\Multipay.Service.Criptography.csproj" />
	</ItemGroup>

</Project>
